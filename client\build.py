#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客服端打包脚本
使用PyInstaller将客服端打包成独立的exe文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def check_requirements():
    """检查打包所需的依赖"""
    print("[INFO] 检查打包环境...")

    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"[OK] PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("[ERROR] PyInstaller 未安装")
        print("请运行: pip install pyinstaller")
        return False

    # 检查必要的文件
    required_files = [
        "main.py",
        "user_manager.py",
        "timezone_utils.py",
        "config_utils.py",
        "requirements.txt"
    ]

    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)

    if missing_files:
        print(f"[ERROR] 缺少必要文件: {missing_files}")
        return False

    print("[OK] 打包环境检查通过")
    return True


def clean_build_dirs():
    """清理之前的打包文件"""
    print("[INFO] 清理打包目录...")

    dirs_to_clean = ["build", "__pycache__"]

    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            try:
                shutil.rmtree(dir_name)
                print(f"  已删除: {dir_name}")
            except PermissionError:
                print(f"  跳过正在使用的目录: {dir_name}")

    # 尝试清理dist目录，但如果有权限问题就跳过
    if Path("dist").exists():
        try:
            shutil.rmtree("dist")
            print("  已删除: dist")
        except PermissionError:
            print("  跳过正在使用的目录: dist (可能有程序正在运行)")

    # 清理spec文件
    for spec_file in Path(".").glob("*.spec"):
        try:
            spec_file.unlink()
            print(f"  已删除: {spec_file}")
        except PermissionError:
            print(f"  跳过正在使用的文件: {spec_file}")

    print("[OK] 清理完成")


def create_spec_file():
    """创建PyInstaller配置文件"""
    print("📝 创建PyInstaller配置文件...")

    # 检查图标文件
    icon_path = None
    if Path("resources/text_icon.ico").exists():
        icon_path = "resources/text_icon.ico"
        print(f"  找到图标文件: {icon_path}")
    else:
        print("  未找到图标文件，将使用默认图标")

    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('.env.example', '.'),
        ('utils', 'utils'),
        ('resources', 'resources'),
    ],
    hiddenimports=[
        # PyQt6 核心模块
        'PyQt6',
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'PyQt6.QtWidgets',
        'PyQt6.sip',

        # PyQt6 子模块
        'PyQt6.QtCore.pyqtSignal',
        'PyQt6.QtCore.QTimer',
        'PyQt6.QtCore.QThread',
        'PyQt6.QtCore.QSettings',
        'PyQt6.QtCore.QPropertyAnimation',
        'PyQt6.QtCore.QEasingCurve',
        'PyQt6.QtCore.QRect',
        'PyQt6.QtCore.QParallelAnimationGroup',
        'PyQt6.QtCore.QDate',
        'PyQt6.QtCore.QObject',
        'PyQt6.QtCore.Qt',

        'PyQt6.QtGui.QFont',
        'PyQt6.QtGui.QIcon',
        'PyQt6.QtGui.QPixmap',
        'PyQt6.QtGui.QAction',
        'PyQt6.QtGui.QColor',
        'PyQt6.QtGui.QPalette',

        'PyQt6.QtWidgets.QApplication',
        'PyQt6.QtWidgets.QMainWindow',
        'PyQt6.QtWidgets.QWidget',
        'PyQt6.QtWidgets.QVBoxLayout',
        'PyQt6.QtWidgets.QHBoxLayout',
        'PyQt6.QtWidgets.QSplitter',
        'PyQt6.QtWidgets.QListWidget',
        'PyQt6.QtWidgets.QTextEdit',
        'PyQt6.QtWidgets.QLineEdit',
        'PyQt6.QtWidgets.QPushButton',
        'PyQt6.QtWidgets.QLabel',
        'PyQt6.QtWidgets.QTabWidget',
        'PyQt6.QtWidgets.QTableWidget',
        'PyQt6.QtWidgets.QTableWidgetItem',
        'PyQt6.QtWidgets.QHeaderView',
        'PyQt6.QtWidgets.QMessageBox',
        'PyQt6.QtWidgets.QDialog',
        'PyQt6.QtWidgets.QFormLayout',
        'PyQt6.QtWidgets.QDialogButtonBox',
        'PyQt6.QtWidgets.QComboBox',
        'PyQt6.QtWidgets.QSpinBox',
        'PyQt6.QtWidgets.QCheckBox',
        'PyQt6.QtWidgets.QGroupBox',
        'PyQt6.QtWidgets.QScrollArea',
        'PyQt6.QtWidgets.QFrame',
        'PyQt6.QtWidgets.QProgressBar',
        'PyQt6.QtWidgets.QGraphicsDropShadowEffect',
        'PyQt6.QtWidgets.QMenu',
        'PyQt6.QtWidgets.QDateEdit',
        'PyQt6.QtWidgets.QListWidgetItem',
        'PyQt6.QtWidgets.QInputDialog',

        # 网络和异步模块
        'requests',
        'websockets',
        'asyncio',
        'aiohttp',

        # 配置和工具模块
        'dotenv',
        'json',
        'base64',
        're',

        # Windows API模块
        'win32gui',
        'win32con',
        'win32api',
        'pywin32',

        # 自定义模块
        'user_manager',
        'timezone_utils',
        'config_utils',
        'utils.file_logger',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='YF_AI_Chat_Client',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon={repr(icon_path) if icon_path else None},
)
'''
    
    with open("client.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print("✅ 配置文件创建完成: client.spec")


def build_executable():
    """执行打包"""
    print("[INFO] 开始打包...")

    try:
        # 使用更强力的PyInstaller命令，直接包含所有PyQt6模块
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed",
            "--clean",
            "--collect-all", "PyQt6",  # 强制收集所有PyQt6模块
            "--collect-all", "PyQt6.QtCore",
            "--collect-all", "PyQt6.QtGui",
            "--collect-all", "PyQt6.QtWidgets",
            "--hidden-import", "PyQt6",
            "--hidden-import", "PyQt6.sip",
            "--hidden-import", "requests",
            "--hidden-import", "websockets",
            "--hidden-import", "dotenv",
            "--hidden-import", "asyncio",
            "--hidden-import", "aiohttp",
            "--hidden-import", "user_manager",
            "--hidden-import", "timezone_utils",
            "--hidden-import", "config_utils",
            "--hidden-import", "utils.file_logger",
            "--add-data", ".env.example;.",
            "--add-data", "utils;utils",
            "--add-data", "resources;resources",
            "--name", "YF_AI_Chat_Client"
        ]

        # 添加图标
        icon_path = Path("resources/text_icon.ico")
        if icon_path.exists():
            cmd.extend(["--icon", str(icon_path)])
            print(f"  添加图标: {icon_path}")
        else:
            print("  [WARNING] 图标文件不存在，将使用默认图标")

        # 添加Windows API模块（如果可用）
        try:
            import win32gui
            cmd.extend([
                "--hidden-import", "win32gui",
                "--hidden-import", "win32con",
                "--hidden-import", "win32api"
            ])
            print("  添加Windows API支持")
        except ImportError:
            print("  跳过Windows API模块")

        cmd.append("main.py")

        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("[OK] 打包成功!")
            return True
        else:
            print("[ERROR] 打包失败!")
            print("错误输出:")
            print(result.stderr)
            if result.stdout:
                print("标准输出:")
                print(result.stdout)
            return False

    except Exception as e:
        print(f"[ERROR] 打包过程中出现异常: {e}")
        return False


def copy_config_files():
    """复制配置文件到dist目录"""
    print("[INFO] 复制配置文件...")

    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("[ERROR] dist目录不存在")
        return False

    # 要复制的配置文件
    config_files = [
        (".env.example", ".env"),  # 复制.env.example为.env
        ("mail.json", "mail.json"),
        ("users.json", "users.json"),
    ]

    for src, dst in config_files:
        src_path = Path(src)
        dst_path = dist_dir / dst

        if src_path.exists():
            shutil.copy2(src_path, dst_path)
            print(f"  已复制: {src} -> {dst_path}")
        else:
            print(f"  跳过不存在的文件: {src}")

    # 复制resources目录（确保图标文件可用）
    resources_src = Path("resources")
    resources_dst = dist_dir / "resources"

    if resources_src.exists():
        if resources_dst.exists():
            shutil.rmtree(resources_dst)
        shutil.copytree(resources_src, resources_dst)
        print(f"  已复制: {resources_src} -> {resources_dst}")
    else:
        print("  [WARNING] resources目录不存在")

    print("[OK] 配置文件复制完成")
    return True


def create_readme():
    """创建使用说明文件"""
    print("[INFO] 创建使用说明...")
    
    readme_content = """# YF AI Chat 客服端

## 使用说明

1. **首次运行**
   - 双击 `YF_AI_Chat_Client.exe` 启动程序
   - 程序会自动在exe所在目录创建配置文件

2. **配置文件说明**
   - `.env`: 主要配置文件，包含服务器地址、Token等
   - `mail.json`: 邮箱配置文件
   - `users.json`: 用户账户配置文件

3. **配置服务器连接**
   - 编辑 `.env` 文件
   - 修改 `SERVER_HOST` 为您的服务器地址
   - 修改 `ADMIN_TOKEN` 为从后端获取的管理员Token
   - 根据连接方式设置 `USE_HTTPS`

4. **常见配置示例**
   ```
   # 本地开发
   SERVER_HOST=localhost
   USE_HTTPS=false
   
   # 局域网连接
   SERVER_HOST=*************
   USE_HTTPS=false
   
   # 远程连接
   SERVER_HOST=chat.22668.xyz
   USE_HTTPS=true
   ```

5. **故障排除**
   - 如果无法连接服务器，请检查 `.env` 文件中的配置
   - 确保 `ADMIN_TOKEN` 与后端配置一致
   - 检查网络连接和防火墙设置

## 技术支持

如有问题，请联系技术支持团队。
"""
    
    dist_dir = Path("dist")
    readme_path = dist_dir / "README.txt"
    
    with open(readme_path, "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print(f"[OK] 使用说明创建完成: {readme_path}")


def main():
    """主函数"""
    print("[INFO] YF AI Chat 客服端打包工具")
    print("=" * 50)

    # 检查环境
    if not check_requirements():
        return False

    # 清理旧文件
    clean_build_dirs()

    # 直接执行打包（不使用spec文件）
    if not build_executable():
        return False

    # 复制配置文件
    if not copy_config_files():
        return False

    # 创建说明文件
    create_readme()

    print("\n[SUCCESS] 打包完成!")
    print("=" * 50)
    print("[INFO] 输出目录: dist/")
    print("[INFO] 可执行文件: dist/YF_AI_Chat_Client.exe")
    print("[INFO] 配置文件已复制到dist目录")
    print("[INFO] 使用说明: dist/README.txt")

    return True


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
