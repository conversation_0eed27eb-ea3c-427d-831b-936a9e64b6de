#!/usr/bin/env python3
"""
统一产品管理组件
合并产品目录、产品数据和购买渠道
支持四个产品：AP2000WF, APH3000, PD22SC, D032
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QTextEdit, QComboBox, QGroupBox,
                            QFormLayout, QScrollArea, QListWidget, QListWidgetItem,
                            QPushButton, QInputDialog, QMessageBox, QCheckBox,
                            QTreeWidget, QTreeWidgetItem, QSplitter, QTabWidget,
                            QSpinBox, QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt6.QtCore import pyqtSignal, Qt
from PyQt6.QtGui import QFont


class UnifiedProductWidget(QWidget):
    """统一产品管理组件"""
    
    config_changed = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.current_product = None
        self.products_data = {}
        self.init_ui()
        self.load_default_products()
    
    def init_ui(self):
        """初始化界面"""
        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 主容器
        main_widget = QWidget()
        layout = QVBoxLayout(main_widget)
        
        # 说明信息
        help_label = QLabel("""
💡 产品管理说明:
• 每个产品包含：基本信息、购买渠道、竞品对比、认证证书、客户数据
• 每个产品在不同平台(Amazon, Shopify, 官网)都有独立的ASIN和链接
• 所有信息将用于AI销售助手的产品推荐和异议处理
        """)
        help_label.setStyleSheet("color: #666; font-size: 11px; background: #f5f5f5; padding: 10px; border-radius: 5px;")
        layout.addWidget(help_label)
        
        # 创建主分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：产品列表
        products_group = QGroupBox("📦 产品列表")
        products_layout = QVBoxLayout(products_group)
        
        self.products_list = QListWidget()
        self.products_list.currentItemChanged.connect(self.on_product_selected)
        products_layout.addWidget(self.products_list)
        
        # 产品操作按钮
        product_btn_layout = QHBoxLayout()
        self.add_product_btn = QPushButton("➕ 添加产品")
        self.add_product_btn.clicked.connect(self.add_product)
        product_btn_layout.addWidget(self.add_product_btn)
        
        self.remove_product_btn = QPushButton("➖ 删除产品")
        self.remove_product_btn.clicked.connect(self.remove_product)
        product_btn_layout.addWidget(self.remove_product_btn)
        
        product_btn_layout.addStretch()
        products_layout.addLayout(product_btn_layout)
        
        main_splitter.addWidget(products_group)
        
        # 右侧：产品详情标签页
        self.product_tabs = QTabWidget()
        
        # 基本信息标签页
        basic_tab = self.create_basic_info_tab()
        self.product_tabs.addTab(basic_tab, "📋 基本信息")
        
        # 购买渠道标签页
        channels_tab = self.create_purchase_channels_tab()
        self.product_tabs.addTab(channels_tab, "🔗 购买渠道")
        
        # 竞品对比标签页
        comparison_tab = self.create_competitor_comparison_tab()
        self.product_tabs.addTab(comparison_tab, "🏆 竞品对比")
        
        # 认证证书标签页
        certifications_tab = self.create_certifications_tab()
        self.product_tabs.addTab(certifications_tab, "🏅 认证证书")
        
        # 客户数据标签页
        customer_tab = self.create_customer_data_tab()
        self.product_tabs.addTab(customer_tab, "⭐ 客户数据")
        
        main_splitter.addWidget(self.product_tabs)
        main_splitter.setSizes([300, 900])
        
        layout.addWidget(main_splitter)
        
        # 设置滚动区域
        scroll.setWidget(main_widget)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(scroll)
    
    def create_basic_info_tab(self):
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 基本产品信息
        basic_group = QGroupBox("📋 基本产品信息")
        basic_layout = QFormLayout(basic_group)
        
        self.product_id_edit = QLineEdit()
        self.product_id_edit.textChanged.connect(self.on_product_data_changed)
        basic_layout.addRow("产品ID:", self.product_id_edit)
        
        self.product_name_edit = QLineEdit()
        self.product_name_edit.textChanged.connect(self.on_product_data_changed)
        basic_layout.addRow("产品名称:", self.product_name_edit)
        
        self.product_category_combo = QComboBox()
        self.product_category_combo.addItems(["Air Purifier", "Dehumidifier", "Humidifier", "Air Quality Monitor"])
        self.product_category_combo.currentTextChanged.connect(self.on_product_data_changed)
        basic_layout.addRow("产品类别:", self.product_category_combo)
        
        self.product_price_edit = QLineEdit()
        self.product_price_edit.textChanged.connect(self.on_product_data_changed)
        basic_layout.addRow("价格:", self.product_price_edit)
        
        self.product_description_edit = QTextEdit()
        self.product_description_edit.setMaximumHeight(80)
        self.product_description_edit.textChanged.connect(self.on_product_data_changed)
        basic_layout.addRow("产品描述:", self.product_description_edit)
        
        layout.addWidget(basic_group)
        
        # 技术规格
        tech_group = QGroupBox("⚙️ 技术规格")
        tech_layout = QFormLayout(tech_group)
        
        self.cadr_rating_edit = QLineEdit()
        self.cadr_rating_edit.textChanged.connect(self.on_product_data_changed)
        tech_layout.addRow("CADR评级:", self.cadr_rating_edit)
        
        self.coverage_area_edit = QLineEdit()
        self.coverage_area_edit.textChanged.connect(self.on_product_data_changed)
        tech_layout.addRow("覆盖面积:", self.coverage_area_edit)
        
        self.filter_type_edit = QLineEdit()
        self.filter_type_edit.textChanged.connect(self.on_product_data_changed)
        tech_layout.addRow("过滤类型:", self.filter_type_edit)
        
        self.noise_level_edit = QLineEdit()
        self.noise_level_edit.textChanged.connect(self.on_product_data_changed)
        tech_layout.addRow("噪音水平:", self.noise_level_edit)
        
        self.power_consumption_edit = QLineEdit()
        self.power_consumption_edit.textChanged.connect(self.on_product_data_changed)
        tech_layout.addRow("功耗:", self.power_consumption_edit)
        
        self.dimensions_edit = QLineEdit()
        self.dimensions_edit.textChanged.connect(self.on_product_data_changed)
        tech_layout.addRow("尺寸:", self.dimensions_edit)
        
        self.weight_edit = QLineEdit()
        self.weight_edit.textChanged.connect(self.on_product_data_changed)
        tech_layout.addRow("重量:", self.weight_edit)
        
        self.warranty_edit = QLineEdit()
        self.warranty_edit.textChanged.connect(self.on_product_data_changed)
        tech_layout.addRow("保修期:", self.warranty_edit)
        
        layout.addWidget(tech_group)
        
        return widget
    
    def create_purchase_channels_tab(self):
        """创建购买渠道标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 购买渠道表格
        channels_group = QGroupBox("🔗 购买渠道配置")
        channels_layout = QVBoxLayout(channels_group)
        
        # 渠道表格
        self.channels_table = QTableWidget()
        self.channels_table.setColumnCount(5)
        self.channels_table.setHorizontalHeaderLabels(["平台", "产品ID/ASIN", "产品链接", "价格", "特殊说明"])
        self.channels_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        channels_layout.addWidget(self.channels_table)
        
        # 渠道操作按钮
        channels_btn_layout = QHBoxLayout()
        self.add_channel_btn = QPushButton("➕ 添加渠道")
        self.add_channel_btn.clicked.connect(self.add_purchase_channel)
        channels_btn_layout.addWidget(self.add_channel_btn)
        
        self.remove_channel_btn = QPushButton("➖ 删除渠道")
        self.remove_channel_btn.clicked.connect(self.remove_purchase_channel)
        channels_btn_layout.addWidget(self.remove_channel_btn)
        
        channels_btn_layout.addStretch()
        channels_layout.addLayout(channels_btn_layout)
        
        layout.addWidget(channels_group)
        
        return widget
    
    def create_competitor_comparison_tab(self):
        """创建竞品对比标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 竞品对比表格
        comparison_group = QGroupBox("🏆 竞品对比")
        comparison_layout = QVBoxLayout(comparison_group)
        
        # 对比表格
        self.comparison_table = QTableWidget()
        self.comparison_table.setColumnCount(4)
        self.comparison_table.setHorizontalHeaderLabels(["规格", "我们的产品", "竞品A", "竞品B"])
        self.comparison_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        comparison_layout.addWidget(self.comparison_table)
        
        # 对比操作按钮
        comparison_btn_layout = QHBoxLayout()
        self.add_comparison_btn = QPushButton("➕ 添加对比项")
        self.add_comparison_btn.clicked.connect(self.add_comparison_row)
        comparison_btn_layout.addWidget(self.add_comparison_btn)
        
        self.remove_comparison_btn = QPushButton("➖ 删除对比项")
        self.remove_comparison_btn.clicked.connect(self.remove_comparison_row)
        comparison_btn_layout.addWidget(self.remove_comparison_btn)
        
        comparison_btn_layout.addStretch()
        comparison_layout.addLayout(comparison_btn_layout)
        
        layout.addWidget(comparison_group)
        
        return widget
    
    def create_certifications_tab(self):
        """创建认证证书标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 认证列表
        cert_group = QGroupBox("🏅 产品认证")
        cert_layout = QVBoxLayout(cert_group)
        
        self.certifications_list = QListWidget()
        cert_layout.addWidget(self.certifications_list)
        
        cert_btn_layout = QHBoxLayout()
        self.add_cert_btn = QPushButton("➕ 添加认证")
        self.add_cert_btn.clicked.connect(self.add_certification)
        cert_btn_layout.addWidget(self.add_cert_btn)
        
        self.remove_cert_btn = QPushButton("➖ 删除认证")
        self.remove_cert_btn.clicked.connect(self.remove_certification)
        cert_btn_layout.addWidget(self.remove_cert_btn)
        
        cert_btn_layout.addStretch()
        cert_layout.addLayout(cert_btn_layout)
        
        layout.addWidget(cert_group)
        
        return widget
    
    def create_customer_data_tab(self):
        """创建客户数据标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 评价统计
        rating_group = QGroupBox("⭐ 客户评价统计")
        rating_layout = QFormLayout(rating_group)
        
        self.customer_rating_edit = QLineEdit()
        self.customer_rating_edit.textChanged.connect(self.on_product_data_changed)
        rating_layout.addRow("平均评分:", self.customer_rating_edit)
        
        self.review_count_edit = QLineEdit()
        self.review_count_edit.textChanged.connect(self.on_product_data_changed)
        rating_layout.addRow("评价数量:", self.review_count_edit)
        
        self.satisfaction_rate_edit = QLineEdit()
        self.satisfaction_rate_edit.textChanged.connect(self.on_product_data_changed)
        rating_layout.addRow("满意度:", self.satisfaction_rate_edit)
        
        layout.addWidget(rating_group)
        
        # 客户好评要点
        praise_group = QGroupBox("👍 客户好评要点")
        praise_layout = QVBoxLayout(praise_group)
        
        self.praise_points_list = QListWidget()
        praise_layout.addWidget(self.praise_points_list)
        
        praise_btn_layout = QHBoxLayout()
        self.add_praise_btn = QPushButton("➕ 添加好评点")
        self.add_praise_btn.clicked.connect(self.add_praise_point)
        praise_btn_layout.addWidget(self.add_praise_btn)
        
        self.remove_praise_btn = QPushButton("➖ 删除好评点")
        self.remove_praise_btn.clicked.connect(self.remove_praise_point)
        praise_btn_layout.addWidget(self.remove_praise_btn)
        
        praise_btn_layout.addStretch()
        praise_layout.addLayout(praise_btn_layout)
        
        layout.addWidget(praise_group)

        return widget

    def load_default_products(self):
        """加载默认产品（已移除硬编码，现在从配置文件读取）"""
        # 注意：硬编码的产品数据已移除
        # 产品数据现在从统一配置文件 advanced_sales_config.json 中读取
        # 如果没有配置文件，显示空的产品列表
        # 硬编码的产品数据已移除
        # 产品数据现在从统一配置文件中读取
        default_products = {
            # 空的默认产品数据
            # 实际产品信息将通过 load_products_data() 方法从配置文件加载
        }

        # 加载产品到列表
        for product_id, product_data in default_products.items():
            self.products_data[product_id] = product_data
            item = QListWidgetItem(f"{product_id} - {product_data['name']}")
            item.setData(Qt.ItemDataRole.UserRole, product_id)
            self.products_list.addItem(item)

        # 默认选择第一个产品
        if self.products_list.count() > 0:
            self.products_list.setCurrentRow(0)

    def on_product_selected(self, current, previous):
        """选择产品时的处理"""
        if current:
            product_id = current.data(Qt.ItemDataRole.UserRole)
            if product_id in self.products_data:
                self.current_product = product_id
                self.load_product_data(self.products_data[product_id])

    def load_product_data(self, product_data):
        """加载产品数据到界面"""
        # 基本信息
        self.product_id_edit.setText(product_data.get('id', ''))
        self.product_name_edit.setText(product_data.get('name', ''))
        self.product_price_edit.setText(product_data.get('price', ''))
        self.product_description_edit.setPlainText(product_data.get('description', ''))

        # 设置类别
        category = product_data.get('category', 'Air Purifier')
        index = self.product_category_combo.findText(category)
        if index >= 0:
            self.product_category_combo.setCurrentIndex(index)

        # 技术规格
        self.cadr_rating_edit.setText(product_data.get('cadr_rating', ''))
        self.coverage_area_edit.setText(product_data.get('coverage_area', ''))
        self.filter_type_edit.setText(product_data.get('filter_type', ''))
        self.noise_level_edit.setText(product_data.get('noise_level', ''))
        self.power_consumption_edit.setText(product_data.get('power_consumption', ''))
        self.dimensions_edit.setText(product_data.get('dimensions', ''))
        self.weight_edit.setText(product_data.get('weight', ''))
        self.warranty_edit.setText(product_data.get('warranty', ''))

        # 客户数据
        self.customer_rating_edit.setText(product_data.get('rating', ''))
        self.review_count_edit.setText(product_data.get('review_count', ''))
        self.satisfaction_rate_edit.setText(product_data.get('satisfaction_rate', ''))

        # 加载购买渠道
        self.load_channels_data(product_data.get('channels', []))

        # 加载认证
        self.load_certifications_data(product_data.get('certifications', []))

        # 加载好评要点
        self.load_praise_points_data(product_data.get('praise_points', []))

    def load_channels_data(self, channels_data):
        """加载购买渠道数据"""
        self.channels_table.setRowCount(len(channels_data))

        for row, channel in enumerate(channels_data):
            for col, value in enumerate(channel):
                item = QTableWidgetItem(str(value))
                self.channels_table.setItem(row, col, item)

    def load_certifications_data(self, certifications):
        """加载认证数据"""
        self.certifications_list.clear()
        for cert in certifications:
            self.certifications_list.addItem(cert)

    def load_praise_points_data(self, praise_points):
        """加载好评要点数据"""
        self.praise_points_list.clear()
        for point in praise_points:
            self.praise_points_list.addItem(point)

    # 添加/删除方法
    def add_product(self):
        """添加产品"""
        text, ok = QInputDialog.getText(self, "添加产品", "请输入产品ID:")
        if ok and text:
            if text not in self.products_data:
                # 创建新产品数据
                new_product = {
                    "id": text,
                    "name": f"New Product {text}",
                    "category": "Air Purifier",
                    "price": "$0",
                    "description": "",
                    "channels": [],
                    "certifications": [],
                    "praise_points": []
                }

                self.products_data[text] = new_product
                item = QListWidgetItem(f"{text} - {new_product['name']}")
                item.setData(Qt.ItemDataRole.UserRole, text)
                self.products_list.addItem(item)
                self.products_list.setCurrentItem(item)
                self.config_changed.emit()
            else:
                QMessageBox.warning(self, "警告", "产品ID已存在！")

    def remove_product(self):
        """删除产品"""
        current_item = self.products_list.currentItem()
        if current_item:
            product_id = current_item.data(Qt.ItemDataRole.UserRole)
            reply = QMessageBox.question(
                self, "确认删除", f"确定要删除产品 {product_id} 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                # 从数据中删除
                if product_id in self.products_data:
                    del self.products_data[product_id]

                # 从列表中删除
                row = self.products_list.row(current_item)
                self.products_list.takeItem(row)
                self.config_changed.emit()

    def add_purchase_channel(self):
        """添加购买渠道"""
        row_count = self.channels_table.rowCount()
        self.channels_table.insertRow(row_count)

        # 设置默认值
        self.channels_table.setItem(row_count, 0, QTableWidgetItem("New Platform"))
        self.channels_table.setItem(row_count, 1, QTableWidgetItem("Product ID"))
        self.channels_table.setItem(row_count, 2, QTableWidgetItem("https://"))
        self.channels_table.setItem(row_count, 3, QTableWidgetItem("$0"))
        self.channels_table.setItem(row_count, 4, QTableWidgetItem("Notes"))

        self.save_current_product_data()

    def remove_purchase_channel(self):
        """删除购买渠道"""
        current_row = self.channels_table.currentRow()
        if current_row >= 0:
            self.channels_table.removeRow(current_row)
            self.save_current_product_data()

    def add_comparison_row(self):
        """添加对比行"""
        row_count = self.comparison_table.rowCount()
        self.comparison_table.insertRow(row_count)

        # 设置默认值
        self.comparison_table.setItem(row_count, 0, QTableWidgetItem("New Spec"))
        self.comparison_table.setItem(row_count, 1, QTableWidgetItem("Our Value"))
        self.comparison_table.setItem(row_count, 2, QTableWidgetItem("Competitor A"))
        self.comparison_table.setItem(row_count, 3, QTableWidgetItem("Competitor B"))

    def remove_comparison_row(self):
        """删除对比行"""
        current_row = self.comparison_table.currentRow()
        if current_row >= 0:
            self.comparison_table.removeRow(current_row)

    def add_certification(self):
        """添加认证"""
        text, ok = QInputDialog.getText(self, "添加认证", "请输入认证名称:")
        if ok and text:
            self.certifications_list.addItem(text)
            self.save_current_product_data()

    def remove_certification(self):
        """删除认证"""
        current_row = self.certifications_list.currentRow()
        if current_row >= 0:
            self.certifications_list.takeItem(current_row)
            self.save_current_product_data()

    def add_praise_point(self):
        """添加好评点"""
        text, ok = QInputDialog.getText(self, "添加好评点", "请输入好评要点:")
        if ok and text:
            self.praise_points_list.addItem(text)
            self.save_current_product_data()

    def remove_praise_point(self):
        """删除好评点"""
        current_row = self.praise_points_list.currentRow()
        if current_row >= 0:
            self.praise_points_list.takeItem(current_row)
            self.save_current_product_data()

    def on_product_data_changed(self):
        """产品数据改变时的处理"""
        self.save_current_product_data()
        self.config_changed.emit()

    def save_current_product_data(self):
        """保存当前产品数据"""
        if not self.current_product:
            return

        if self.current_product in self.products_data:
            product_data = self.products_data[self.current_product]

            # 保存基本信息
            product_data['id'] = self.product_id_edit.text()
            product_data['name'] = self.product_name_edit.text()
            product_data['category'] = self.product_category_combo.currentText()
            product_data['price'] = self.product_price_edit.text()
            product_data['description'] = self.product_description_edit.toPlainText()

            # 保存技术规格
            product_data['cadr_rating'] = self.cadr_rating_edit.text()
            product_data['coverage_area'] = self.coverage_area_edit.text()
            product_data['filter_type'] = self.filter_type_edit.text()
            product_data['noise_level'] = self.noise_level_edit.text()
            product_data['power_consumption'] = self.power_consumption_edit.text()
            product_data['dimensions'] = self.dimensions_edit.text()
            product_data['weight'] = self.weight_edit.text()
            product_data['warranty'] = self.warranty_edit.text()

            # 保存客户数据
            product_data['rating'] = self.customer_rating_edit.text()
            product_data['review_count'] = self.review_count_edit.text()
            product_data['satisfaction_rate'] = self.satisfaction_rate_edit.text()

            # 保存购买渠道
            channels = []
            for row in range(self.channels_table.rowCount()):
                channel = []
                for col in range(self.channels_table.columnCount()):
                    item = self.channels_table.item(row, col)
                    channel.append(item.text() if item else "")
                channels.append(channel)
            product_data['channels'] = channels

            # 保存认证
            certifications = []
            for i in range(self.certifications_list.count()):
                certifications.append(self.certifications_list.item(i).text())
            product_data['certifications'] = certifications

            # 保存好评要点
            praise_points = []
            for i in range(self.praise_points_list.count()):
                praise_points.append(self.praise_points_list.item(i).text())
            product_data['praise_points'] = praise_points

    def get_products_data(self):
        """获取所有产品数据"""
        self.save_current_product_data()
        return self.products_data

    def get_current_product_data(self):
        """获取当前选中的产品数据"""
        current_item = self.products_list.currentItem()
        if current_item:
            product_id = current_item.data(Qt.ItemDataRole.UserRole)
            if product_id in self.products_data:
                # 先保存当前编辑的数据
                self.save_current_product_data()
                return self.products_data[product_id]
        return {}

    def load_products_data(self, products_data):
        """加载产品数据（支持新的配置格式）"""
        print(f"🔍 开始加载产品数据，数据类型: {type(products_data)}")
        if products_data:
            print(f"📊 产品数据包含 {len(products_data)} 个产品: {list(products_data.keys())}")
            # 如果是新的配置格式（包含basic_info等子结构）
            if isinstance(products_data, dict) and any('basic_info' in v for v in products_data.values() if isinstance(v, dict)):
                print("🔄 检测到新配置格式，开始转换...")
                # 转换新格式到内部格式
                converted_data = {}
                for product_id, product_config in products_data.items():
                    if isinstance(product_config, dict):
                        basic_info = product_config.get('basic_info', {})
                        tech_specs = product_config.get('technical_specs', {})
                        customer_data = product_config.get('customer_data', {})
                        channels = product_config.get('purchase_channels', {})

                        # 转换购买渠道格式
                        channels_list = []
                        for channel_name, channel_info in channels.items():
                            if isinstance(channel_info, dict):
                                # 获取渠道标识符（优先级：asin > product_id > store_id）
                                identifier = (channel_info.get('asin', '') or
                                            channel_info.get('product_id', '') or
                                            channel_info.get('store_id', ''))

                                channels_list.append([
                                    channel_name.replace('_', ' ').title(),
                                    identifier,
                                    channel_info.get('url', ''),
                                    channel_info.get('price', ''),
                                    ', '.join(channel_info.get('benefits', []))
                                ])

                        # 合并认证信息（从技术规格和产品配置中）
                        certifications = []
                        if 'certifications' in tech_specs:
                            certifications.extend(tech_specs['certifications'])
                        if 'certifications' in product_config:
                            certifications.extend(product_config['certifications'])
                        # 去重
                        certifications = list(set(certifications))

                        converted_data[product_id] = {
                            'id': basic_info.get('id', product_id),
                            'name': basic_info.get('name', ''),
                            'category': basic_info.get('category', ''),
                            'price': basic_info.get('price', ''),
                            'description': basic_info.get('description', ''),
                            'cadr_rating': tech_specs.get('efficiency', tech_specs.get('capacity', '')),
                            'coverage_area': tech_specs.get('coverage_area', ''),
                            'filter_type': tech_specs.get('filtration', ''),
                            'noise_level': tech_specs.get('noise_level', ''),
                            'power_consumption': tech_specs.get('power_consumption', ''),
                            'dimensions': tech_specs.get('dimensions', ''),
                            'weight': tech_specs.get('weight', ''),
                            'warranty': tech_specs.get('warranty', ''),
                            'rating': customer_data.get('rating', ''),
                            'review_count': customer_data.get('review_count', ''),
                            'satisfaction_rate': customer_data.get('satisfaction_rate', ''),
                            'channels': channels_list,
                            'certifications': certifications,
                            'praise_points': customer_data.get('praise_points', [])
                        }

                self.products_data = converted_data
                print(f"✅ 新格式转换完成，转换了 {len(converted_data)} 个产品")
            else:
                # 旧格式或已转换格式
                print("📋 使用现有格式，无需转换")
                self.products_data = products_data

            # 重新加载产品列表
            self.products_list.clear()
            for product_id, product_data in self.products_data.items():
                name = product_data.get('name', 'Unknown')
                item = QListWidgetItem(f"{product_id} - {name}")
                item.setData(Qt.ItemDataRole.UserRole, product_id)
                self.products_list.addItem(item)

            # 默认选择第一个产品
            if self.products_list.count() > 0:
                self.products_list.setCurrentRow(0)
