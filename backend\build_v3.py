#!/usr/bin/env python3
"""
YF AI Chat Backend v3 打包脚本
高性能版本打包，包含完整的电源管理和性能优化功能
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path
from datetime import datetime

def print_banner():
    """打印横幅"""
    print("=" * 80)
    print("🚀 YF AI Chat Backend v3 打包工具")
    print("   高性能版本 - 包含电源管理和性能优化")
    print("=" * 80)

def check_requirements():
    """检查打包环境"""
    print("🔍 检查打包环境...")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller 未安装")
        print("请运行: pip install pyinstaller")
        return False
    
    # 检查必要的依赖
    required_packages = [
        ('fastapi', 'fastapi'),
        ('uvicorn', 'uvicorn'),
        ('pydantic', 'pydantic'),
        ('aiosqlite', 'aiosqlite'),
        ('httpx', 'httpx'),
        ('python-dotenv', 'dotenv'),
        ('psutil', 'psutil')
    ]

    missing_packages = []
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name}")
        except ImportError:
            missing_packages.append(package_name)
            print(f"❌ {package_name}")
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def clean_previous_builds():
    """清理之前的构建文件"""
    print("🧹 清理之前的构建文件...")
    
    current_dir = Path(__file__).parent
    
    # 清理目录
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        dir_path = current_dir / dir_name
        if dir_path.exists():
            try:
                shutil.rmtree(dir_path)
                print(f"✅ 已清理: {dir_name}")
            except Exception as e:
                print(f"⚠️ 清理 {dir_name} 失败: {e}")
    
    # 清理.pyc文件
    for root, dirs, files in os.walk(current_dir):
        for file in files:
            if file.endswith('.pyc'):
                try:
                    os.remove(os.path.join(root, file))
                except:
                    pass

def create_hook_file():
    """创建Pydantic hook文件"""
    print("📝 创建Pydantic hook文件...")
    
    hook_content = '''"""
PyInstaller hook for pydantic
"""
from PyInstaller.utils.hooks import collect_all

datas, binaries, hiddenimports = collect_all('pydantic')

# 添加额外的隐藏导入
hiddenimports += [
    'pydantic.fields',
    'pydantic.main',
    'pydantic.types',
    'pydantic.validators',
    'pydantic.json',
    'pydantic.parse',
    'pydantic.schema',
    'pydantic.utils',
    'pydantic.error_wrappers',
    'pydantic.env_settings',
    'pydantic_settings',
    'pydantic.v1',
    'pydantic.v1.fields',
    'pydantic.v1.main',
    'pydantic.v1.types',
    'pydantic.v1.validators',
]
'''
    
    hook_file = Path(__file__).parent / 'hook-pydantic.py'
    with open(hook_file, 'w', encoding='utf-8') as f:
        f.write(hook_content)
    
    print("✅ Pydantic hook文件已创建")

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")
    
    current_dir = Path(__file__).parent
    spec_file = current_dir / 'YF_AI_Chat_Backend_v3.spec'
    
    if not spec_file.exists():
        print(f"❌ 规格文件不存在: {spec_file}")
        return False
    
    # 构建命令
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',  # 清理临时文件
        '--noconfirm',  # 不询问覆盖
        str(spec_file)
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 执行构建
        start_time = time.time()
        result = subprocess.run(cmd, cwd=current_dir, capture_output=True, text=True)
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✅ 构建成功! 耗时: {end_time - start_time:.1f}秒")
            return True
        else:
            print("❌ 构建失败!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def create_portable_version():
    """创建便携版"""
    print("📦 创建便携版...")
    
    current_dir = Path(__file__).parent
    dist_dir = current_dir / 'dist'
    exe_file = dist_dir / 'YF_AI_Chat_Backend_v3.exe'
    
    if not exe_file.exists():
        print("❌ 可执行文件不存在，无法创建便携版")
        return False
    
    # 创建便携版目录
    portable_dir = dist_dir / 'YF_AI_Chat_Backend_v3_便携版'
    portable_dir.mkdir(exist_ok=True)
    
    # 复制可执行文件
    shutil.copy2(exe_file, portable_dir / 'YF_AI_Chat_Backend_v3.exe')
    
    # 创建配置文件模板
    env_template = '''# YF AI Chat Backend v3 配置文件
# 请根据需要修改以下配置

# 服务器配置
HOST=0.0.0.0
PORT=8000

# HTTPS配置 (disabled/direct/reverse_proxy)
HTTPS_MODE=disabled

# Token配置
WORDPRESS_TOKEN=your_wordpress_token_here
ADMIN_TOKEN=your_admin_token_here
API_TOKEN=your_api_token_here

# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# 管理员超时配置
ADMIN_CONTROL_TIMEOUT_MINUTES=15
ADMIN_TIMEOUT_CHECK_INTERVAL_MINUTES=1

# 日志级别 (DEBUG/INFO/WARNING/ERROR)
LOG_LEVEL=INFO
'''
    
    with open(portable_dir / '.env.template', 'w', encoding='utf-8') as f:
        f.write(env_template)
    
    # 创建使用说明
    readme_content = '''# YF AI Chat Backend v3 使用说明

## 🚀 快速开始

1. 复制 `.env.template` 为 `.env`
2. 编辑 `.env` 文件，填入你的配置信息
3. 双击 `YF_AI_Chat_Backend_v3.exe` 启动服务

## ⚡ 高性能特性

- 自动切换到Windows高性能电源计划
- 防止系统进入效能模式
- 进程高优先级运行
- 优化的网络连接参数

## 🔧 配置说明

### 必需配置
- `OPENAI_API_KEY`: OpenAI API密钥
- `WORDPRESS_TOKEN`: WordPress插件访问令牌
- `ADMIN_TOKEN`: 管理员访问令牌

### 可选配置
- `HOST`: 服务器监听地址 (默认: 0.0.0.0)
- `PORT`: 服务器端口 (默认: 8000)
- `HTTPS_MODE`: HTTPS模式 (默认: disabled)

## 📝 日志文件

日志文件位于 `logs/` 目录下，包含详细的运行信息。

## 🛠️ 故障排除

1. 如果启动失败，请检查 `.env` 配置
2. 查看日志文件获取详细错误信息
3. 确保防火墙允许程序访问网络
4. 建议以管理员身份运行以获得最佳性能

## 📞 技术支持

如有问题，请查看日志文件或联系技术支持。

版本: v3.0
构建时间: ''' + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + '''
'''
    
    with open(portable_dir / '使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ 便携版已创建: {portable_dir}")
    return True

def show_build_info():
    """显示构建信息"""
    print("\n" + "=" * 80)
    print("📊 构建完成信息")
    print("=" * 80)
    
    current_dir = Path(__file__).parent
    dist_dir = current_dir / 'dist'
    
    if dist_dir.exists():
        exe_file = dist_dir / 'YF_AI_Chat_Backend_v3.exe'
        portable_dir = dist_dir / 'YF_AI_Chat_Backend_v3_便携版'
        
        if exe_file.exists():
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            print(f"📁 可执行文件: {exe_file}")
            print(f"📏 文件大小: {size_mb:.1f} MB")
        
        if portable_dir.exists():
            print(f"📦 便携版目录: {portable_dir}")
        
        print(f"🕒 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("\n🎉 打包完成! 可以开始使用了。")
    else:
        print("❌ 构建目录不存在")

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_requirements():
        sys.exit(1)
    
    # 清理之前的构建
    clean_previous_builds()
    
    # 创建hook文件
    create_hook_file()
    
    # 构建可执行文件
    if not build_executable():
        sys.exit(1)
    
    # 创建便携版
    create_portable_version()
    
    # 显示构建信息
    show_build_info()

if __name__ == "__main__":
    main()
