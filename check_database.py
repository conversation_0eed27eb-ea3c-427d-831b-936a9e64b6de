import sqlite3
import json
from datetime import datetime

# 连接数据库
conn = sqlite3.connect('backend/chat_data.db')
cursor = conn.cursor()

# 查看所有表
cursor.execute('SELECT name FROM sqlite_master WHERE type="table"')
tables = cursor.fetchall()
print('=== 数据库中的表 ===')
for table in tables:
    print(f'表名: {table[0]}')

# 查看form_submissions表结构
print('\n=== form_submissions表结构 ===')
try:
    cursor.execute('PRAGMA table_info(form_submissions)')
    columns = cursor.fetchall()
    for col in columns:
        print(f'{col[1]} ({col[2]}) - 主键:{col[5]} 非空:{col[3]} 默认值:{col[4]}')
    
    # 查询最新记录
    print('\n=== 最新的表单提交记录 ===')
    cursor.execute('SELECT * FROM form_submissions ORDER BY submitted_at DESC LIMIT 3')
    rows = cursor.fetchall()
    
    # 获取列名
    column_names = [description[0] for description in cursor.description]
    print(f'列名: {column_names}')
    
    for i, row in enumerate(rows, 1):
        print(f'\n记录 {i}:')
        for col, val in zip(column_names, row):
            if col == 'form_data' and val:
                try:
                    form_data = json.loads(val)
                    print(f'  {col}: {json.dumps(form_data, indent=4, ensure_ascii=False)}')
                except:
                    print(f'  {col}: {val}')
            else:
                print(f'  {col}: {val}')
        print('-' * 50)

except Exception as e:
    print(f'查询form_submissions表时出错: {e}')

conn.close()
