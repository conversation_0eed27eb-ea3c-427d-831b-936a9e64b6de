#!/usr/bin/env python3
"""
客服端用户管理系统
支持多用户账户管理
"""

import os
import json
import hashlib
from pathlib import Path
from typing import Dict, List, Optional
from config_utils import get_config_path

class UserManager:
    def __init__(self, users_file=None):
        if users_file is None:
            # 使用exe所在目录的users.json
            self.users_file = get_config_path('users.json')
        else:
            self.users_file = Path(users_file)
        self.users = self.load_users()
    
    def load_users(self) -> Dict:
        """加载用户数据"""
        if self.users_file.exists():
            try:
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading users: {e}")
        
        # 返回默认用户
        return self.get_default_users()
    
    def get_default_users(self) -> Dict:
        """获取默认用户配置"""
        return {
            "users": {
                "admin": {
                    "password_hash": self.hash_password("admin"),
                    "role": "admin",
                    "name": "系统管理员",
                    "created_at": "2024-01-01T00:00:00",
                    "active": True
                }
            },
            "version": "1.0"
        }
    
    def save_users(self):
        """保存用户数据"""
        try:
            # 确保目录存在
            self.users_file.parent.mkdir(exist_ok=True)
            
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(self.users, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Error saving users: {e}")
            return False
    
    def hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate(self, username: str, password: str) -> bool:
        """验证用户"""
        users = self.users.get("users", {})
        user = users.get(username)
        
        if not user or not user.get("active", False):
            return False
        
        password_hash = self.hash_password(password)
        return password_hash == user.get("password_hash")
    
    def add_user(self, username: str, password: str, name: str = "", role: str = "operator") -> bool:
        """添加用户"""
        if username in self.users.get("users", {}):
            return False
        
        from datetime import datetime
        
        self.users.setdefault("users", {})[username] = {
            "password_hash": self.hash_password(password),
            "role": role,
            "name": name or username,
            "created_at": datetime.now().isoformat(),
            "active": True
        }
        
        return self.save_users()
    
    def update_password(self, username: str, new_password: str) -> bool:
        """更新密码"""
        users = self.users.get("users", {})
        if username not in users:
            return False
        
        users[username]["password_hash"] = self.hash_password(new_password)
        return self.save_users()
    
    def deactivate_user(self, username: str) -> bool:
        """停用用户"""
        users = self.users.get("users", {})
        if username not in users:
            return False
        
        users[username]["active"] = False
        return self.save_users()
    
    def list_users(self) -> List[Dict]:
        """列出所有用户"""
        users = self.users.get("users", {})
        result = []
        
        for username, user_data in users.items():
            result.append({
                "username": username,
                "name": user_data.get("name", username),
                "role": user_data.get("role", "operator"),
                "active": user_data.get("active", False),
                "created_at": user_data.get("created_at", "")
            })
        
        return result

def main():
    """用户管理命令行工具"""
    user_manager = UserManager()
    
    print("👥 YF AI Chat 用户管理工具")
    print("=" * 50)
    
    while True:
        print("\n选择操作:")
        print("1. 查看所有用户")
        print("2. 添加用户")
        print("3. 修改密码")
        print("4. 停用用户")
        print("5. 测试登录")
        print("6. 退出")
        
        choice = input("\n请选择 (1-6): ").strip()
        
        if choice == "1":
            print("\n📋 用户列表:")
            users = user_manager.list_users()
            for user in users:
                status = "✅ 活跃" if user["active"] else "❌ 停用"
                print(f"  {user['username']} ({user['name']}) - {user['role']} - {status}")
        
        elif choice == "2":
            print("\n➕ 添加新用户:")
            username = input("用户名: ").strip()
            if not username:
                print("❌ 用户名不能为空")
                continue
            
            password = input("密码: ").strip()
            if not password:
                print("❌ 密码不能为空")
                continue
            
            name = input("显示名称 (可选): ").strip()
            role = input("角色 (admin/operator) [operator]: ").strip() or "operator"
            
            if user_manager.add_user(username, password, name, role):
                print(f"✅ 用户 {username} 添加成功")
            else:
                print(f"❌ 用户 {username} 已存在")
        
        elif choice == "3":
            print("\n🔑 修改密码:")
            username = input("用户名: ").strip()
            new_password = input("新密码: ").strip()
            
            if user_manager.update_password(username, new_password):
                print(f"✅ 用户 {username} 密码修改成功")
            else:
                print(f"❌ 用户 {username} 不存在")
        
        elif choice == "4":
            print("\n🚫 停用用户:")
            username = input("用户名: ").strip()
            
            if user_manager.deactivate_user(username):
                print(f"✅ 用户 {username} 已停用")
            else:
                print(f"❌ 用户 {username} 不存在")
        
        elif choice == "5":
            print("\n🔐 测试登录:")
            username = input("用户名: ").strip()
            password = input("密码: ").strip()
            
            if user_manager.authenticate(username, password):
                print(f"✅ 用户 {username} 登录成功")
            else:
                print(f"❌ 用户 {username} 登录失败")
        
        elif choice == "6":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()
