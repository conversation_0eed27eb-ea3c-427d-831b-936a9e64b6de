"""
EXE性能包装器
专为打包后的exe文件设计，在启动时自动应用高性能设置
"""

import os
import sys
import time
import atexit
import logging
import subprocess
from pathlib import Path

# 设置基本日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ExePerformanceManager:
    """EXE性能管理器"""
    
    def __init__(self):
        self.original_power_plan = None
        self.performance_applied = False
        
    def get_current_power_plan(self):
        """获取当前电源计划"""
        try:
            result = subprocess.run(
                ['powercfg', '/getactivescheme'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                # 提取GUID
                output = result.stdout.strip()
                if 'GUID:' in output:
                    guid = output.split('GUID:')[1].split()[0]
                    return guid
                    
        except Exception as e:
            logger.error(f"获取电源计划失败: {e}")
            
        return None
    
    def apply_high_performance(self):
        """应用高性能设置"""
        if sys.platform != "win32":
            logger.info("非Windows系统，跳过性能优化")
            return True
            
        try:
            logger.info("🔧 正在应用高性能优化...")
            
            # 保存当前电源计划
            self.original_power_plan = self.get_current_power_plan()
            logger.info(f"当前电源计划: {self.original_power_plan}")
            
            # 设置高性能电源计划
            high_perf_guid = "8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c"
            
            result = subprocess.run(
                ["powercfg", "/setactive", high_perf_guid],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                logger.info("✅ 已切换到高性能电源计划")
                
                # 应用额外优化
                self._apply_advanced_optimizations(high_perf_guid)
                
                self.performance_applied = True
                return True
            else:
                logger.warning(f"切换电源计划失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"应用高性能设置失败: {e}")
            return False
    
    def _apply_advanced_optimizations(self, power_guid):
        """应用高级优化设置"""
        try:
            optimizations = [
                # 禁用USB选择性暂停
                (power_guid, "2a737441-1930-4402-8d77-b2bebba308a3", 
                 "48e6b7a6-50f5-4782-a5d4-53bb8f07e226", "0"),
                
                # 设置处理器最小状态100%
                (power_guid, "54533251-82be-4824-96c1-47b60b740d00",
                 "893dee8e-2bef-41e0-89c6-b55d0929964c", "100"),
                
                # 设置处理器最大状态100%
                (power_guid, "54533251-82be-4824-96c1-47b60b740d00",
                 "bc5038f7-23e0-4960-96da-33abaf5935ec", "100"),
                
                # 禁用硬盘关闭
                (power_guid, "0012ee47-9041-4b5d-9b77-535fba8b1442",
                 "6738e2c4-e8a5-4a42-b16a-e040e769756e", "0"),
            ]
            
            for guid, subgroup, setting, value in optimizations:
                subprocess.run([
                    "powercfg", "/setacvalueindex", guid, subgroup, setting, value
                ], capture_output=True, timeout=5)
            
            # 应用所有设置
            subprocess.run([
                "powercfg", "/setactive", power_guid
            ], capture_output=True, timeout=5)
            
            logger.info("🚀 高级优化设置已应用")
            
        except Exception as e:
            logger.warning(f"应用高级优化失败: {e}")
    
    def restore_original_settings(self):
        """恢复原始设置"""
        if not self.performance_applied or sys.platform != "win32":
            return
            
        try:
            logger.info("🔄 正在恢复原始电源设置...")
            
            if self.original_power_plan:
                # 恢复原始电源计划
                result = subprocess.run([
                    "powercfg", "/setactive", self.original_power_plan
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    logger.info("✅ 已恢复原始电源计划")
                else:
                    # 如果恢复失败，至少切换到平衡模式
                    subprocess.run([
                        "powercfg", "/setactive", "381b4222-f694-41f0-9685-ff5bb260df2e"
                    ], capture_output=True, timeout=5)
                    logger.info("✅ 已切换到平衡电源模式")
            else:
                # 切换到平衡模式
                subprocess.run([
                    "powercfg", "/setactive", "381b4222-f694-41f0-9685-ff5bb260df2e"
                ], capture_output=True, timeout=5)
                logger.info("✅ 已切换到平衡电源模式")
                
        except Exception as e:
            logger.error(f"恢复电源设置失败: {e}")
    
    def optimize_process_priority(self):
        """优化当前进程优先级"""
        try:
            if sys.platform == "win32":
                import psutil
                current_process = psutil.Process()
                current_process.nice(psutil.HIGH_PRIORITY_CLASS)
                logger.info("⚡ 已设置进程为高优先级")
                return True
        except ImportError:
            logger.info("psutil未安装，跳过进程优先级设置")
        except Exception as e:
            logger.warning(f"设置进程优先级失败: {e}")
        
        return False

# 全局性能管理器
perf_manager = ExePerformanceManager()

def setup_performance_optimization():
    """设置性能优化"""
    logger.info("🚀 YF AI Chat Backend - EXE高性能模式")
    logger.info("=" * 50)
    
    # 应用高性能设置
    success = perf_manager.apply_high_performance()
    if success:
        logger.info("✅ 高性能电源设置已应用")
    else:
        logger.warning("⚠️ 高性能设置应用失败，可能需要管理员权限")
    
    # 优化进程优先级
    perf_manager.optimize_process_priority()
    
    # 注册退出时的清理函数
    atexit.register(cleanup_on_exit)
    
    logger.info("🎯 性能优化提示:")
    logger.info("   - 建议以管理员身份运行以获得最佳性能")
    logger.info("   - 高性能模式将在程序退出时自动恢复")
    logger.info("   - 防止系统进入效能模式")
    logger.info("=" * 50)

def cleanup_on_exit():
    """程序退出时的清理函数"""
    logger.info("🔄 程序退出，正在清理...")
    perf_manager.restore_original_settings()

def keep_system_awake():
    """保持系统唤醒（在主程序中调用）"""
    if sys.platform == "win32":
        try:
            import ctypes
            
            # Windows常量
            ES_CONTINUOUS = 0x80000000
            ES_SYSTEM_REQUIRED = 0x00000001
            ES_DISPLAY_REQUIRED = 0x00000002
            
            # 设置执行状态
            ctypes.windll.kernel32.SetThreadExecutionState(
                ES_CONTINUOUS | ES_SYSTEM_REQUIRED | ES_DISPLAY_REQUIRED
            )
            
            return True
        except Exception as e:
            logger.warning(f"保持系统唤醒失败: {e}")
            return False
    
    return True

# 如果作为主模块运行，则应用性能优化
if __name__ == "__main__":
    setup_performance_optimization()
    
    # 保持程序运行以测试
    try:
        logger.info("性能优化已应用，按Ctrl+C退出...")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("收到退出信号")
    finally:
        cleanup_on_exit()
