#!/usr/bin/env python3
"""
数据库管理器打包脚本
使用 PyInstaller 将数据库管理器打包成可执行文件
"""

import os
import sys
import subprocess
from pathlib import Path

def build_database_manager():
    """打包数据库管理器"""
    
    # 获取当前目录
    current_dir = Path(__file__).parent
    
    # 源文件路径
    source_file = current_dir / "main.py"
    
    # 图标文件路径
    icon_file = current_dir.parent / "backend" / "resources" / "text_icon.ico"
    
    # 输出目录
    dist_dir = current_dir / "dist"
    build_dir = current_dir / "build"
    
    # 检查源文件是否存在
    if not source_file.exists():
        print(f"❌ 源文件不存在: {source_file}")
        return False
    
    # 检查图标文件是否存在
    if not icon_file.exists():
        print(f"⚠️ 图标文件不存在: {icon_file}，将使用默认图标")
        icon_file = None
    
    print("🔨 开始打包数据库管理器...")
    print(f"📁 源文件: {source_file}")
    if icon_file:
        print(f"🎨 图标文件: {icon_file}")
    print()
    
    # PyInstaller 命令
    cmd = [
        "python", "-m", "PyInstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # 不显示控制台窗口
        "--name=YF_AI_Database_Manager", # 设置输出文件名
        "--clean",                      # 清理临时文件
    ]
    
    # 添加图标（如果存在）
    if icon_file:
        cmd.append(f"--icon={icon_file}")
    
    # 添加隐藏导入（确保所有模块都被包含）
    hidden_imports = [
        "PyQt6.QtCore",
        "PyQt6.QtGui", 
        "PyQt6.QtWidgets",
        "pandas",
        "sqlite3",
        "json",
        "pathlib"
    ]
    
    for module in hidden_imports:
        cmd.extend(["--hidden-import", module])
    
    # 添加数据文件（如果有配置文件等）
    # cmd.extend(["--add-data", "config.json;."])
    
    cmd.append(str(source_file))
    
    try:
        # 执行打包命令
        print("🚀 执行打包命令...")
        print(f"命令: {' '.join(cmd)}")
        print()
        
        result = subprocess.run(cmd, cwd=current_dir, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 打包成功!")
            
            # 查找生成的exe文件
            exe_file = dist_dir / "YF_AI_Database_Manager.exe"
            if exe_file.exists():
                print(f"📦 可执行文件位置: {exe_file}")
                print(f"📏 文件大小: {exe_file.stat().st_size / 1024 / 1024:.2f} MB")
                
                print()
                print("📋 使用说明:")
                print("1. 可执行文件已生成，可以直接运行")
                print("2. 首次运行会在exe同目录创建config.json配置文件")
                print("3. 支持连接SQLite数据库文件")
                print("4. 支持数据查看、编辑、查询和导出功能")
                print()
                print("🖥️ 创建桌面快捷方式:")
                print(f"   右键点击 {exe_file}")
                print("   选择 '发送到' -> '桌面快捷方式'")
                
                return True
            else:
                print(f"❌ 未找到生成的exe文件: {exe_file}")
                return False
        else:
            print("❌ 打包失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except FileNotFoundError:
        print("❌ PyInstaller 未安装!")
        print("请先安装 PyInstaller:")
        print("pip install pyinstaller")
        return False
    except Exception as e:
        print(f"❌ 打包过程中出现错误: {e}")
        return False

def clean_build_files():
    """清理构建文件"""
    current_dir = Path(__file__).parent
    
    # 要清理的目录和文件
    clean_targets = [
        current_dir / "build",
        current_dir / "dist",
        current_dir / "YF_AI_Database_Manager.spec"
    ]
    
    print("🧹 清理构建文件...")
    
    for target in clean_targets:
        if target.exists():
            if target.is_dir():
                import shutil
                shutil.rmtree(target)
                print(f"🗑️ 已删除目录: {target}")
            else:
                target.unlink()
                print(f"🗑️ 已删除文件: {target}")
    
    print("✅ 清理完成!")

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = ["PyQt6", "pandas", "pyinstaller"]
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == "pyinstaller":
                import PyInstaller
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print()
        print("❌ 缺少必要的依赖包，请先安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def main():
    """主函数"""
    print("🔐 YF AI Chat 数据库管理器打包工具")
    print("=" * 60)
    print()
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    print()
    print("选择操作:")
    print("1. 打包数据库管理器")
    print("2. 清理构建文件")
    print("3. 退出")
    print()
    
    while True:
        choice = input("请选择 (1-3): ").strip()
        
        if choice == "1":
            print()
            success = build_database_manager()
            if success:
                print()
                input("按回车键退出...")
            break
        elif choice == "2":
            print()
            clean_build_files()
            print()
            input("按回车键退出...")
            break
        elif choice == "3":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请输入 1-3")

if __name__ == "__main__":
    main()
