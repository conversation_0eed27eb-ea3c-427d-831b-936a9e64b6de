/**
 * YF AI Chat Widget for Shopify
 * 基于WordPress版本改造，直接调用后端API
 * 版本: 2.1.7-debug - 调试版本，修复清空历史记录功能
 *
 * 配置通过 window.YF_CHAT_CONFIG 从 snippets/yf-chat-widget.liquid 加载
 * 安全更新: 清理所有可能暴露API信息的控制台日志
 * 显示修复: 强化CSS优先级，确保在各种Shopify主题中正确显示
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // 🔒 安全日志：不暴露任何配置信息
    // console.log('YF AI Chat: Initializing...');

    // 检查配置是否存在
    if (typeof window.YF_CHAT_CONFIG === 'undefined') {
        // console.error('YF AI Chat: Configuration not found');
        // console.error('Please ensure yf-chat-widget.liquid is properly included in your theme');
        return; // 这个return现在在函数内部，是合法的
    }

    // 使用全局配置
    const YF_CHAT_CONFIG = window.YF_CHAT_CONFIG;

    class YFAIChatShopify {
        constructor() {
            // 🔒 完全移除版本和调试信息
            // console.log('YF AI Chat: Starting...');

            // 🔒 安全的配置检查，完全不暴露任何信息
            if (!YF_CHAT_CONFIG.apiUrl) {
                // console.error('YF AI Chat: Configuration error');
                this.configValid = false;
                return;
            }
            if (!YF_CHAT_CONFIG.apiToken || YF_CHAT_CONFIG.apiToken === 'YOUR_WORDPRESS_TOKEN_HERE' || YF_CHAT_CONFIG.apiToken.length < 10) {
                // console.error('YF AI Chat: Authentication error');
                this.configValid = false;
                return;
            }

            // 🔒 安全日志：配置验证通过，不暴露具体信息
            // console.log('YF AI Chat: Ready');
            this.configValid = true;

            this.isOpen = false;
            this.isTyping = false;
            this.sessionTimeout = YF_CHAT_CONFIG.settings.sessionTimeout;
            this.saveHistoryEnabled = YF_CHAT_CONFIG.settings.saveHistory;
            this.lastMessageTime = null;
            this.pollInterval = null;
            this.messageIds = new Set();
            this.emptyResponseCount = 0;

            // 只有配置有效时才初始化
            if (this.configValid) {
                // 初始化会话
                this.initializeSession().then(() => {
                    this.init();
                }).catch(error => {
                    // 🔒 不暴露错误详情
                    // console.log('Session initialization completed');
                    this.sessionId = this.generateSessionId();
                    this.chatHistory = [];
                    this.init();
                });
            }
        }

        init() {
            if (!this.configValid) {
                // console.error('YF AI Chat: Cannot initialize with invalid configuration');
                return;
            }
            this.createWidget();
            this.bindEvents();
            this.setPosition();
        }

        generateSessionId() {
            return this.generatePersistentSessionId();
        }

        generatePersistentSessionId() {
            const existingId = localStorage.getItem('yf_chat_persistent_session_id');
            if (existingId) {
                // 🔒 不暴露session ID
                // console.log('Using existing session');
                return existingId;
            }

            const fingerprint = this.generateBrowserFingerprint();
            const timestamp = Date.now();
            const sessionId = 'session_' + fingerprint + '_' + timestamp;

            localStorage.setItem('yf_chat_persistent_session_id', sessionId);
            // 🔒 不暴露session ID
            // console.log('Generated new session');

            return sessionId;
        }

        generateBrowserFingerprint() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('Browser fingerprint', 2, 2);

            const fingerprint = [
                navigator.userAgent,
                navigator.language,
                screen.width + 'x' + screen.height,
                screen.colorDepth,
                new Date().getTimezoneOffset(),
                navigator.platform,
                navigator.cookieEnabled,
                typeof(navigator.doNotTrack) !== 'undefined' ? navigator.doNotTrack : 'unknown',
                canvas.toDataURL()
            ].join('|');

            return this.simpleHash(fingerprint).toString(36);
        }

        simpleHash(str) {
            let hash = 0;
            if (str.length === 0) return hash;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return Math.abs(hash);
        }

        async initializeSession() {
            // 🔒 不暴露初始化详情
            // console.log('YF AI Chat: Initializing...');

            this.sessionId = this.generateSessionId();
            // 🔒 不暴露session ID

            if (!this.saveHistoryEnabled) {
                this.chatHistory = [];
                return;
            }

            const hasBackendHistory = await this.checkBackendSession();

            if (hasBackendHistory) {
                await this.loadBackendHistory();
            } else {
                this.loadChatHistoryForSession();
            }

            this.updateSessionInStorage();
        }

        loadChatHistoryForSession() {
            const historyKey = 'yf_chat_history_' + this.sessionId;
            const savedHistory = localStorage.getItem(historyKey);

            if (savedHistory) {
                try {
                    const historyData = JSON.parse(savedHistory);
                    // 🔒 不暴露session ID

                    if (historyData.messages && historyData.lastUpdated) {
                        const now = Date.now();
                        const historyAge = now - historyData.lastUpdated;

                        if (historyAge < this.sessionTimeout) {
                            this.chatHistory = historyData.messages || [];
                            return;
                        } else {
                            localStorage.removeItem(historyKey);
                        }
                    }
                } catch (e) {
                    // 🔒 不暴露错误详情
                    localStorage.removeItem(historyKey);
                }
            }
            this.chatHistory = [];
        }

        async checkBackendSession() {
            try {
                const response = await fetch(`${YF_CHAT_CONFIG.apiUrl}/api/session/${this.sessionId}/exists`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${YF_CHAT_CONFIG.apiToken}`,
                        'Cache-Control': 'no-cache'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data && data.exists && data.message_count > 0) {
                        // 🔒 不暴露消息数量
                        return true;
                    }
                }

                return false;
            } catch (error) {
                // 🔒 不暴露错误详情
                return false;
            }
        }

        async loadBackendHistory() {
            try {
                const response = await fetch(`${YF_CHAT_CONFIG.apiUrl}/api/session/${this.sessionId}/messages`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${YF_CHAT_CONFIG.apiToken}`,
                        'Cache-Control': 'no-cache'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data && data.messages) {
                        const messages = data.messages;
                        // 🔒 不暴露消息数量

                        messages.sort((a, b) => a.id - b.id);

                        this.chatHistory = [];
                        this.messageIds.clear();

                        messages.forEach(msg => {
                            this.messageIds.add(msg.id);

                            if (msg.sender === 'admin') {
                                this.addAdminMessageToUI(msg.content, false, msg.id);
                            } else {
                                this.addMessageToUI(msg.content, msg.sender, false, msg.id);
                            }

                            this.chatHistory.push({
                                content: msg.content,
                                sender: msg.sender,
                                timestamp: Date.now()
                            });
                        });

                        // 🔒 不暴露消息数量
                        // console.log('Backend history loaded');
                    }
                }
            } catch (error) {
                // 🔒 不暴露错误详情
                this.loadChatHistoryForSession();
            }
        }

        updateSessionInStorage() {
            if (!this.saveHistoryEnabled) {
                return;
            }

            const sessionData = {
                sessionId: this.sessionId,
                createdAt: Date.now(),
                lastActivity: Date.now()
            };
            localStorage.setItem('yf_chat_session', JSON.stringify(sessionData));
            // 🔒 不暴露session数据
        }

        updateSessionActivity() {
            if (!this.saveHistoryEnabled) {
                return;
            }

            const savedSession = localStorage.getItem('yf_chat_session');
            if (savedSession) {
                try {
                    const sessionData = JSON.parse(savedSession);
                    sessionData.lastActivity = Date.now();
                    localStorage.setItem('yf_chat_session', JSON.stringify(sessionData));
                    // 🔒 不暴露活动更新详情
                } catch (e) {
                    // 🔒 不暴露错误详情
                    this.updateSessionInStorage();
                }
            } else {
                this.updateSessionInStorage();
            }
        }

        clearSession() {
            localStorage.removeItem('yf_chat_session');
            if (this.sessionId) {
                const historyKey = 'yf_chat_history_' + this.sessionId;
                localStorage.removeItem(historyKey);
            }
            this.messageIds.clear();
        }

        saveChatHistory() {
            if (!this.saveHistoryEnabled) {
                return;
            }

            const historyData = {
                messages: this.chatHistory,
                lastUpdated: Date.now()
            };
            const historyKey = 'yf_chat_history_' + this.sessionId;
            localStorage.setItem(historyKey, JSON.stringify(historyData));
            // 🔒 不暴露session ID和消息数量
        }

        createWidget() {
            // console.log('YF AI Chat: Creating widget...');

            try {
                // 检查是否已存在widget，避免重复创建
                const existingWidget = document.getElementById('yf-chat-widget');
                if (existingWidget) {
                    existingWidget.remove();
                    // console.log('YF AI Chat: Removed existing widget');
                }

                // 使用简单直接的方法创建HTML
                const widgetHTML = `
                    <div class="yf-chat-widget" id="yf-chat-widget">
                        <button class="yf-chat-button" id="yf-chat-button">
                            💬
                        </button>
                        <div class="yf-chat-window" id="yf-chat-window">
                            <div class="yf-chat-header">
                                <div class="yf-chat-title">${YF_CHAT_CONFIG.strings.title}</div>
                                <div class="yf-chat-header-buttons">
                                    <button class="yf-chat-clear" id="yf-chat-clear" title="清除聊天记录">🗑️</button>
                                    <button class="yf-chat-close" id="yf-chat-close">×</button>
                                </div>
                            </div>
                            <div class="yf-chat-messages" id="yf-chat-messages">
                            </div>
                            <div class="yf-chat-input-area">
                                <textarea class="yf-chat-input" id="yf-chat-input"
                                    placeholder="${YF_CHAT_CONFIG.strings.placeholder}"
                                    rows="1"></textarea>
                                <button class="yf-chat-send" id="yf-chat-send">
                                    ➤
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                // 直接插入到body末尾
                document.body.insertAdjacentHTML('beforeend', widgetHTML);
                // console.log('YF AI Chat: Widget HTML inserted');

                // 验证元素是否创建成功
                const widget = document.getElementById('yf-chat-widget');
                const button = document.getElementById('yf-chat-button');

                if (widget && button) {
                    // console.log('YF AI Chat: Widget and button created successfully');
                    // console.log('YF AI Chat: Button element:', button);
                    // console.log('YF AI Chat: Button computed style:', window.getComputedStyle(button).display);
                } else {
                    // console.error('YF AI Chat: Widget or button creation failed');
                    // console.log('YF AI Chat: Widget:', widget);
                    // console.log('YF AI Chat: Button:', button);
                }



                // 恢复聊天历史或显示欢迎消息
                this.restoreChatHistory();

                // console.log('YF AI Chat: Ready');
            } catch (error) {
                console.error('YF AI Chat: Initialization failed:', error);
            }
        }



        restoreChatHistory() {
            const messagesContainer = document.getElementById('yf-chat-messages');

            // 🔒 不暴露消息数量

            if (this.chatHistory.length > 0) {
                this.chatHistory.forEach((msg, index) => {
                    // 🔒 不暴露消息内容
                    if (msg.sender === 'admin') {
                        this.addAdminMessageToUI(msg.content, false);
                    } else {
                        this.addMessageToUI(msg.content, msg.sender, false);
                    }

                    const simulatedId = `local_${msg.timestamp}_${msg.sender}_${msg.content.substring(0, 20)}`;
                    this.messageIds.add(simulatedId);
                    // 🔒 不暴露模拟ID
                });
                // 🔒 不暴露恢复详情
            } else {
                this.addMessageToUI(YF_CHAT_CONFIG.strings.welcomeMessage, 'ai', false);
            }
        }

        setPosition() {
            const widget = document.getElementById('yf-chat-widget');
            const margin = YF_CHAT_CONFIG.margin + 'px';

            widget.classList.add(YF_CHAT_CONFIG.position);
            widget.style.setProperty('--yf-margin', margin);
        }

        bindEvents() {
            const self = this;

            // Toggle chat window
            document.getElementById('yf-chat-button').addEventListener('click', function() {
                self.toggleChat();
            });

            // Close chat window
            document.getElementById('yf-chat-close').addEventListener('click', function() {
                self.closeChat();
            });

            // Clear chat history
            document.getElementById('yf-chat-clear').addEventListener('click', function() {
                self.clearChatHistory();
            });

            // Send message on button click
            document.getElementById('yf-chat-send').addEventListener('click', function() {
                self.sendMessage();
            });

            // Send message on Enter key
            document.getElementById('yf-chat-input').addEventListener('keypress', function(e) {
                if (e.which === 13 && !e.shiftKey) {
                    e.preventDefault();
                    self.sendMessage();
                }
            });

            // Auto-resize textarea
            document.getElementById('yf-chat-input').addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 100) + 'px';
            });

            // Close on outside click
            document.addEventListener('click', function(e) {
                if (self.isOpen && !e.target.closest('#yf-chat-widget')) {
                    self.closeChat();
                }
            });
        }

        toggleChat() {
            if (this.isOpen) {
                this.closeChat();
            } else {
                this.openChat();
            }
        }

        openChat() {
            document.getElementById('yf-chat-window').classList.add('show');
            document.getElementById('yf-chat-input').focus();
            this.isOpen = true;
            this.scrollToBottom();
        }

        closeChat() {
            document.getElementById('yf-chat-window').classList.remove('show');
            this.isOpen = false;
        }

        sendMessage() {
            const input = document.getElementById('yf-chat-input');
            const message = input.value.trim();

            if (!message || this.isTyping) {
                return;
            }

            input.value = '';
            input.style.height = 'auto';

            this.updateSessionActivity();
            this.showTyping();
            this.sendToBackend(message);
        }

        addMessage(content, sender, messageId = null, allowWithoutId = false) {
            if (messageId && this.messageIds.has(messageId)) {
                return;
            }

            if (!messageId && !allowWithoutId) {
                // 🔒 不暴露消息ID详情
                return;
            }

            if (messageId) {
                this.messageIds.add(messageId);
            }

            this.addMessageToUI(content, sender, true, messageId);
        }

        addMessageToUI(content, sender, saveToHistory = true, messageId = null) {
            const messagesContainer = document.getElementById('yf-chat-messages');

            // 🔧 安全检查：确保消息容器存在
            if (!messagesContainer) {
                // console.error('Messages container not found');
                return;
            }

            const messageElement = document.createElement('div');
            messageElement.className = `yf-chat-message ${sender}`;
            messageElement.setAttribute('data-message-id', messageId || 'temp-' + Date.now());
            messageElement.innerHTML = `
                <div class="yf-chat-message-content">
                    ${this.escapeHtml(content)}
                </div>
            `;

            if (messageId) {
                this.insertMessageAtCorrectPosition(messageElement, messageId);
            } else {
                messagesContainer.appendChild(messageElement);
            }

            this.scrollToBottom();

            if (saveToHistory) {
                this.chatHistory.push({
                    content: content,
                    sender: sender,
                    timestamp: Date.now()
                });
                this.saveChatHistory();
            }
        }

        insertMessageAtCorrectPosition(messageElement, messageId) {
            const messagesContainer = document.getElementById('yf-chat-messages');
            const existingMessages = messagesContainer.querySelectorAll('.yf-chat-message[data-message-id]');

            let inserted = false;

            for (let i = 0; i < existingMessages.length; i++) {
                const existingElement = existingMessages[i];
                const existingId = existingElement.getAttribute('data-message-id');

                if (typeof existingId === 'string' && existingId.startsWith('temp-')) {
                    continue;
                }

                const existingIdNum = parseInt(existingId);
                const newIdNum = parseInt(messageId);

                if (existingIdNum > newIdNum) {
                    messagesContainer.insertBefore(messageElement, existingElement);
                    inserted = true;
                    break;
                }
            }

            if (!inserted) {
                messagesContainer.appendChild(messageElement);
            }

            // 🔒 不暴露消息ID
        }

        showTyping() {
            if (document.querySelector('.yf-chat-typing')) {
                return;
            }

            const typingElement = document.createElement('div');
            typingElement.className = 'yf-chat-message ai yf-chat-typing';
            typingElement.innerHTML = `
                <div class="yf-chat-message-content">
                    <div class="yf-chat-typing">
                        <span>${YF_CHAT_CONFIG.strings.assistantName} is typing</span>
                        <div class="yf-chat-typing-dots">
                            <div class="yf-chat-typing-dot"></div>
                            <div class="yf-chat-typing-dot"></div>
                            <div class="yf-chat-typing-dot"></div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('yf-chat-messages').appendChild(typingElement);
            this.scrollToBottom();
            this.isTyping = true;
        }

        hideTyping() {
            const typingElements = document.querySelectorAll('.yf-chat-typing');
            typingElements.forEach(element => element.remove());
            this.isTyping = false;
        }

        async sendToBackend(message) {
            try {
                // 🔒 不暴露API调用详情
                const response = await fetch(`${YF_CHAT_CONFIG.apiUrl}/api/chat`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${YF_CHAT_CONFIG.apiToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: message,
                        session_id: this.sessionId
                    })
                });

                this.hideTyping();

                if (response.ok) {
                    const data = await response.json();
                    // 🔒 不暴露响应数据

                    if (data.admin_controlled === true) {
                        // 🔒 不暴露管理状态详情
                    } else if (data.no_response === true) {
                        // 🔒 不暴露空响应详情
                    } else if (data.response && data.response.trim() !== '') {
                        // 🔒 不暴露AI响应内容
                        const aiMessageId = data.ai_message_id;
                        // 🔒 不暴露消息ID
                        this.addMessage(data.response, 'ai', aiMessageId);
                    } else {
                        this.addMessage('Sorry, I didn\'t receive a proper response. Please try again.', 'ai', null, true);
                    }

                    // 第一次成功发送消息后开始轮询
                    if (!this.pollInterval) {
                        this.startPolling();
                    }

                    // 立即触发一次轮询
                    setTimeout(() => {
                        this.checkForNewMessages();
                    }, 100);
                } else {
                    // 🔒 不暴露错误状态
                    this.addMessage(YF_CHAT_CONFIG.strings.error, 'ai', null, true);
                }
            } catch (error) {
                this.hideTyping();
                // 🔒 不暴露错误详情
                this.addMessage(YF_CHAT_CONFIG.strings.error, 'ai', null, true);
            }
        }

        scrollToBottom() {
            const messagesContainer = document.getElementById('yf-chat-messages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        async clearChatHistory() {
            if (confirm('确定要清除所有聊天记录吗？这将开始一个全新的对话会话。\n\n历史记录将自动保存到服务器。')) {
                // 🔧 分离导出和清空操作，确保清空操作始终执行
                let exportSuccess = false;

                // 先尝试导出（不阻塞清空操作）
                try {
                    if (this.sessionId && this.chatHistory.length > 0) {
                        // 🔧 临时调试：显示基本信息（不暴露敏感数据）
                        console.log('Starting export operation...');
                        console.log('Session exists:', !!this.sessionId);
                        console.log('Chat history length:', this.chatHistory.length);
                        console.log('API URL configured:', !!YF_CHAT_CONFIG.apiUrl);
                        console.log('API Token configured:', !!YF_CHAT_CONFIG.apiToken);

                        const result = await this.exportSessionToCSV(this.sessionId, 'user_cleared');
                        exportSuccess = (result !== null);

                        console.log('Export result:', exportSuccess ? 'success' : 'failed');
                    } else {
                        console.log('Skipping export: no session or empty history');
                        exportSuccess = true; // 没有内容需要导出，视为成功
                    }
                } catch (error) {
                    console.log('Export operation failed:', error.message);
                    exportSuccess = false;
                }

                // 🔧 无论导出是否成功，都执行清空操作
                try {
                    console.log('Starting UI clear operation...');

                    // 清除UI中的消息
                    const messagesContainer = document.getElementById('yf-chat-messages');
                    if (messagesContainer) {
                        console.log('Messages container found, clearing...');
                        messagesContainer.innerHTML = '';
                        console.log('Messages container cleared');
                    } else {
                        console.log('Messages container not found!');
                    }

                    // 清除历史记录
                    console.log('Clearing chat history...');
                    this.chatHistory = [];

                    // 清除消息ID集合
                    console.log('Clearing message IDs...');
                    this.messageIds.clear();

                    // 🔧 修正：保持相同的session ID，只清除本地存储的历史记录
                    console.log('Clearing session...');
                    this.clearSession();

                    // 重置轮询相关状态
                    console.log('Resetting polling state...');
                    this.lastMessageTime = null;
                    this.emptyResponseCount = 0;
                    this.stopPolling();

                    // 更新存储
                    console.log('Updating session storage...');
                    this.updateSessionInStorage();

                    // 显示欢迎消息和新会话提示
                    this.addMessageToUI(YF_CHAT_CONFIG.strings.welcomeMessage, 'ai', false);

                    // 根据导出结果显示不同的提示消息
                    if (exportSuccess) {
                        this.addMessageToUI('✨ New conversation started! Chat history has been saved to server.', 'system', false);
                    } else {
                        this.addMessageToUI('✨ New conversation started! (Note: History backup may have failed)', 'system', false);
                    }

                } catch (clearError) {
                    // 如果清空操作也失败，强制刷新页面
                    // console.error('Clear operation failed, forcing page refresh');
                    window.location.reload();
                }
            }
        }

        async exportSessionToCSV(sessionId, reason) {
            // 导出会话记录到CSV
            try {
                // 🔧 验证必要参数
                if (!sessionId || !YF_CHAT_CONFIG.apiUrl || !YF_CHAT_CONFIG.apiToken) {
                    // console.error('Missing required parameters for export');
                    return null;
                }

                // 🔒 构建请求，避免在日志中暴露敏感信息
                const apiUrl = YF_CHAT_CONFIG.apiUrl + '/api/export-session';
                const headers = {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${YF_CHAT_CONFIG.apiToken}`
                };

                const requestBody = {
                    session_id: sessionId,
                    reason: reason || 'user_cleared'
                };

                console.log('Sending export request...');
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(requestBody)
                });

                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);

                if (response.ok) {
                    const result = await response.json();
                    console.log('Export operation completed successfully');
                    return result;
                } else {
                    const errorText = await response.text();
                    console.log(`Export operation failed with status: ${response.status}`);
                    console.log('Error response:', errorText);
                    return null;
                }
            } catch (error) {
                // 🔒 不记录具体错误信息
                // console.log('Export operation encountered an issue:', error.name);
                return null;
            }
        }

        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        startPolling() {
            // 🔒 不暴露轮询详情
            this.pollInterval = setInterval(() => {
                this.checkForNewMessages();
            }, 3000);
        }

        stopPolling() {
            if (this.pollInterval) {
                clearInterval(this.pollInterval);
                this.pollInterval = null;
                // 🔒 不暴露轮询状态
            }
        }

        async checkForNewMessages() {
            if (!this.emptyResponseCount) {
                this.emptyResponseCount = 0;
            }

            // 🔒 不暴露轮询详情

            const requestUrl = `${YF_CHAT_CONFIG.apiUrl}/api/session/${this.sessionId}/messages?_=${Date.now()}`;
            // 🔒 不暴露请求URL

            try {
                const response = await fetch(requestUrl, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${YF_CHAT_CONFIG.apiToken}`,
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    // 🔒 不暴露API响应

                    if (data.messages && data.messages.length > 0) {
                        // 🔒 不暴露消息数量
                        this.emptyResponseCount = 0;

                        const sortedMessages = data.messages.sort((a, b) => a.id - b.id);
                        // 🔒 不暴露消息详情

                        sortedMessages.forEach((message) => {
                            const messageId = message.id;

                            if (!messageId) {
                                // 🔒 不暴露消息详情
                                return;
                            }

                            if (message.sender === 'admin') {
                                this.addAdminMessage(message.content, messageId);
                            } else if (message.sender === 'ai') {
                                this.addMessage(message.content, 'ai', messageId);
                            } else if (message.sender === 'user') {
                                this.addMessage(message.content, 'user', messageId);
                            }
                        });
                    } else {
                        this.emptyResponseCount++;
                        // 🔒 不暴露响应计数

                        if (this.emptyResponseCount >= 20) {
                            // 🔒 不暴露轮询状态
                            this.clearSession();
                            this.sessionId = this.generateSessionId();
                            this.updateSessionInStorage();
                            this.lastMessageTime = null;
                            this.emptyResponseCount = 0;
                            this.stopPolling();
                            return;
                        }
                    }
                } else if (response.status === 404) {
                    // 🔒 不暴露会话状态
                    this.clearSession();
                    this.sessionId = this.generateSessionId();
                    this.updateSessionInStorage();
                    this.lastMessageTime = null;
                    this.emptyResponseCount = 0;
                    this.stopPolling();
                } else {
                    // 🔒 不暴露错误状态
                }
            } catch (error) {
                // 🔒 不暴露错误详情
            }
        }

        addAdminMessage(content, messageId = null) {
            if (messageId && this.messageIds.has(messageId)) {
                return;
            }

            if (!messageId) {
                // 🔒 不暴露消息ID详情
                return;
            }

            this.messageIds.add(messageId);
            this.hideTyping();
            this.addAdminMessageToUI(content, true, messageId);

            if (!this.isOpen) {
                this.showNotification();
            }
        }

        addAdminMessageToUI(content, saveToHistory = true, messageId = null) {
            // 🔒 不暴露管理员消息详情

            const messagesContainer = document.getElementById('yf-chat-messages');
            const messageElement = document.createElement('div');
            messageElement.className = 'yf-chat-message ai';  // 使用AI样式实现无感知效果
            messageElement.setAttribute('data-message-id', messageId || 'temp-' + Date.now());
            messageElement.innerHTML = `
                <div class="yf-chat-message-content">
                    ${this.escapeHtml(content)}
                </div>
            `;

            if (messageId) {
                this.insertMessageAtCorrectPosition(messageElement, messageId);
            } else {
                messagesContainer.appendChild(messageElement);
            }

            this.scrollToBottom();

            if (saveToHistory) {
                this.chatHistory.push({
                    content: content,
                    sender: 'admin',
                    timestamp: Date.now()
                });
                this.saveChatHistory();
            }
        }

        showNotification() {
            const button = document.getElementById('yf-chat-button');
            button.classList.add('yf-chat-notification');

            setTimeout(() => {
                button.classList.remove('yf-chat-notification');
            }, 3000);
        }
    }

    // 初始化聊天组件
    new YFAIChatShopify();
});
