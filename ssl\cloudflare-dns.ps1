param(
    [Parameter(Mandatory=$true)]
    [string]$RecordName,
    
    [Parameter(Mandatory=$true)]
    [string]$Token,
    
    [Parameter(Mandatory=$false)]
    [string]$Action = "create"
)

# Cloudflare配置
$CF_API_TOKEN = "****************************************"
$CF_ZONE_ID = "e92deb270a88a0a6865e6347cd88d96e"
$CF_API_BASE = "https://api.cloudflare.com/client/v4"

# 日志函数
function Write-Log {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message"
    Add-Content -Path "ssl/dns-script.log" -Value "[$timestamp] $Message"
}

# 创建DNS记录
function Create-DNSRecord {
    param(
        [string]$Name,
        [string]$Content
    )
    
    Write-Log "创建DNS TXT记录: $Name = $Content"
    
    $headers = @{
        "Authorization" = "Bearer $CF_API_TOKEN"
        "Content-Type" = "application/json"
    }
    
    $body = @{
        type = "TXT"
        name = $Name
        content = $Content
        ttl = 120
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri "$CF_API_BASE/zones/$CF_ZONE_ID/dns_records" -Method POST -Headers $headers -Body $body
        
        if ($response.success) {
            Write-Log "DNS记录创建成功: ID = $($response.result.id)"
            # 保存记录ID以便后续删除
            Add-Content -Path "ssl/dns-record-ids.txt" -Value "$Name|$($response.result.id)"
            return $true
        } else {
            Write-Log "DNS记录创建失败: $($response.errors | ConvertTo-Json)"
            return $false
        }
    } catch {
        Write-Log "API调用失败: $($_.Exception.Message)"
        return $false
    }
}

# 删除DNS记录
function Remove-DNSRecord {
    param([string]$Name)
    
    Write-Log "删除DNS TXT记录: $Name"
    
    # 从文件中查找记录ID
    $recordFile = "ssl/dns-record-ids.txt"
    if (Test-Path $recordFile) {
        $records = Get-Content $recordFile
        foreach ($record in $records) {
            $parts = $record -split '\|'
            if ($parts[0] -eq $Name) {
                $recordId = $parts[1]
                
                $headers = @{
                    "Authorization" = "Bearer $CF_API_TOKEN"
                    "Content-Type" = "application/json"
                }
                
                try {
                    $response = Invoke-RestMethod -Uri "$CF_API_BASE/zones/$CF_ZONE_ID/dns_records/$recordId" -Method DELETE -Headers $headers
                    
                    if ($response.success) {
                        Write-Log "DNS记录删除成功: $recordId"
                        # 从文件中移除记录
                        $updatedRecords = $records | Where-Object { $_ -ne $record }
                        $updatedRecords | Set-Content $recordFile
                        return $true
                    } else {
                        Write-Log "DNS记录删除失败: $($response.errors | ConvertTo-Json)"
                        return $false
                    }
                } catch {
                    Write-Log "删除API调用失败: $($_.Exception.Message)"
                    return $false
                }
            }
        }
    }
    
    Write-Log "未找到记录ID: $Name"
    return $false
}

# 主逻辑
Write-Log "开始执行DNS脚本 - Action: $Action, RecordName: $RecordName"

# 确保ssl目录存在
if (!(Test-Path "ssl")) {
    New-Item -ItemType Directory -Path "ssl" -Force
}

switch ($Action.ToLower()) {
    "create" {
        $success = Create-DNSRecord -Name $RecordName -Content $Token
        if ($success) {
            Write-Log "等待DNS传播..."
            Start-Sleep -Seconds 30
            Write-Host "DNS记录创建完成，等待验证..."
            exit 0
        } else {
            Write-Host "DNS记录创建失败"
            exit 1
        }
    }
    
    "delete" {
        $success = Remove-DNSRecord -Name $RecordName
        if ($success) {
            Write-Host "DNS记录删除完成"
            exit 0
        } else {
            Write-Host "DNS记录删除失败"
            exit 1
        }
    }
    
    default {
        Write-Log "未知操作: $Action"
        Write-Host "支持的操作: create, delete"
        exit 1
    }
}
