"""
售后表单收集和处理服务
处理高级售后问题的表单收集，并推送到客服端
"""

import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.path_utils import get_salesgpt_config_directory

from services.database import get_db
from models.chat_models import ChatMessage, ChatSession
from utils.file_logger import backend_logger as logger


class FormService:
    def __init__(self):
        self.active_forms = {}  # 存储正在进行的表单会话
        self.form_config = self._load_form_config()
    
    def _load_form_config(self) -> Dict[str, Any]:
        """加载表单配置"""
        try:
            config_dir = get_salesgpt_config_directory()
            config_path = config_dir / 'advanced_sales_config.json'
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('after_sales_support', {}).get('advanced_support', {})
        except Exception as e:
            logger.error(f"加载表单配置失败: {e}")
            return self._get_default_form_config()
    
    def _get_default_form_config(self) -> Dict[str, Any]:
        """获取默认表单配置（移除硬编码，返回空配置）"""
        logger.warning("表单配置文件加载失败，使用空配置。请检查配置文件是否存在且格式正确。")
        return {
            "form_fields": {},
            "categories": {},
            "enabled": False
        }
    
    def should_trigger_form(self, message: str) -> Tuple[bool, str]:
        """判断是否应该触发表单收集"""
        message_lower = message.lower()

        # 从配置文件读取高级售后问题的关键词
        categories = self.form_config.get('categories', {})

        for category_key, category_info in categories.items():
            if isinstance(category_info, dict):
                keywords = category_info.get('keywords', [])
                if any(keyword.lower() in message_lower for keyword in keywords):
                    return True, category_key

        return False, ""
    
    def initiate_form_collection(self, session_id: str, issue_category: str) -> str:
        """开始表单收集流程"""
        # 初始化表单状态
        self.active_forms[session_id] = {
            "category": issue_category,
            "current_field": None,
            "collected_data": {},
            "field_order": list(self.form_config["form_fields"].keys()),
            "started_at": datetime.now().isoformat()
        }
        
        # 获取第一个字段
        first_field = self.active_forms[session_id]["field_order"][0]
        self.active_forms[session_id]["current_field"] = first_field
        
        # 生成开始消息
        trigger_message = self._get_trigger_message(issue_category)
        first_field_prompt = self._get_field_prompt(first_field)
        
        logger.info(f"开始表单收集: {session_id}, 类别: {issue_category}")
        
        return f"{trigger_message}\n\n{first_field_prompt}"
    
    def _get_trigger_message(self, category: str) -> str:
        """获取表单触发消息"""
        # 优先从配置文件读取
        categories = self.form_config.get('categories', {})
        if category in categories and 'trigger_message' in categories[category]:
            return categories[category]['trigger_message']

        # 如果配置文件中没有，使用默认消息
        default_messages = {
            "technical_malfunction": "看起来您遇到了技术问题，我需要收集一些详细信息来为您提供最好的帮助。",
            "replacement_request": "关于换货申请，我需要了解一些具体情况来为您处理。",
            "refund_request": "关于退款申请，我需要核实一些信息来为您办理。",
            "complex_technical": "这个问题需要我们的技术专家来为您解答，请提供一些详细信息。"
        }
        fallback_message = self.form_config.get("form_prompts", {}).get("default_fallback_message", "I need to collect some information to better assist you.")
        return default_messages.get(category, fallback_message)
    
    def process_form_input(self, session_id: str, user_input: str) -> str:
        """处理表单输入"""
        if session_id not in self.active_forms:
            expired_message = self.form_config.get("form_prompts", {}).get("form_session_expired", "Form session has expired, please start again.")
            return expired_message
        
        form_state = self.active_forms[session_id]
        current_field = form_state["current_field"]
        
        # 验证当前字段输入
        is_valid, error_message = self._validate_field_input(current_field, user_input)
        
        if not is_valid:
            return f"{error_message}\n\n{self._get_field_prompt(current_field)}"
        
        # 保存有效输入
        form_state["collected_data"][current_field] = user_input
        
        # 获取下一个字段
        next_field = self._get_next_field(session_id)
        
        if next_field:
            # 继续收集下一个字段
            form_state["current_field"] = next_field
            recorded_message = self.form_config.get("form_prompts", {}).get("field_recorded", "✅ Recorded.")
            return f"{recorded_message}\n\n{self._get_field_prompt(next_field)}"
        else:
            # 表单完成，提交数据
            return self._submit_form(session_id)
    
    def _validate_field_input(self, field_name: str, user_input: str) -> Tuple[bool, str]:
        """验证字段输入"""
        field_config = self.form_config["form_fields"].get(field_name, {})
        
        if not field_config:
            return True, ""
        
        # 检查必填字段
        if field_config.get("required", False) and not user_input.strip():
            return False, field_config.get("error_message", "此字段为必填项")
        
        # 检查字段类型和验证规则
        field_type = field_config.get("type", "text")
        
        if field_type == "email":
            pattern = field_config.get("validation", r"^[\w\.-]+@[\w\.-]+\.[a-zA-Z]{2,}$")
            if not re.match(pattern, user_input):
                default_error = self.form_config.get("form_prompts", {}).get("validation_failed_default", "Email format is incorrect")
                return False, field_config.get("error_message", default_error)
        
        elif field_type == "select":
            options = field_config.get("options", [])
            if user_input not in options:
                template = self.form_config.get("form_prompts", {}).get("validation_failed_select", "Please select one of the following options: {options}")
                return False, template.format(options=', '.join(options))
        
        elif field_type == "textarea":
            min_length = field_config.get("min_length", 0)
            max_length = field_config.get("max_length", 1000)
            if len(user_input) < min_length:
                template = self.form_config.get("form_prompts", {}).get("validation_failed_textarea_min", "Description must be at least {min_length} characters")
                return False, template.format(min_length=min_length)
            if len(user_input) > max_length:
                template = self.form_config.get("form_prompts", {}).get("validation_failed_textarea_max", "Description cannot exceed {max_length} characters")
                return False, template.format(max_length=max_length)
        
        elif field_type == "text":
            validation = field_config.get("validation")
            if validation and not re.match(validation, user_input):
                default_error = self.form_config.get("form_prompts", {}).get("validation_failed_default", "Input format is incorrect")
                return False, field_config.get("error_message", default_error)
        
        return True, ""
    
    def _get_field_prompt(self, field_name: str) -> str:
        """获取字段提示信息"""
        field_config = self.form_config["form_fields"].get(field_name, {})
        label = field_config.get("label", field_name)
        field_type = field_config.get("type", "text")
        placeholder = field_config.get("placeholder", "")

        # 从配置文件获取提示模板
        form_prompts = self.form_config.get("form_prompts", {})
        prompt_template = form_prompts.get("field_prompt_template", "Please provide your {label}:")
        prompt = prompt_template.format(label=label)

        # 添加特殊提示
        if field_type == "select":
            options = field_config.get("options", [])
            options_template = form_prompts.get("select_options_template", "\nOptions: {options}")
            prompt += options_template.format(options=', '.join(options))
        elif field_type == "email":
            email_hint = form_prompts.get("email_hint", "\n(Please enter a valid email address)")
            prompt += email_hint
        elif field_type == "textarea":
            min_length = field_config.get("min_length", 0)
            if min_length > 0:
                textarea_hint = form_prompts.get("textarea_hint", "\n(Please describe in detail, minimum {min_length} characters)")
                prompt += textarea_hint.format(min_length=min_length)

        # 添加placeholder示例（如果存在）
        if placeholder:
            prompt += f"\n{placeholder}"

        return prompt
    
    def _get_next_field(self, session_id: str) -> Optional[str]:
        """获取下一个需要收集的字段"""
        form_state = self.active_forms[session_id]
        field_order = form_state["field_order"]
        collected_data = form_state["collected_data"]
        
        for field in field_order:
            if field not in collected_data:
                return field
        
        return None
    
    def _submit_form(self, session_id: str) -> str:
        """提交表单数据"""
        form_state = self.active_forms[session_id]
        collected_data = form_state["collected_data"]
        
        try:
            # 保存表单数据到数据库
            self._save_form_submission(session_id, form_state)
            
            # 推送到客服端
            self._notify_customer_service(session_id, form_state)
            
            # 清理表单状态
            del self.active_forms[session_id]
            
            # 返回成功消息
            success_message = self.form_config["form_submission"]["success_message"]
            logger.info(f"表单提交成功: {session_id}")
            
            return success_message
            
        except Exception as e:
            logger.error(f"表单提交失败: {e}")
            return "提交过程中出现错误，请稍后重试或联系客服。"
    
    def _save_form_submission(self, session_id: str, form_state: Dict[str, Any]):
        """保存完整表单数据到专门的表单表（聊天过程已自然保存在chat_messages中）"""
        try:
            # 使用同步数据库连接
            from services.database import get_db_service
            from models.chat_models import FormSubmission

            db_service = get_db_service()
            with db_service.sync_session() as db:
                # 保存到专门的表单表（用于客服端管理）
                form_submission = FormSubmission(
                    session_id=session_id,
                    category=form_state["category"],
                    form_data=form_state["collected_data"],
                    status=0,  # 默认未处理状态
                    priority=self._get_priority_level(form_state["category"]),
                    customer_email=form_state["collected_data"].get("email"),
                    customer_order=form_state["collected_data"].get("order_number"),
                    submitted_at=datetime.now()
                )

                db.add(form_submission)
                db.commit()
                db.refresh(form_submission)

                logger.info(f"表单数据已保存到form_submissions表: {session_id}, ID: {form_submission.id}")
                logger.info(f"表单详情: 邮箱={form_submission.customer_email}, 订单={form_submission.customer_order}, 类别={form_submission.category}")
                return form_submission.id

        except Exception as e:
            logger.error(f"保存表单数据失败: {e}")
            raise


    
    def _notify_customer_service(self, session_id: str, form_state: Dict[str, Any]):
        """通知客服端有新的表单提交"""
        try:
            # 这里应该通过WebSocket或其他方式通知客服端
            # 暂时记录日志
            notification_data = {
                "type": "form_submission",
                "session_id": session_id,
                "category": form_state["category"],
                "priority": self._get_priority_level(form_state["category"]),
                "customer_info": {
                    "email": form_state["collected_data"].get("email"),
                    "order_number": form_state["collected_data"].get("order_number"),
                    "region": form_state["collected_data"].get("region")
                },
                "issue_summary": form_state["collected_data"].get("issue_description", "")[:100],
                "submitted_at": datetime.now().isoformat()
            }
            
            logger.info(f"🔔 客服通知: {json.dumps(notification_data, ensure_ascii=False)}")
            logger.info(f"表单提交成功: {session_id}")

            # 通过WebSocket通知客服端
            self._send_websocket_notification(notification_data)
            
        except Exception as e:
            logger.error(f"通知客服端失败: {e}")

    def _send_websocket_notification(self, notification_data: Dict[str, Any]):
        """发送WebSocket通知到客服端"""
        try:
            # 导入WebSocket管理器
            import asyncio
            from main import manager

            # 创建异步任务发送通知
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，创建任务
                asyncio.create_task(
                    manager.broadcast_to_admins(json.dumps(notification_data, ensure_ascii=False))
                )
            else:
                # 如果没有运行的事件循环，直接运行
                asyncio.run(
                    manager.broadcast_to_admins(json.dumps(notification_data, ensure_ascii=False))
                )

            logger.info(f"WebSocket通知已发送: {notification_data['type']}")

        except Exception as e:
            logger.error(f"发送WebSocket通知失败: {e}")

    def _get_priority_level(self, category: str) -> str:
        """获取问题优先级"""
        priority_map = {
            "refund_request": "high",
            "replacement_request": "medium", 
            "technical_malfunction": "medium",
            "complex_technical": "low"
        }
        return priority_map.get(category, "medium")
    
    def is_form_active(self, session_id: str) -> bool:
        """检查是否有活跃的表单会话"""
        return session_id in self.active_forms
    
    def cancel_form(self, session_id: str) -> str:
        """取消表单收集"""
        if session_id in self.active_forms:
            del self.active_forms[session_id]
            return "表单收集已取消。如需帮助，请重新描述您的问题。"
        return "没有进行中的表单收集。"
    
    def get_form_progress(self, session_id: str) -> Dict[str, Any]:
        """获取表单收集进度"""
        if session_id not in self.active_forms:
            return {}
        
        form_state = self.active_forms[session_id]
        total_fields = len(form_state["field_order"])
        completed_fields = len(form_state["collected_data"])
        
        return {
            "total_fields": total_fields,
            "completed_fields": completed_fields,
            "progress_percentage": (completed_fields / total_fields) * 100,
            "current_field": form_state["current_field"],
            "category": form_state["category"]
        }


# 全局实例
form_service = FormService()


def get_form_service() -> FormService:
    """获取表单服务实例"""
    return form_service
