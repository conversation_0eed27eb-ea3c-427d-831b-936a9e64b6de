# YF AI Chat Shopify Plugin - 发布说明

## 🔒 v2.1.6 - 安全增强版本 (2025-01-04)

### 🛡️ 安全修复 (重要更新)
- **移除敏感日志**: 完全移除所有可能暴露API URL、Token、Session ID的console.log输出
- **隐藏网络请求**: 防止在浏览器控制台中暴露敏感的网络请求信息
- **保护用户隐私**: 移除可能泄露用户会话和消息内容的调试信息
- **生产环境优化**: 适合生产环境部署，无调试信息泄露风险

### ✅ 继承的修复
- **移除测试按钮**: 删除了扳手悬浮测试按钮，适合生产环境使用
- **语法错误修复**: 解决了 "Illegal return statement" 错误，确保JavaScript正常执行
- **配置验证优化**: 改进了配置加载和验证逻辑，提高稳定性
- **API地址修正**: 恢复正确的Cloudflare隧道地址配置

### 🔧 技术改进
- 增强错误处理机制
- 优化初始化流程
- 清理调试代码
- 提高代码质量

### 📁 更新的文件
- `assets/yf-chat-shopify.js` - 移除所有敏感信息日志输出
- `snippets/yf-chat-widget.liquid` - 移除配置和购物车相关日志

### 🎯 升级指南 (强烈推荐)
1. **立即更新**: 下载最新的 `yf-chat-shopify.js` 和 `yf-chat-widget.liquid`
2. **上传文件**: 上传到您的Shopify主题对应目录
3. **清除缓存**: 清除浏览器缓存 (Ctrl+F5)
4. **安全验证**: 打开浏览器控制台，确认没有敏感信息显示
5. **功能测试**: 验证聊天功能正常工作

---

## 📋 历史版本

### v2.1.5 (2025-01-04)
- 移除测试按钮，生产就绪版本
- 修复语法错误和配置验证

### v2.1.4 (2025-01-04)
- 修复语法错误和配置验证
- 增强错误处理

### v2.1.3 (2024-12-XX)
- 修复按钮显示问题
- 增强CSS优先级

### v2.1.2 (2024-12-XX)
- 安全增强版本
- 清理控制台日志输出

### v2.1.1 (2024-12-XX)
- 基础功能实现
- WordPress功能移植

---

## 🆘 支持

如果遇到问题，请参考：
- `TROUBLESHOOTING.md` - 故障排除指南
- `test.html` - 本地测试页面
- `README.md` - 完整安装说明

## 📞 联系方式

如需技术支持，请提供：
1. 浏览器控制台错误信息
2. Shopify主题名称和版本
3. 具体的问题描述
