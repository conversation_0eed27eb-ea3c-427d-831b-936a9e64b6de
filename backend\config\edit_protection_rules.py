#!/usr/bin/env python3
"""
防护规则编辑器
用于编辑和管理API防护规则配置文件
支持实时热重载，无需重启后端服务
"""

import json
import os
import sys
from pathlib import Path
from datetime import datetime

class ProtectionRulesEditor:
    def __init__(self):
        self.config_file = Path(__file__).parent / "protection_rules.json"
        self.config = {}
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                print(f"✅ 配置文件加载成功: {self.config_file}")
            else:
                print(f"❌ 配置文件不存在: {self.config_file}")
                return False
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            return False
        return True
    
    def save_config(self):
        """保存配置文件"""
        try:
            # 更新最后修改时间
            self.config["last_updated"] = datetime.now().isoformat() + "Z"
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            print(f"✅ 配置文件保存成功: {self.config_file}")
            print("🔄 后端服务将自动重新加载配置（无需重启）")
            return True
        except Exception as e:
            print(f"❌ 保存配置文件失败: {e}")
            return False
    
    def show_status(self):
        """显示当前状态"""
        print("\n" + "="*50)
        print("🛡️  API防护系统状态")
        print("="*50)
        print(f"配置文件: {self.config_file}")
        print(f"防护启用: {'✅ 是' if self.config.get('enabled', True) else '❌ 否'}")
        print(f"最后更新: {self.config.get('last_updated', '未知')}")
        
        # 显示各模块状态
        rate_limiting = self.config.get("rate_limiting", {})
        print(f"频率限制: {'✅ 启用' if rate_limiting.get('enabled', True) else '❌ 禁用'}")
        print(f"  - 每分钟最大请求: {rate_limiting.get('max_requests_per_minute', 10)}")
        print(f"  - 每小时最大请求: {rate_limiting.get('max_requests_per_hour', 100)}")
        
        ip_blacklist = self.config.get("ip_blacklist", {})
        blocked_ips = ip_blacklist.get("ips", [])
        print(f"IP黑名单: {'✅ 启用' if ip_blacklist.get('enabled', True) else '❌ 禁用'}")
        print(f"  - 已封禁IP数量: {len(blocked_ips)}")
        
        content_filtering = self.config.get("content_filtering", {})
        spam_patterns = content_filtering.get("spam_patterns", [])
        print(f"内容过滤: {'✅ 启用' if content_filtering.get('enabled', True) else '❌ 禁用'}")
        print(f"  - 垃圾模式数量: {len(spam_patterns)}")
        
        llm_abuse = self.config.get("llm_abuse_protection", {})
        suspicious_patterns = llm_abuse.get("suspicious_patterns", [])
        print(f"LLM滥用防护: {'✅ 启用' if llm_abuse.get('enabled', True) else '❌ 禁用'}")
        print(f"  - 可疑模式数量: {len(suspicious_patterns)}")
    
    def toggle_protection(self):
        """切换防护开关"""
        current = self.config.get("enabled", True)
        self.config["enabled"] = not current
        status = "启用" if self.config["enabled"] else "禁用"
        print(f"🔄 防护系统已{status}")
        return self.save_config()
    
    def manage_ip_blacklist(self):
        """管理IP黑名单"""
        while True:
            print("\n" + "-"*30)
            print("🚫 IP黑名单管理")
            print("-"*30)
            
            blacklist = self.config.get("ip_blacklist", {})
            ips = blacklist.get("ips", [])
            
            if ips:
                print("当前黑名单:")
                for i, ip in enumerate(ips, 1):
                    print(f"  {i}. {ip}")
            else:
                print("黑名单为空")
            
            print("\n选项:")
            print("1. 添加IP")
            print("2. 删除IP")
            print("3. 清空黑名单")
            print("0. 返回主菜单")
            
            choice = input("\n请选择操作: ").strip()
            
            if choice == "1":
                ip = input("请输入要封禁的IP地址: ").strip()
                if ip and ip not in ips:
                    ips.append(ip)
                    self.config.setdefault("ip_blacklist", {})["ips"] = ips
                    print(f"✅ IP {ip} 已添加到黑名单")
                    self.save_config()
                elif ip in ips:
                    print(f"⚠️  IP {ip} 已在黑名单中")
                else:
                    print("❌ 无效的IP地址")
            
            elif choice == "2":
                if not ips:
                    print("❌ 黑名单为空")
                    continue
                
                try:
                    index = int(input("请输入要删除的IP编号: ")) - 1
                    if 0 <= index < len(ips):
                        removed_ip = ips.pop(index)
                        self.config.setdefault("ip_blacklist", {})["ips"] = ips
                        print(f"✅ IP {removed_ip} 已从黑名单移除")
                        self.save_config()
                    else:
                        print("❌ 无效的编号")
                except ValueError:
                    print("❌ 请输入有效的数字")
            
            elif choice == "3":
                confirm = input("确认清空所有黑名单IP? (y/N): ").strip().lower()
                if confirm == 'y':
                    self.config.setdefault("ip_blacklist", {})["ips"] = []
                    print("✅ 黑名单已清空")
                    self.save_config()
            
            elif choice == "0":
                break
    
    def adjust_rate_limits(self):
        """调整频率限制"""
        print("\n" + "-"*30)
        print("⏱️  频率限制设置")
        print("-"*30)
        
        rate_config = self.config.get("rate_limiting", {})
        
        print(f"当前设置:")
        print(f"  启用状态: {'✅ 是' if rate_config.get('enabled', True) else '❌ 否'}")
        print(f"  每分钟最大请求: {rate_config.get('max_requests_per_minute', 10)}")
        print(f"  每小时最大请求: {rate_config.get('max_requests_per_hour', 100)}")
        print(f"  封禁时长(分钟): {rate_config.get('ban_duration_minutes', 60)}")
        
        print("\n选项:")
        print("1. 切换启用状态")
        print("2. 设置每分钟最大请求数")
        print("3. 设置每小时最大请求数")
        print("4. 设置封禁时长")
        print("0. 返回主菜单")
        
        choice = input("\n请选择操作: ").strip()
        
        if choice == "1":
            current = rate_config.get("enabled", True)
            self.config.setdefault("rate_limiting", {})["enabled"] = not current
            status = "启用" if not current else "禁用"
            print(f"✅ 频率限制已{status}")
            self.save_config()
        
        elif choice == "2":
            try:
                value = int(input("请输入每分钟最大请求数 (1-100): "))
                if 1 <= value <= 100:
                    self.config.setdefault("rate_limiting", {})["max_requests_per_minute"] = value
                    print(f"✅ 每分钟最大请求数已设置为 {value}")
                    self.save_config()
                else:
                    print("❌ 值必须在1-100之间")
            except ValueError:
                print("❌ 请输入有效的数字")
        
        elif choice == "3":
            try:
                value = int(input("请输入每小时最大请求数 (10-1000): "))
                if 10 <= value <= 1000:
                    self.config.setdefault("rate_limiting", {})["max_requests_per_hour"] = value
                    print(f"✅ 每小时最大请求数已设置为 {value}")
                    self.save_config()
                else:
                    print("❌ 值必须在10-1000之间")
            except ValueError:
                print("❌ 请输入有效的数字")
        
        elif choice == "4":
            try:
                value = int(input("请输入封禁时长(分钟) (5-1440): "))
                if 5 <= value <= 1440:
                    self.config.setdefault("rate_limiting", {})["ban_duration_minutes"] = value
                    print(f"✅ 封禁时长已设置为 {value} 分钟")
                    self.save_config()
                else:
                    print("❌ 值必须在5-1440分钟之间")
            except ValueError:
                print("❌ 请输入有效的数字")
    
    def run(self):
        """运行编辑器主循环"""
        if not self.load_config():
            return
        
        print("🛡️  API防护规则编辑器")
        print("支持实时热重载，修改后无需重启后端服务")
        
        while True:
            self.show_status()
            
            print("\n" + "="*50)
            print("📋 操作菜单")
            print("="*50)
            print("1. 切换防护开关")
            print("2. 管理IP黑名单")
            print("3. 调整频率限制")
            print("4. 重新加载配置")
            print("5. 查看配置文件")
            print("0. 退出")
            
            choice = input("\n请选择操作 (0-5): ").strip()
            
            if choice == "1":
                self.toggle_protection()
            
            elif choice == "2":
                self.manage_ip_blacklist()
            
            elif choice == "3":
                self.adjust_rate_limits()
            
            elif choice == "4":
                if self.load_config():
                    print("✅ 配置重新加载成功")
                else:
                    print("❌ 配置重新加载失败")
            
            elif choice == "5":
                print(f"\n配置文件路径: {self.config_file}")
                print("可以使用文本编辑器直接编辑此文件")
                print("修改后会自动被后端服务检测并重新加载")
            
            elif choice == "0":
                print("👋 再见！")
                break
            
            else:
                print("❌ 无效的选择，请重试")

if __name__ == "__main__":
    editor = ProtectionRulesEditor()
    try:
        editor.run()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，再见！")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        sys.exit(1)
