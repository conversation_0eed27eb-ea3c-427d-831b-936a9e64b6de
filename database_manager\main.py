#!/usr/bin/env python3
"""
YF AI Chat 数据库管理器
主程序入口
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    
    try:
        import PyQt6
    except ImportError:
        missing_packages.append("PyQt6")
    
    try:
        import pandas
    except ImportError:
        missing_packages.append("pandas")
    
    try:
        import sqlite3
    except ImportError:
        missing_packages.append("sqlite3")
    
    if missing_packages:
        error_msg = f"缺少必要的依赖包: {', '.join(missing_packages)}\n\n"
        error_msg += "请运行以下命令安装:\n"
        error_msg += f"pip install {' '.join(missing_packages)}"
        
        print(error_msg)
        return False
    
    return True

def setup_application():
    """设置应用程序"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("YF AI Chat 数据库管理器")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("YF AI Chat")
    
    # 设置应用程序图标（如果存在）
    icon_path = project_root / "resources" / "database_icon.ico"
    if not icon_path.exists():
        # 如果没有专门的图标，使用backend的图标
        icon_path = project_root.parent / "backend" / "resources" / "text_icon.ico"
    
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    # 设置高DPI支持 (PyQt6中这些属性已经默认启用)
    try:
        app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    except AttributeError:
        # PyQt6中这些属性可能不存在或已默认启用
        pass
    
    return app

def main():
    """主函数"""
    print("🔐 YF AI Chat 数据库管理器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return 1
    
    try:
        # 创建应用程序
        app = setup_application()
        
        # 导入主窗口（延迟导入以确保PyQt6已正确加载）
        from gui.main_window import MainWindow
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        print("✅ 数据库管理器已启动")
        print("📋 功能说明:")
        print("  - 数据库连接和管理")
        print("  - 表格数据查看和编辑")
        print("  - SQL查询执行")
        print("  - 数据导出到CSV/Excel/JSON")
        print("  - 配置自动保存")
        print()
        
        # 运行应用程序
        return app.exec()
        
    except ImportError as e:
        error_msg = f"导入模块失败: {e}\n\n"
        error_msg += "请确保所有依赖包已正确安装。"
        
        print(f"❌ {error_msg}")
        
        # 如果PyQt6可用，显示图形错误对话框
        try:
            app = QApplication(sys.argv)
            QMessageBox.critical(None, "启动错误", error_msg)
        except:
            pass
        
        input("按回车键退出...")
        return 1
        
    except Exception as e:
        error_msg = f"启动失败: {e}"
        print(f"❌ {error_msg}")
        
        try:
            QMessageBox.critical(None, "启动错误", error_msg)
        except:
            pass
        
        input("按回车键退出...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
