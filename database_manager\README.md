# 🔐 YF AI Chat 数据库管理器

专为YF AI Chat后端数据库设计的图形化管理工具，提供数据查看、编辑、查询和导出功能。

## ✨ 主要功能

### 🔗 数据库连接管理
- 支持SQLite数据库文件连接
- 数据库路径历史记录和自动保存
- 快速连接后端数据库
- 连接前数据库信息预览
- 可选只读模式和备份功能

### 📊 数据表格查看
- 分页显示大量数据
- 支持数据筛选和排序
- 实时编辑单元格数据
- 记录的增删改操作
- 表结构信息显示

### 💻 SQL查询执行
- 语法高亮的SQL编辑器
- 查询历史记录管理
- 预设常用查询模板
- 查询结果表格显示
- 支持多种SQL语句执行

### 📤 数据导出功能
- 导出到CSV、Excel、JSON格式
- 自定义导出条件和限制
- 可配置编码和分隔符
- 数据预览功能
- 批量导出支持

### ⚙️ 配置管理
- 自动保存用户配置
- 窗口位置和大小记忆
- 导出设置保存
- 查询历史管理

## 🚀 快速开始

### 方式一：运行打包好的exe文件
1. 下载 `YF_AI_Database_Manager.exe`
2. 双击运行
3. 点击"连接数据库"选择数据库文件
4. 开始使用各项功能

### 方式二：从源码运行
1. 安装依赖包：
   ```bash
   pip install PyQt6 pandas
   ```

2. 运行程序：
   ```bash
   python main.py
   ```

### 方式三：自己打包
1. 安装打包依赖：
   ```bash
   pip install PyQt6 pandas pyinstaller
   ```

2. 运行打包脚本：
   ```bash
   python build_database_manager.py
   ```
   或者直接运行：
   ```bash
   build.bat
   ```

## 📋 支持的数据库表

本工具专门针对YF AI Chat后端数据库设计，支持以下表：

- **chat_sessions** - 聊天会话表
- **chat_messages** - 聊天消息表
- **session_read_status** - 会话已读状态表
- **form_submissions** - 表单提交表
- **user_preferences** - 用户偏好表

## 🎯 使用场景

### 数据分析
- 查看聊天会话统计
- 分析用户消息模式
- 监控表单提交情况

### 数据维护
- 清理过期数据
- 修正错误记录
- 批量数据更新

### 数据导出
- 生成报表数据
- 备份重要信息
- 数据迁移准备

### 开发调试
- 查看实时数据变化
- 验证功能正确性
- 排查数据问题

## 📁 项目结构

```
database_manager/
├── main.py                 # 主程序入口
├── gui/                    # GUI组件
│   ├── main_window.py      # 主窗口
│   ├── table_viewer.py     # 表格查看器
│   ├── query_editor.py     # SQL查询编辑器
│   ├── connection_dialog.py # 数据库连接对话框
│   └── export_dialog.py    # 导出对话框
├── core/                   # 核心功能
│   ├── database.py         # 数据库操作
│   ├── config.py           # 配置管理
│   └── export.py           # 导出功能
├── build_database_manager.py # 打包脚本
├── build.bat               # 打包批处理
├── requirements.txt        # 依赖包列表
└── README.md              # 说明文档
```

## ⚠️ 注意事项

1. **数据安全**
   - 建议在操作生产数据库前先备份
   - 可使用只读模式避免意外修改
   - 重要操作前会有确认提示

2. **性能考虑**
   - 大表数据采用分页显示
   - 可设置查询记录限制
   - 避免无条件查询大表

3. **兼容性**
   - 仅支持SQLite数据库
   - 需要Python 3.8+
   - Windows 10+推荐

## 🔧 技术栈

- **GUI框架**: PyQt6
- **数据处理**: pandas
- **数据库**: sqlite3
- **打包工具**: PyInstaller
- **配置管理**: JSON

## 📝 更新日志

### v1.0.0 (2025-07-08)
- ✅ 初始版本发布
- ✅ 基础数据库连接功能
- ✅ 表格数据查看和编辑
- ✅ SQL查询执行器
- ✅ 数据导出功能
- ✅ 配置管理系统

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📄 许可证

本项目采用MIT许可证。
