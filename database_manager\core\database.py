#!/usr/bin/env python3
"""
数据库操作模块
负责SQLite数据库的连接、查询、更新等操作
"""

import sqlite3
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import json

class DatabaseManager:
    def __init__(self):
        self.connection = None
        self.database_path = None
        self.is_connected = False
    
    def connect(self, database_path: str) -> bool:
        """连接到数据库"""
        try:
            if self.connection:
                self.connection.close()
            
            self.database_path = database_path
            self.connection = sqlite3.connect(database_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row  # 使结果可以按列名访问
            self.is_connected = True
            return True
        except Exception as e:
            self.is_connected = False
            raise Exception(f"连接数据库失败: {e}")
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
        self.is_connected = False
        self.database_path = None
    
    def get_tables(self) -> List[str]:
        """获取数据库中的所有表名"""
        if not self.is_connected:
            return []
        
        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
            tables = [row[0] for row in cursor.fetchall()]
            return tables
        except Exception as e:
            raise Exception(f"获取表列表失败: {e}")
    
    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """获取表结构信息"""
        if not self.is_connected:
            return []
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = []
            for row in cursor.fetchall():
                columns.append({
                    'cid': row[0],
                    'name': row[1],
                    'type': row[2],
                    'notnull': bool(row[3]),
                    'default_value': row[4],
                    'pk': bool(row[5])
                })
            return columns
        except Exception as e:
            raise Exception(f"获取表结构失败: {e}")
    
    def get_table_data(self, table_name: str, limit: int = 100, offset: int = 0, 
                      where_clause: str = "", order_by: str = "") -> pd.DataFrame:
        """获取表数据"""
        if not self.is_connected:
            return pd.DataFrame()
        
        try:
            query = f"SELECT * FROM {table_name}"
            
            if where_clause:
                query += f" WHERE {where_clause}"
            
            if order_by:
                query += f" ORDER BY {order_by}"
            
            if limit > 0:
                query += f" LIMIT {limit} OFFSET {offset}"
            
            df = pd.read_sql_query(query, self.connection)
            return df
        except Exception as e:
            raise Exception(f"获取表数据失败: {e}")
    
    def get_table_count(self, table_name: str, where_clause: str = "") -> int:
        """获取表记录数"""
        if not self.is_connected:
            return 0
        
        try:
            query = f"SELECT COUNT(*) FROM {table_name}"
            if where_clause:
                query += f" WHERE {where_clause}"
            
            cursor = self.connection.cursor()
            cursor.execute(query)
            return cursor.fetchone()[0]
        except Exception as e:
            raise Exception(f"获取记录数失败: {e}")
    
    def execute_query(self, query: str) -> Tuple[pd.DataFrame, str]:
        """执行SQL查询"""
        if not self.is_connected:
            return pd.DataFrame(), "数据库未连接"
        
        try:
            query = query.strip()
            
            # 判断是否是SELECT查询
            if query.upper().startswith('SELECT'):
                df = pd.read_sql_query(query, self.connection)
                return df, f"查询成功，返回 {len(df)} 行记录"
            else:
                # 执行非SELECT语句
                cursor = self.connection.cursor()
                cursor.execute(query)
                self.connection.commit()
                
                affected_rows = cursor.rowcount
                return pd.DataFrame(), f"执行成功，影响 {affected_rows} 行记录"
                
        except Exception as e:
            return pd.DataFrame(), f"查询执行失败: {e}"
    
    def update_record(self, table_name: str, record_id: Any, column: str, value: Any) -> bool:
        """更新单个记录的字段值"""
        if not self.is_connected:
            return False
        
        try:
            # 获取主键列名
            table_info = self.get_table_info(table_name)
            pk_column = None
            for col in table_info:
                if col['pk']:
                    pk_column = col['name']
                    break
            
            if not pk_column:
                raise Exception("未找到主键列")
            
            cursor = self.connection.cursor()
            cursor.execute(f"UPDATE {table_name} SET {column} = ? WHERE {pk_column} = ?", 
                         (value, record_id))
            self.connection.commit()
            return True
        except Exception as e:
            raise Exception(f"更新记录失败: {e}")
    
    def delete_record(self, table_name: str, record_id: Any) -> bool:
        """删除记录"""
        if not self.is_connected:
            return False
        
        try:
            # 获取主键列名
            table_info = self.get_table_info(table_name)
            pk_column = None
            for col in table_info:
                if col['pk']:
                    pk_column = col['name']
                    break
            
            if not pk_column:
                raise Exception("未找到主键列")
            
            cursor = self.connection.cursor()
            cursor.execute(f"DELETE FROM {table_name} WHERE {pk_column} = ?", (record_id,))
            self.connection.commit()
            return True
        except Exception as e:
            raise Exception(f"删除记录失败: {e}")
    
    def insert_record(self, table_name: str, data: Dict[str, Any]) -> bool:
        """插入新记录"""
        if not self.is_connected:
            return False
        
        try:
            columns = list(data.keys())
            values = list(data.values())
            placeholders = ', '.join(['?' for _ in values])
            
            query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
            
            cursor = self.connection.cursor()
            cursor.execute(query, values)
            self.connection.commit()
            return True
        except Exception as e:
            raise Exception(f"插入记录失败: {e}")
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库基本信息"""
        if not self.is_connected:
            return {}
        
        try:
            info = {
                'path': self.database_path,
                'size': Path(self.database_path).stat().st_size if Path(self.database_path).exists() else 0,
                'tables': len(self.get_tables()),
                'version': self.connection.execute("SELECT sqlite_version()").fetchone()[0]
            }
            return info
        except Exception as e:
            return {'error': str(e)}

# 全局数据库管理器实例
db_manager = DatabaseManager()
