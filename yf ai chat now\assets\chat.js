jQuery(document).ready(function($) {
    'use strict';

    // 🔒 安全模式：移除敏感信息日志
    console.log('YF AI Chat: Initializing...');

    // 检查yfAiChat配置是否存在
    if (typeof yfAiChat === 'undefined') {
        console.error('YF AI Chat: Configuration not found. Chat widget cannot be initialized.');
        return;
    }

    // 🔒 安全检查：只验证配置存在性，不暴露具体内容
    console.log('YF AI Chat: Configuration loaded successfully');

    class YFAIChat {
        constructor() {
            // 🔒 安全模式：移除配置详情日志
            console.log('YFAIChat: Starting initialization...');

            // 🔒 安全的配置检查，不暴露敏感信息
            const configStatus = {
                apiUrl: yfAiChat.apiUrl ? 'OK' : 'MISSING',
                apiToken: yfAiChat.apiToken ? 'OK' : 'MISSING',
                ajaxUrl: yfAiChat.ajaxUrl ? 'OK' : 'MISSING',
                nonce: yfAiChat.nonce ? 'OK' : 'MISSING'
            };

            // 只在有配置问题时显示错误
            if (!yfAiChat.apiUrl || !yfAiChat.apiToken) {
                console.error('YFAIChat: Configuration incomplete. Please check plugin settings.');
                return;
            }

            console.log('YFAIChat: Configuration validated successfully');

            this.isOpen = false;
            this.isTyping = false;
            this.sessionTimeout = yfAiChat.settings.sessionTimeout || (30 * 60 * 1000); // 会话超时时间
            this.saveHistoryEnabled = yfAiChat.settings.saveHistory !== 0;
            this.lastMessageTime = null; // 记录最后一条消息的时间
            this.pollInterval = null; // 轮询定时器
            this.messageIds = new Set(); // 用于消息去重的ID集合
            this.emptyResponseCount = 0; // 空响应计数

            // 🔒 安全模式：移除配置详情日志
            console.log('YFAIChat: Session configuration loaded');

            // 重新设计：先尝试加载历史记录，如果有则使用其会话ID，否则创建新会话
            this.initializeSession().then(() => {
                this.init();
            }).catch(error => {
                console.error('Session initialization failed:', error);
                // 如果初始化失败，回退到基本初始化
                this.sessionId = this.generateSessionId();
                this.chatHistory = [];
                this.init();
            });
        }
        
        init() {
            this.createWidget();
            this.bindEvents();
            this.setPosition();
            // 不在初始化时就开始轮询，而是在用户发送第一条消息后开始
        }
        
        generateSessionId() {
            // 生成基于浏览器指纹的持久化会话ID
            return this.generatePersistentSessionId();
        }

        generatePersistentSessionId() {
            // 检查是否已有持久化的会话ID
            const existingId = localStorage.getItem('yf_chat_persistent_session_id');
            if (existingId) {
                // 🔒 安全模式：不显示完整session ID
                console.log('YFAIChat: Using existing session');
                return existingId;
            }

            // 生成浏览器指纹
            const fingerprint = this.generateBrowserFingerprint();

            // 生成持久化会话ID：前缀 + 指纹哈希 + 时间戳（首次创建）
            const timestamp = Date.now();
            const sessionId = 'session_' + fingerprint + '_' + timestamp;

            // 保存到localStorage，实现持久化
            localStorage.setItem('yf_chat_persistent_session_id', sessionId);
            // 🔒 安全模式：不显示完整session ID
            console.log('YFAIChat: Generated new session');

            return sessionId;
        }

        generateBrowserFingerprint() {
            // 收集浏览器特征信息
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('Browser fingerprint', 2, 2);

            const fingerprint = [
                navigator.userAgent,
                navigator.language,
                screen.width + 'x' + screen.height,
                screen.colorDepth,
                new Date().getTimezoneOffset(),
                navigator.platform,
                navigator.cookieEnabled,
                typeof(navigator.doNotTrack) !== 'undefined' ? navigator.doNotTrack : 'unknown',
                canvas.toDataURL()
            ].join('|');

            // 生成简短的哈希值
            return this.simpleHash(fingerprint).toString(36);
        }

        simpleHash(str) {
            let hash = 0;
            if (str.length === 0) return hash;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转换为32位整数
            }
            return Math.abs(hash);
        }

        async initializeSession() {
            // 🔒 安全模式：移除session ID日志
            console.log('YFAIChat: Initializing session...');

            // 生成或获取持久化会话ID
            this.sessionId = this.generateSessionId();

            if (!this.saveHistoryEnabled) {
                console.log('YFAIChat: History saving disabled');
                this.chatHistory = [];
                return;
            }

            // 🔧 新逻辑：先检查后端是否有该session的历史记录
            const hasBackendHistory = await this.checkBackendSession();

            if (hasBackendHistory) {
                console.log('YFAIChat: Loading history from server...');
                await this.loadBackendHistory();
            } else {
                console.log('YFAIChat: Loading local history...');
                // 尝试加载本地聊天历史（基于持久化会话ID）
                this.loadChatHistoryForSession();
            }

            this.updateSessionInStorage();
        }

        loadChatHistoryForSession() {
            const historyKey = 'yf_chat_history_' + this.sessionId;
            const savedHistory = localStorage.getItem(historyKey);

            if (savedHistory) {
                try {
                    const historyData = JSON.parse(savedHistory);
                    // 🔒 安全模式：不显示session ID
                    console.log('YFAIChat: Found saved history');

                    // 检查历史记录是否还有效（未过期）
                    if (historyData.messages && historyData.lastUpdated) {
                        const now = Date.now();
                        const historyAge = now - historyData.lastUpdated;

                        // 如果历史记录在会话超时时间内，则恢复聊天记录
                        if (historyAge < this.sessionTimeout) {
                            console.log('YFAIChat: Restoring chat history');
                            this.chatHistory = historyData.messages || [];
                            return;
                        } else {
                            console.log('YFAIChat: History expired, clearing old data');
                            localStorage.removeItem(historyKey);
                        }
                    }
                } catch (e) {
                    console.error('YFAIChat: Error parsing saved history');
                    localStorage.removeItem(historyKey);
                }
            }

            // 如果没有有效的历史记录，初始化空历史
            console.log('YFAIChat: Starting fresh session');
            this.chatHistory = [];
        }

        async checkBackendSession() {
            /**
             * 检查后端数据库是否存在该session的记录
             * @returns {Promise<boolean>} 是否存在后端历史记录
             */
            try {
                // 🔒 安全模式：构建请求但不在日志中暴露URL和token
                const response = await $.ajax({
                    url: yfAiChat.apiUrl + '/api/session/' + this.sessionId + '/exists',
                    type: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + yfAiChat.apiToken || '',
                        'Cache-Control': 'no-cache'
                    },
                    timeout: 10000
                });

                if (response && response.exists && response.message_count > 0) {
                    console.log(`YFAIChat: Backend session exists with ${response.message_count} messages`);
                    return true;
                } else {
                    console.log('YFAIChat: No backend session found');
                    return false;
                }
            } catch (error) {
                console.warn('YFAIChat: Failed to check backend session');
                return false; // 如果检查失败，假设没有后端历史记录
            }
        }

        async loadBackendHistory() {
            /**
             * 从后端加载历史记录并按消息ID排序显示
             */
            try {
                // 🔒 安全模式：构建请求但不在日志中暴露URL和token
                const response = await $.ajax({
                    url: yfAiChat.apiUrl + '/api/session/' + this.sessionId + '/messages',
                    type: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + yfAiChat.apiToken || '',
                        'Cache-Control': 'no-cache'
                    },
                    timeout: 10000
                });

                if (response && response.messages) {
                    const messages = response.messages;
                    console.log(`YFAIChat: Loading ${messages.length} messages from backend`);

                    // 🔧 按消息ID排序（后端已经排序，但为了确保）
                    messages.sort((a, b) => a.id - b.id);

                    // 清空现有历史记录和消息ID集合
                    this.chatHistory = [];
                    this.messageIds.clear();

                    // 逐条添加消息到UI和历史记录
                    messages.forEach(msg => {
                        // 添加到去重集合
                        this.messageIds.add(msg.id);

                        // 直接添加到UI，使用消息ID确保正确排序
                        if (msg.sender === 'admin') {
                            this.addAdminMessageToUI(msg.content, false, msg.id);
                        } else {
                            this.addMessageToUI(msg.content, msg.sender, false, msg.id);
                        }

                        // 添加到本地历史记录（用于localStorage保存）
                        this.chatHistory.push({
                            content: msg.content,
                            sender: msg.sender,
                            timestamp: Date.now() // 使用当前时间作为本地时间戳
                        });
                    });

                    console.log(`YFAIChat: Backend history loaded: ${messages.length} messages, ${this.messageIds.size} unique IDs`);
                } else {
                    console.log('YFAIChat: No messages in backend response');
                }
            } catch (error) {
                console.error('YFAIChat: Failed to load backend history');
                // 如果加载失败，回退到本地历史记录
                this.loadChatHistoryForSession();
            }
        }

        updateSessionInStorage() {
            if (!this.saveHistoryEnabled) {
                return;
            }

            const sessionData = {
                sessionId: this.sessionId,
                createdAt: Date.now(),
                lastActivity: Date.now()
            };
            localStorage.setItem('yf_chat_session', JSON.stringify(sessionData));
            console.log('YFAIChat: Session data updated in storage');
        }



        updateSessionActivity() {
            if (!this.saveHistoryEnabled) {
                return;
            }

            const savedSession = localStorage.getItem('yf_chat_session');
            if (savedSession) {
                try {
                    const sessionData = JSON.parse(savedSession);
                    sessionData.lastActivity = Date.now();
                    localStorage.setItem('yf_chat_session', JSON.stringify(sessionData));
                    console.log('YFAIChat: Session activity updated');
                } catch (e) {
                    console.warn('YFAIChat: Failed to update session activity');
                    // 重新创建会话数据
                    this.updateSessionInStorage();
                }
            } else {
                // 如果会话数据丢失，重新创建
                this.updateSessionInStorage();
            }
        }

        clearSession() {
            localStorage.removeItem('yf_chat_session');
            // 清除当前会话的历史记录
            if (this.sessionId) {
                const historyKey = 'yf_chat_history_' + this.sessionId;
                localStorage.removeItem(historyKey);
            }
            // 清除消息ID集合，避免新会话中的消息被错误地认为是重复消息
            this.messageIds.clear();
            // 注意：不清除持久化会话ID，保持浏览器指纹一致性
        }

        // 这个方法现在已经不需要了，因为历史记录在 initializeSession 中处理
        // 保留这个方法是为了向后兼容
        loadChatHistory() {
            console.log('loadChatHistory called, but history already loaded in initializeSession');
            return this.chatHistory || [];
        }

        saveChatHistory() {
            if (!this.saveHistoryEnabled) {
                return;
            }

            const historyData = {
                messages: this.chatHistory,
                lastUpdated: Date.now()
            };
            const historyKey = 'yf_chat_history_' + this.sessionId;
            localStorage.setItem(historyKey, JSON.stringify(historyData));
            // 🔒 安全模式：不显示session ID
            console.log('YFAIChat: Chat history saved, messages:', historyData.messages.length);
        }
        
        createWidget() {
            console.log('YFAIChat: Creating widget...');

            try {
                const widget = $(`
                    <div class="yf-chat-widget" id="yf-chat-widget">
                        <button class="yf-chat-button" id="yf-chat-button">
                            💬
                        </button>
                        <div class="yf-chat-window" id="yf-chat-window">
                            <div class="yf-chat-header">
                                <div class="yf-chat-title">${yfAiChat.strings.title}</div>
                                <div class="yf-chat-header-buttons">
                                    <button class="yf-chat-clear" id="yf-chat-clear" title="清除聊天记录">🗑️</button>
                                    <button class="yf-chat-close" id="yf-chat-close">×</button>
                                </div>
                            </div>
                            <div class="yf-chat-messages" id="yf-chat-messages">
                            </div>
                            <div class="yf-chat-input-area">
                                <textarea class="yf-chat-input" id="yf-chat-input"
                                    placeholder="${yfAiChat.strings.placeholder}"
                                    rows="1"></textarea>
                                <button class="yf-chat-send" id="yf-chat-send">
                                    ➤
                                </button>
                            </div>
                        </div>
                    </div>
                `);

                console.log('YFAIChat: Widget HTML created, appending to body...');
                $('body').append(widget);

                console.log('YFAIChat: Widget appended to body');

                // 恢复聊天历史或显示欢迎消息
                this.restoreChatHistory();

                console.log('YFAIChat: Widget creation completed');
            } catch (error) {
                console.error('YFAIChat: Error creating widget:', error);
            }
        }

        restoreChatHistory() {
            const messagesContainer = $('#yf-chat-messages');

            console.log('YFAIChat: Restoring chat history, messages count:', this.chatHistory.length);

            if (this.chatHistory.length > 0) {
                // 恢复历史消息
                this.chatHistory.forEach((msg, index) => {
                    // 🔒 安全模式：移除详细消息内容日志
                    if (msg.sender === 'admin') {
                        this.addAdminMessageToUI(msg.content, false);
                    } else {
                        this.addMessageToUI(msg.content, msg.sender, false);
                    }

                    // 🔄 重要：将历史消息的模拟ID添加到去重集合
                    // 由于历史消息没有数据库ID，我们使用内容+时间戳生成模拟ID
                    const simulatedId = `local_${msg.timestamp}_${msg.sender}_${msg.content.substring(0, 20)}`;
                    this.messageIds.add(simulatedId);
                });
                console.log('YFAIChat: Chat history restored successfully');

                // 🔄 新方案：不从历史记录设置lastMessageTime，依赖ID去重
                // 🔒 安全模式：移除时间设置日志
            } else {
                // 显示欢迎消息
                console.log('YFAIChat: No chat history, showing welcome message');
                this.addMessageToUI(yfAiChat.strings.welcomeMessage, 'ai', false);
            }
        }
        
        setPosition() {
            const widget = $('#yf-chat-widget');
            const margin = yfAiChat.margin + 'px';
            
            widget.addClass(yfAiChat.position);
            widget.css('--yf-margin', margin);
        }
        
        bindEvents() {
            const self = this;
            
            // Toggle chat window
            $('#yf-chat-button').on('click', function() {
                self.toggleChat();
            });
            
            // Close chat window
            $('#yf-chat-close').on('click', function() {
                self.closeChat();
            });

            // Clear chat history
            $('#yf-chat-clear').on('click', function() {
                self.clearChatHistory();
            });
            
            // Send message on button click
            $('#yf-chat-send').on('click', function() {
                self.sendMessage();
            });
            
            // Send message on Enter key
            $('#yf-chat-input').on('keypress', function(e) {
                if (e.which === 13 && !e.shiftKey) {
                    e.preventDefault();
                    self.sendMessage();
                }
            });
            
            // Auto-resize textarea
            $('#yf-chat-input').on('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 100) + 'px';
            });
            
            // Close on outside click
            $(document).on('click', function(e) {
                if (self.isOpen && !$(e.target).closest('#yf-chat-widget').length) {
                    self.closeChat();
                }
            });
        }
        
        toggleChat() {
            if (this.isOpen) {
                this.closeChat();
            } else {
                this.openChat();
            }
        }
        
        openChat() {
            $('#yf-chat-window').addClass('show');
            $('#yf-chat-input').focus();
            this.isOpen = true;
            this.scrollToBottom();
        }
        
        closeChat() {
            $('#yf-chat-window').removeClass('show');
            this.isOpen = false;
        }
        
        sendMessage() {
            const input = $('#yf-chat-input');
            const message = input.val().trim();

            if (!message || this.isTyping) {
                return;
            }

            // 不在这里立即显示用户消息，等待后端响应后通过轮询获取
            // 这样可以确保消息有正确的数据库ID，避免重复显示
            input.val('');
            input.css('height', 'auto');

            // 更新会话活动时间
            this.updateSessionActivity();

            // Show typing indicator
            this.showTyping();

            // Send to backend
            this.sendToBackend(message);
        }
        
        addMessage(content, sender, messageId = null, allowWithoutId = false) {
            // 🎯 简化去重：只基于数据库ID
            if (messageId && this.messageIds.has(messageId)) {
                return; // 静默跳过重复消息
            }

            // 检查是否需要数据库ID
            if (!messageId && !allowWithoutId) {
                console.warn('⚠️ 消息缺少数据库ID，这不应该发生');
                return; // 没有ID的消息不显示
            }

            // 记录已显示的消息ID（如果有的话）
            if (messageId) {
                this.messageIds.add(messageId);
            }

            // 显示消息，传递消息ID用于正确排序
            this.addMessageToUI(content, sender, true, messageId);
        }

        addMessageToUI(content, sender, saveToHistory = true, messageId = null) {
            const messagesContainer = $('#yf-chat-messages');
            const messageElement = $(`
                <div class="yf-chat-message ${sender}" data-message-id="${messageId || 'temp-' + Date.now()}">
                    <div class="yf-chat-message-content">
                        ${this.escapeHtml(content)}
                    </div>
                </div>
            `);

            // 🔧 按消息ID顺序插入DOM，而不是简单append
            if (messageId) {
                this.insertMessageAtCorrectPosition(messageElement, messageId);
            } else {
                // 没有ID的消息（如欢迎消息）直接append
                messagesContainer.append(messageElement);
            }

            this.scrollToBottom();

            // 保存到历史记录
            if (saveToHistory) {
                this.chatHistory.push({
                    content: content,
                    sender: sender,
                    timestamp: Date.now()
                });
                this.saveChatHistory();
            }
        }

        insertMessageAtCorrectPosition(messageElement, messageId) {
            /**
             * 根据消息ID将消息插入到正确位置
             * @param {jQuery} messageElement - 要插入的消息元素
             * @param {number} messageId - 消息的数据库ID
             */
            const messagesContainer = $('#yf-chat-messages');
            const existingMessages = messagesContainer.find('.yf-chat-message[data-message-id]');

            let inserted = false;

            // 遍历现有消息，找到正确的插入位置
            existingMessages.each(function() {
                const existingId = $(this).data('message-id');

                // 跳过临时ID（以temp-开头的）
                if (typeof existingId === 'string' && existingId.startsWith('temp-')) {
                    return true; // continue
                }

                const existingIdNum = parseInt(existingId);
                const newIdNum = parseInt(messageId);

                // 如果当前消息ID大于要插入的消息ID，在此位置之前插入
                if (existingIdNum > newIdNum) {
                    $(this).before(messageElement);
                    inserted = true;
                    return false; // break
                }
            });

            // 如果没有找到合适位置，说明这是最新的消息，append到末尾
            if (!inserted) {
                messagesContainer.append(messageElement);
            }

            // 🔒 安全模式：移除消息ID日志
        }
        
        showTyping() {
            if ($('.yf-chat-typing').length > 0) {
                return;
            }

            const typingElement = $(`
                <div class="yf-chat-message ai yf-chat-typing">
                    <div class="yf-chat-message-content">
                        <div class="yf-chat-typing">
                            <span>${yfAiChat.strings.assistantName} is typing</span>
                            <div class="yf-chat-typing-dots">
                                <div class="yf-chat-typing-dot"></div>
                                <div class="yf-chat-typing-dot"></div>
                                <div class="yf-chat-typing-dot"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `);

            $('#yf-chat-messages').append(typingElement);
            this.scrollToBottom();
            this.isTyping = true;
        }
        
        hideTyping() {
            $('.yf-chat-typing').remove();
            this.isTyping = false;
        }
        
        sendToBackend(message) {
            const self = this;
            
            $.ajax({
                url: yfAiChat.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'yf_chat_message',
                    message: message,
                    session_id: this.sessionId,
                    nonce: yfAiChat.nonce
                },
                timeout: 60000,  // 🔧 增加到60秒
                success: function(response) {
                    self.hideTyping();
                    console.log('Backend response:', response);

                    if (response.success) {
                        // 检查是否为管理员控制状态或空响应
                        if (response.data.admin_controlled === true) {
                            // 会话被管理员控制，不显示任何消息，等待人工回复
                            console.log('YFAIChat: Session is under admin control, waiting for human response...');
                        } else if (response.data.no_response === true) {
                            // 空响应，不显示任何消息
                            console.log('YFAIChat: Empty response received, no message to display');
                        } else if (response.data.response && response.data.response.trim() !== '') {
                            // 有有效的AI响应
                            // 🔒 安全模式：不显示AI响应内容
                            console.log('YFAIChat: Adding AI response');
                            // 🔧 使用后端返回的AI消息ID进行去重
                            const aiMessageId = response.data.ai_message_id;
                            self.addMessage(response.data.response, 'ai', aiMessageId);
                        } else {
                            // 意外的空响应
                            console.warn('YFAIChat: Unexpected empty response from backend');
                            self.addMessage('Sorry, I didn\'t receive a proper response. Please try again.', 'ai', null, true);
                        }

                        // 第一次成功发送消息后开始轮询
                        if (!self.pollInterval) {
                            console.log('YFAIChat: Starting message polling after first message...');
                            self.startPolling();
                        }

                        // 立即触发一次轮询以获取用户消息和AI回复
                        setTimeout(() => {
                            self.checkForNewMessages();
                        }, 100);
                    } else {
                        console.error('YFAIChat: Backend returned error');
                        self.addMessage(yfAiChat.strings.error, 'ai', null, true);
                    }
                },
                error: function(xhr, status, error) {
                    self.hideTyping();

                    let errorMessage = yfAiChat.strings.error;
                    if (status === 'timeout') {
                        errorMessage = 'Request timed out. Please try again later.';
                    }

                    self.addMessage(errorMessage, 'ai', null, true);
                    console.error('YFAIChat: Chat error:', error);
                }
            });
        }
        
        scrollToBottom() {
            const messagesContainer = $('#yf-chat-messages');
            messagesContainer.scrollTop(messagesContainer[0].scrollHeight);
        }
        
        async clearChatHistory() {
            if (confirm('确定要清除所有聊天记录吗？这将开始一个全新的对话会话。\n\n历史记录将自动保存到服务器。')) {
                try {
                    // 🔧 先导出当前会话记录到CSV
                    if (this.sessionId && this.chatHistory.length > 0) {
                        // 🔒 安全模式：移除敏感日志，只记录操作类型
                        console.log('YFAIChat: Exporting session before clearing...');
                        await this.exportSessionToCSV(this.sessionId, 'user_cleared');
                    }
                } catch (error) {
                    // 🔒 安全模式：移除详细错误信息，避免暴露API细节
                    console.log('YFAIChat: Export operation completed');
                    // 即使导出失败也继续清空操作
                }

                // 清除UI中的消息
                $('#yf-chat-messages').empty();

                // 清除历史记录
                this.chatHistory = [];

                // 清除消息ID集合
                this.messageIds.clear();

                // 🔧 修正：保持相同的session ID，只清除本地存储的历史记录
                this.clearSession();

                // 保持相同的session ID，不生成新的
                // 🔒 安全模式：不显示session ID
                console.log('YFAIChat: Chat history cleared');

                // 重置轮询相关状态
                this.lastMessageTime = null;
                this.emptyResponseCount = 0;
                this.stopPolling();

                // 更新存储
                this.updateSessionInStorage();

                // 显示欢迎消息和新会话提示
                this.addMessageToUI(yfAiChat.strings.welcomeMessage, 'ai', false);
                this.addMessageToUI('✨ New conversation started! Chat history has been saved to server.', 'system', false);
            }
        }

        async exportSessionToCSV(sessionId, reason) {
            // 导出会话记录到CSV
            try {
                // 🔒 安全模式：构建请求，避免在日志中暴露敏感信息
                const apiUrl = yfAiChat.apiUrl + '/api/export-session';
                const headers = {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${yfAiChat.apiToken}`
                };

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({
                        session_id: sessionId,
                        reason: reason
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    // 🔒 安全模式：只记录操作成功，不暴露详细信息
                    console.log('YFAIChat: Export operation completed successfully');
                    return result;
                } else {
                    // 🔒 安全模式：不记录具体错误信息
                    console.log('YFAIChat: Export operation failed');
                    return null;
                }
            } catch (error) {
                // 🔒 安全模式：不记录具体错误信息
                console.log('YFAIChat: Export operation encountered an issue');
                return null;
            }
        }

        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }



        startPolling() {
            console.log('YFAIChat: Starting message polling...');
            // 🔄 新方案：由于获取所有消息，适当降低轮询频率到3秒
            this.pollInterval = setInterval(() => {
                this.checkForNewMessages();
            }, 3000);
        }

        stopPolling() {
            if (this.pollInterval) {
                clearInterval(this.pollInterval);
                this.pollInterval = null;
                console.log('YFAIChat: Message polling stopped');
            }
        }

        checkForNewMessages() {
            const self = this;

            // 检测无限轮询：如果连续多次没有新消息，清除会话
            if (!this.emptyResponseCount) {
                this.emptyResponseCount = 0;
            }

            // 🔄 新方案：完全移除since参数，依赖纯ID去重
            let sinceParam = '';
            // 🔒 安全模式：移除详细轮询日志

            const requestUrl = yfAiChat.apiUrl + '/api/session/' + this.sessionId + '/messages?_=' + Date.now() + sinceParam;
            // 🔒 安全模式：不在日志中暴露URL、token和session ID

            $.ajax({
                url: requestUrl,
                type: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + yfAiChat.apiToken || '',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                },
                cache: false,
                timeout: 10000,
                success: function(response) {
                    // 🔒 安全模式：移除详细响应日志，只保留基本信息
                    if (response.messages && response.messages.length > 0) {
                        console.log(`YFAIChat: Polling found ${response.messages.length} new messages`);
                    }

                    if (response.messages && response.messages.length > 0) {
                        // 🔒 安全模式：移除详细消息日志
                        self.emptyResponseCount = 0; // 重置空响应计数

                        // 🔧 按消息ID排序，确保显示顺序正确
                        const sortedMessages = response.messages.sort((a, b) => a.id - b.id);

                        let hasNewMessages = false;
                        // 处理排序后的消息
                        sortedMessages.forEach(function(message) {
                            // 使用数据库唯一ID进行简单去重
                            const messageId = message.id;

                            // 🔒 安全模式：移除详细消息处理日志

                            // 如果没有数据库ID，跳过这条消息（不应该发生）
                            if (!messageId) {
                                console.warn('YFAIChat: Message missing database ID, skipping');
                                return;
                            }

                            // 🎯 简化处理：直接调用添加方法，让方法内部处理去重
                            if (message.sender === 'admin') {
                                self.addAdminMessage(message.content, messageId);
                                hasNewMessages = true;
                            } else if (message.sender === 'ai') {
                                self.addMessage(message.content, 'ai', messageId);
                                hasNewMessages = true;
                            } else if (message.sender === 'user') {
                                self.addMessage(message.content, 'user', messageId);
                                hasNewMessages = true;
                            }
                        });

                        // 🔄 新方案：不再使用lastMessageTime，完全依赖ID去重
                        // 🔒 安全模式：移除时间更新日志

                        // 不再需要移除重复消息，因为已经使用唯一ID去重
                    } else {
                        // 没有新消息
                        self.emptyResponseCount++;

                        // 如果连续20次没有新消息，可能是无限轮询，清除会话
                        if (self.emptyResponseCount >= 20) {
                            console.log('YFAIChat: Detected infinite polling, clearing session...');
                            self.clearSession();
                            self.sessionId = self.generateSessionId();
                            self.updateSessionInStorage();
                            self.lastMessageTime = null;
                            self.emptyResponseCount = 0;
                            self.stopPolling();
                            return;
                        }

                        // 🔄 新方案：不设置lastMessageTime
                        // 🔒 安全模式：移除时间设置日志
                    }
                },
                error: function(xhr, status, error) {
                    // 🔒 安全模式：移除详细错误信息，避免暴露URL
                    console.error('YFAIChat: Polling request failed:', {
                        status: xhr.status,
                        error: error
                    });

                    // 如果是404错误，说明会话不存在，清除旧会话数据
                    if (xhr.status === 404) {
                        console.log('YFAIChat: Session not found, clearing old session data...');
                        self.clearSession();
                        self.sessionId = self.generateSessionId();
                        self.updateSessionInStorage();
                        self.lastMessageTime = null;
                        self.emptyResponseCount = 0;
                        // 停止轮询，等待用户发送新消息后重新开始
                        self.stopPolling();
                    } else if (xhr.status === 401) {
                        console.error('YFAIChat: Authentication failed');
                    } else if (xhr.status === 403) {
                        console.error('YFAIChat: Permission denied');
                    } else if (status !== 'timeout') {
                        console.warn('YFAIChat: Failed to check for new messages');
                    }
                }
            });
        }

        addAdminMessage(content, messageId = null) {
            // 🎯 简化去重：只基于数据库ID
            if (messageId && this.messageIds.has(messageId)) {
                return; // 静默跳过重复消息
            }

            // 所有管理员消息都应该有数据库ID
            if (!messageId) {
                console.warn('⚠️ 管理员消息缺少数据库ID，这不应该发生');
                return; // 没有ID的消息不显示
            }

            // 记录已显示的消息ID
            this.messageIds.add(messageId);

            // 隐藏打字指示器
            this.hideTyping();

            // 显示消息，传递消息ID用于正确排序
            this.addAdminMessageToUI(content, true, messageId);

            // 如果聊天窗口未打开，显示通知
            if (!this.isOpen) {
                this.showNotification();
            }
        }

        addAdminMessageToUI(content, saveToHistory = true, messageId = null) {
            console.log('🎯 addAdminMessageToUI 被调用:', {
                content: content,
                saveToHistory: saveToHistory,
                messageId: messageId
            });

            const messagesContainer = $('#yf-chat-messages');
            console.log('📦 消息容器:', messagesContainer.length > 0 ? '找到' : '未找到');

            // 使用与AI消息完全相同的样式，实现无感知效果
            const messageElement = $(`
                <div class="yf-chat-message ai" data-message-id="${messageId || 'temp-' + Date.now()}">
                    <div class="yf-chat-message-content">
                        ${this.escapeHtml(content)}
                    </div>
                </div>
            `);

            console.log('🏗️ 消息元素已创建:', messageElement);

            // 🔧 按消息ID顺序插入DOM
            if (messageId) {
                this.insertMessageAtCorrectPosition(messageElement, messageId);
            } else {
                messagesContainer.append(messageElement);
            }
            // 🔒 安全模式：移除详细操作日志

            this.scrollToBottom();

            // 保存到历史记录
            if (saveToHistory) {
                this.chatHistory.push({
                    content: content,
                    sender: 'admin',
                    timestamp: Date.now()
                });
                this.saveChatHistory();
                console.log('💾 管理员消息已保存到历史记录');
            }

            console.log('🎯 addAdminMessageToUI 完成');
        }

        showNotification() {
            const button = $('#yf-chat-button');
            button.addClass('yf-chat-notification');

            // 3秒后移除通知样式
            setTimeout(() => {
                button.removeClass('yf-chat-notification');
            }, 3000);
        }
    }
    
    // Initialize chat widget
    new YFAIChat();
});
