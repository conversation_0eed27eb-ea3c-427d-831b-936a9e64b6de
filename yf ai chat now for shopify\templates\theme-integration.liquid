{% comment %}
YF AI Chat for Shopify - 主题集成代码
这个文件包含了需要添加到 theme.liquid 文件中的代码

使用方法：
1. 复制下面的代码
2. 粘贴到 theme.liquid 文件的 </body> 标签之前
3. 保存文件

或者使用简化版本：
{% include 'yf-chat-widget' %}
{% endcomment %}

<!-- YF AI Chat Widget 开始 -->
<!-- 样式文件 -->
{{ 'yf-chat-shopify.css' | asset_url | stylesheet_tag }}

<!-- 配置脚本 -->
<script>
// YF AI Chat 配置
window.YF_CHAT_CONFIG = {
    // 🔧 API配置 - 请修改为您的实际配置
    apiUrl: 'https://chat.22668.xyz:8000',
    apiToken: 'YOUR_WORDPRESS_TOKEN_HERE',
    
    // 界面配置
    position: 'bottom-right',
    margin: 100,
    
    // 文本配置
    strings: {
        title: 'AI智能客服',
        placeholder: '输入您的问题...',
        welcomeMessage: '您好！我是Dylan，您的专属环保电器顾问。有什么可以帮助您的吗？',
        assistantName: 'Dylan',
        error: '抱歉，出现了一些问题，请稍后再试。'
    },
    
    // 功能配置
    settings: {
        sessionTimeout: 30 * 60 * 1000,
        saveHistory: true
    }
};

// Shopify商品信息集成
if (typeof window.ShopifyAnalytics !== 'undefined' && 
    window.ShopifyAnalytics.meta && 
    window.ShopifyAnalytics.meta.product) {
    
    window.YF_CHAT_SHOPIFY_PRODUCT = {
        id: window.ShopifyAnalytics.meta.product.id,
        title: window.ShopifyAnalytics.meta.product.title,
        vendor: window.ShopifyAnalytics.meta.product.vendor,
        type: window.ShopifyAnalytics.meta.product.type,
        price: window.ShopifyAnalytics.meta.product.price,
        url: window.location.href
    };
    
    console.log('Shopify产品信息已获取:', window.YF_CHAT_SHOPIFY_PRODUCT);
}

// Shopify购物车功能
window.YF_CHAT_SHOPIFY_CART = {
    addToCart: function(variantId, quantity = 1) {
        return fetch('/cart/add.js', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: variantId,
                quantity: quantity
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('商品已添加到购物车:', data);
            // 触发购物车更新事件
            document.dispatchEvent(new CustomEvent('cart:updated', { detail: data }));
            return data;
        })
        .catch(error => {
            console.error('添加到购物车失败:', error);
            throw error;
        });
    },
    
    getCart: function() {
        return fetch('/cart.js')
        .then(response => response.json())
        .then(data => {
            console.log('购物车信息:', data);
            return data;
        })
        .catch(error => {
            console.error('获取购物车失败:', error);
            throw error;
        });
    },
    
    updateCart: function(updates) {
        return fetch('/cart/update.js', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ updates: updates })
        })
        .then(response => response.json())
        .then(data => {
            console.log('购物车已更新:', data);
            document.dispatchEvent(new CustomEvent('cart:updated', { detail: data }));
            return data;
        })
        .catch(error => {
            console.error('更新购物车失败:', error);
            throw error;
        });
    }
};

// Shopify客户信息 (如果已登录)
{% if customer %}
window.YF_CHAT_SHOPIFY_CUSTOMER = {
    id: {{ customer.id }},
    email: '{{ customer.email }}',
    firstName: '{{ customer.first_name }}',
    lastName: '{{ customer.last_name }}',
    ordersCount: {{ customer.orders_count }},
    totalSpent: '{{ customer.total_spent | money }}'
};
console.log('Shopify客户信息已获取:', window.YF_CHAT_SHOPIFY_CUSTOMER);
{% endif %}

// 页面类型信息
window.YF_CHAT_SHOPIFY_PAGE = {
    type: '{{ template.name }}',
    {% if template.name == 'product' %}
    productId: {{ product.id }},
    productHandle: '{{ product.handle }}',
    {% elsif template.name == 'collection' %}
    collectionId: {{ collection.id }},
    collectionHandle: '{{ collection.handle }}',
    {% elsif template.name == 'page' %}
    pageId: {{ page.id }},
    pageHandle: '{{ page.handle }}',
    {% endif %}
    url: window.location.href,
    title: document.title
};

// 商店信息
window.YF_CHAT_SHOPIFY_SHOP = {
    name: '{{ shop.name }}',
    domain: '{{ shop.domain }}',
    currency: '{{ shop.currency }}',
    moneyFormat: '{{ shop.money_format }}'
};
</script>

<!-- 主要脚本文件 -->
{{ 'yf-chat-shopify.js' | asset_url | script_tag }}

<!-- 可选：Google Analytics 集成 -->
{% if settings.google_analytics_id %}
<script>
// 聊天事件追踪
document.addEventListener('DOMContentLoaded', function() {
    // 监听聊天开始事件
    document.addEventListener('yf-chat:opened', function() {
        if (typeof gtag !== 'undefined') {
            gtag('event', 'chat_opened', {
                'event_category': 'engagement',
                'event_label': 'ai_chat'
            });
        }
    });
    
    // 监听消息发送事件
    document.addEventListener('yf-chat:message_sent', function(e) {
        if (typeof gtag !== 'undefined') {
            gtag('event', 'chat_message_sent', {
                'event_category': 'engagement',
                'event_label': 'ai_chat',
                'value': 1
            });
        }
    });
});
</script>
{% endif %}

<!-- 可选：Facebook Pixel 集成 -->
{% if settings.facebook_pixel_id %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 监听聊天开始事件
    document.addEventListener('yf-chat:opened', function() {
        if (typeof fbq !== 'undefined') {
            fbq('track', 'Contact', {
                content_name: 'AI Chat Started'
            });
        }
    });
});
</script>
{% endif %}

<!-- 样式覆盖 (可选) -->
<style>
/* 自定义样式覆盖 */
.yf-chat-widget {
    /* 根据主题调整z-index */
    z-index: 9999;
}

/* 适配主题色彩 */
.yf-chat-header {
    background: linear-gradient(135deg, {{ settings.accent_color | default: '#667eea' }} 0%, {{ settings.secondary_color | default: '#764ba2' }} 100%);
}

.yf-chat-widget .yf-chat-send {
    background: {{ settings.accent_color | default: '#667eea' }};
}

.yf-chat-widget .yf-chat-send:hover {
    background: {{ settings.accent_color | color_darken: 10 | default: '#5a6fd8' }};
}

/* 响应式调整 */
@media (max-width: 768px) {
    .yf-chat-widget {
        --yf-margin: 20px !important;
    }
    
    .yf-chat-window {
        width: calc(100vw - 40px);
        max-width: 350px;
        height: 70vh;
        max-height: 500px;
    }
}
</style>

<!-- YF AI Chat Widget 结束 -->

{% comment %}
集成说明：

1. 基础集成
   - 复制上述代码到 theme.liquid 的 </body> 前
   - 修改 apiUrl 和 apiToken 配置
   - 上传 CSS 和 JS 文件到 assets/ 目录

2. 高级功能
   - 自动获取商品、客户、页面信息
   - 购物车操作集成
   - Google Analytics 和 Facebook Pixel 事件追踪

3. 样式自定义
   - 使用主题设置中的颜色
   - 响应式设计适配
   - 可根据需要调整样式

4. 事件系统
   - yf-chat:opened - 聊天窗口打开
   - yf-chat:closed - 聊天窗口关闭
   - yf-chat:message_sent - 发送消息
   - yf-chat:message_received - 接收消息
   - cart:updated - 购物车更新

5. 调试
   - 打开浏览器控制台查看日志
   - 检查配置是否正确加载
   - 验证API连接状态
{% endcomment %}
