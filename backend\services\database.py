import os
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy import create_engine, select, update
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker
from models.chat_models import Base, ChatSession, ChatMessage, SessionReadStatus, ChatSessionResponse, ChatMessageResponse, SessionReadStatusResponse
from utils.timezone_config import get_current_time_naive, convert_utc_to_la
from utils.path_utils import get_database_path, get_database_url

class DatabaseService:
    def __init__(self):
        # 使用新的路径管理工具
        from utils.path_utils import copy_default_files_if_needed

        # 确保必要的目录和文件存在
        copy_default_files_if_needed()

        # 获取数据库路径和URL
        db_file_path = get_database_path()

        # 从环境变量获取数据库URL，优先使用环境变量配置
        database_url = os.getenv("DATABASE_URL")
        if database_url:
            print(f"使用环境变量中的数据库配置: {database_url}")
            # 如果是相对路径，转换为绝对路径
            if not database_url.startswith("sqlite+aiosqlite:///"):
                # 处理相对路径
                if database_url.startswith("sqlite+aiosqlite://"):
                    relative_path = database_url.replace("sqlite+aiosqlite://", "")
                    database_url = f"sqlite+aiosqlite:///{db_file_path.parent / relative_path}"
            else:
                # 处理绝对路径中的相对路径部分
                path_part = database_url.replace("sqlite+aiosqlite:///", "")
                if not os.path.isabs(path_part):
                    # 相对路径，基于backend目录解析
                    from utils.path_utils import get_backend_directory
                    backend_dir = get_backend_directory()
                    absolute_path = backend_dir / path_part
                    database_url = f"sqlite+aiosqlite:///{absolute_path}"
                    db_file_path = absolute_path
        else:
            print(f"环境变量DATABASE_URL未设置，使用默认路径: {db_file_path}")
            database_url = get_database_url()

        # 确保数据库文件目录存在
        db_file_path.parent.mkdir(parents=True, exist_ok=True)

        # 确保数据库文件存在
        if not db_file_path.exists():
            print(f"WARNING: 数据库文件不存在: {db_file_path}")
            print(f"将在首次使用时自动创建数据库文件")
        else:
            print(f"OK: 数据库文件已找到: {db_file_path}")

        # 添加连接池配置以避免锁定问题
        self.engine = create_async_engine(
            database_url,
            echo=False,
            pool_pre_ping=True,
            pool_recycle=300,
            connect_args={"check_same_thread": False}
        )
        self.async_session = async_sessionmaker(self.engine, expire_on_commit=False)

        # 同步会话（用于非异步操作）
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker
        # 确保同步数据库URL使用正确的路径
        if database_url.startswith("sqlite+aiosqlite:///"):
            # 提取路径部分，避免多余的斜杠
            db_path_from_url = database_url.replace("sqlite+aiosqlite:///", "")
            sync_database_url = f"sqlite:///{db_path_from_url}"
        else:
            sync_database_url = f"sqlite:///{db_file_path}"

        print(f"异步数据库URL: {database_url}")
        print(f"同步数据库URL: {sync_database_url}")

        self.sync_engine = create_engine(
            sync_database_url,
            pool_pre_ping=True,
            pool_recycle=300,
            connect_args={"check_same_thread": False}
        )
        self.sync_session = sessionmaker(bind=self.sync_engine)
    
    async def init_db(self):
        """Initialize database tables"""
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
    
    async def get_or_create_session(self, session_id: str, user_ip: str = None, user_agent: str = None) -> ChatSessionResponse:
        """Get existing session or create new one"""
        async with self.async_session() as session:
            # Try to get existing session
            result = await session.execute(
                select(ChatSession).where(ChatSession.id == session_id)
            )
            chat_session = result.scalar_one_or_none()
            
            if not chat_session:
                # Create new session
                chat_session = ChatSession(
                    id=session_id,
                    user_ip=user_ip,
                    user_agent=user_agent
                )
                session.add(chat_session)
                await session.commit()
                await session.refresh(chat_session)
            
            return ChatSessionResponse(
                id=chat_session.id,
                created_at=chat_session.created_at,
                updated_at=chat_session.updated_at,
                admin_control=chat_session.admin_control,
                admin_control_at=chat_session.admin_control_at,
                admin_control_by=chat_session.admin_control_by,
                user_ip=chat_session.user_ip,
                user_agent=chat_session.user_agent
            )

    async def get_session(self, session_id: str) -> Optional[ChatSessionResponse]:
        """Get a specific session by ID"""
        async with self.async_session() as session:
            result = await session.execute(
                select(ChatSession).where(ChatSession.id == session_id)
            )
            chat_session = result.scalar_one_or_none()

            if not chat_session:
                return None

            return ChatSessionResponse(
                id=chat_session.id,
                created_at=chat_session.created_at,
                updated_at=chat_session.updated_at,
                admin_control=chat_session.admin_control,
                admin_control_at=chat_session.admin_control_at,
                admin_control_by=chat_session.admin_control_by,
                user_ip=chat_session.user_ip,
                user_agent=chat_session.user_agent
            )
    
    async def save_message(self, session_id: str, content: str, sender: str, admin_id: str = None) -> ChatMessageResponse:
        """Save a chat message"""
        async with self.async_session() as session:
            message = ChatMessage(
                session_id=session_id,
                content=content,
                sender=sender,
                admin_id=admin_id
            )
            session.add(message)
            await session.commit()
            await session.refresh(message)

            # Update session timestamp
            await session.execute(
                update(ChatSession)
                .where(ChatSession.id == session_id)
                .values(updated_at=get_current_time_naive())
            )
            await session.commit()

            # 确保返回的消息包含数据库生成的唯一ID
            return ChatMessageResponse(
                id=message.id,  # 这是数据库自动生成的唯一ID
                session_id=message.session_id,
                content=message.content,
                sender=message.sender,
                admin_id=message.admin_id,
                created_at=message.created_at
            )
    
    async def get_session_messages(self, session_id: str, limit: int = 100) -> List[ChatMessageResponse]:
        """Get messages for a session"""
        async with self.async_session() as session:
            result = await session.execute(
                select(ChatMessage)
                .where(ChatMessage.session_id == session_id)
                .order_by(ChatMessage.id.asc())  # 🔧 按ID正序排列，确保消息按发送顺序显示
                .limit(limit)
            )
            messages = result.scalars().all()

            # 🔧 移除 reversed() 操作，保持消息的正确时间顺序
            return [
                ChatMessageResponse(
                    id=msg.id,
                    session_id=msg.session_id,
                    content=msg.content,
                    sender=msg.sender,
                    admin_id=msg.admin_id,
                    created_at=msg.created_at
                )
                for msg in messages  # 直接使用messages，不再反转
            ]
    
    async def get_all_sessions(self, limit: int = 50, include_archived: bool = False) -> List[ChatSessionResponse]:
        """Get all chat sessions"""
        async with self.async_session() as session:
            query = select(ChatSession)
            if not include_archived:
                query = query.where(ChatSession.archived == False)
            query = query.order_by(ChatSession.updated_at.desc()).limit(limit)

            result = await session.execute(query)
            sessions = result.scalars().all()

            return [
                ChatSessionResponse(
                    id=sess.id,
                    created_at=sess.created_at,
                    updated_at=sess.updated_at,
                    admin_control=sess.admin_control,
                    admin_control_at=sess.admin_control_at,
                    admin_control_by=sess.admin_control_by,
                    user_ip=sess.user_ip,
                    user_agent=sess.user_agent,
                    archived=getattr(sess, 'archived', False),
                    archived_at=getattr(sess, 'archived_at', None),
                    archived_by=getattr(sess, 'archived_by', None)
                )
                for sess in sessions
            ]
    
    async def set_admin_control(self, session_id: str, admin_control: bool, admin_id: str = None):
        """Set admin control status for a session"""
        async with self.async_session() as session:
            values = {"admin_control": admin_control}

            if admin_control:
                # 接管时记录时间和管理员ID
                values["admin_control_at"] = get_current_time_naive()
                values["admin_control_by"] = admin_id
            else:
                # 释放时清除时间和管理员ID
                values["admin_control_at"] = None
                values["admin_control_by"] = None

            await session.execute(
                update(ChatSession)
                .where(ChatSession.id == session_id)
                .values(**values)
            )
            await session.commit()

    async def check_and_release_expired_admin_control(self, timeout_minutes: int = 15):
        """检查并释放超时的管理员控制"""
        from datetime import timedelta

        async with self.async_session() as session:
            # 计算超时时间点
            timeout_time = get_current_time_naive() - timedelta(minutes=timeout_minutes)

            # 查找超时的会话
            result = await session.execute(
                select(ChatSession)
                .where(
                    ChatSession.admin_control == True,
                    ChatSession.admin_control_at < timeout_time
                )
            )
            expired_sessions = result.scalars().all()

            # 释放超时的会话
            released_sessions = []
            for chat_session in expired_sessions:
                await session.execute(
                    update(ChatSession)
                    .where(ChatSession.id == chat_session.id)
                    .values(
                        admin_control=False,
                        admin_control_at=None,
                        admin_control_by=None
                    )
                )
                released_sessions.append({
                    "session_id": chat_session.id,
                    "admin_id": chat_session.admin_control_by,
                    "control_time": chat_session.admin_control_at
                })

            await session.commit()
            return released_sessions

    async def mark_session_read(self, session_id: str, admin_id: str):
        """Mark a session as read by an admin"""
        async with self.async_session() as session:
            # 检查是否已存在已读记录
            result = await session.execute(
                select(SessionReadStatus)
                .where(SessionReadStatus.session_id == session_id)
                .where(SessionReadStatus.admin_id == admin_id)
            )
            existing_record = result.scalar_one_or_none()

            if existing_record:
                # 更新已读时间
                await session.execute(
                    update(SessionReadStatus)
                    .where(SessionReadStatus.session_id == session_id)
                    .where(SessionReadStatus.admin_id == admin_id)
                    .values(read_at=datetime.now())
                )
            else:
                # 创建新的已读记录
                read_status = SessionReadStatus(
                    session_id=session_id,
                    admin_id=admin_id
                )
                session.add(read_status)

            await session.commit()

    async def get_all_sessions_with_read_status(self, admin_id: str, limit: int = 50, include_archived: bool = False) -> List[dict]:
        """Get all chat sessions with read status for specific admin"""
        async with self.async_session() as session:
            # 获取会话和对应的已读状态
            from sqlalchemy.orm import aliased
            from sqlalchemy import outerjoin

            query = select(ChatSession, SessionReadStatus.read_at).outerjoin(
                SessionReadStatus,
                (ChatSession.id == SessionReadStatus.session_id) &
                (SessionReadStatus.admin_id == admin_id)
            )

            if not include_archived:
                query = query.where(ChatSession.archived == False)

            query = query.order_by(ChatSession.updated_at.desc()).limit(limit)
            result = await session.execute(query)

            sessions_with_status = []
            for chat_session, read_at in result:
                session_dict = {
                    "id": chat_session.id,
                    "created_at": chat_session.created_at,
                    "updated_at": chat_session.updated_at,
                    "admin_control": chat_session.admin_control,
                    "admin_control_at": chat_session.admin_control_at,
                    "admin_control_by": chat_session.admin_control_by,
                    "user_ip": chat_session.user_ip,
                    "user_agent": chat_session.user_agent,
                    "archived": getattr(chat_session, 'archived', False),
                    "archived_at": getattr(chat_session, 'archived_at', None),
                    "archived_by": getattr(chat_session, 'archived_by', None),
                    "is_read": read_at is not None  # 如果有已读时间，则为已读
                }
                sessions_with_status.append(session_dict)

            return sessions_with_status

    async def archive_session(self, session_id: str, admin_id: str):
        """Archive a session"""
        async with self.async_session() as session:
            await session.execute(
                update(ChatSession)
                .where(ChatSession.id == session_id)
                .values(
                    archived=True,
                    archived_at=get_current_time_naive(),
                    archived_by=admin_id
                )
            )
            await session.commit()

    async def unarchive_session(self, session_id: str):
        """Unarchive a session"""
        async with self.async_session() as session:
            await session.execute(
                update(ChatSession)
                .where(ChatSession.id == session_id)
                .values(
                    archived=False,
                    archived_at=None,
                    archived_by=None
                )
            )
            await session.commit()

    async def get_archived_sessions(self, admin_id: str = None, limit: int = 50) -> List[ChatSessionResponse]:
        """Get archived sessions"""
        async with self.async_session() as session:
            query = select(ChatSession).where(ChatSession.archived == True)
            if admin_id:
                query = query.where(ChatSession.archived_by == admin_id)
            query = query.order_by(ChatSession.archived_at.desc()).limit(limit)

            result = await session.execute(query)
            sessions = result.scalars().all()

            return [
                ChatSessionResponse(
                    id=sess.id,
                    created_at=sess.created_at,
                    updated_at=sess.updated_at,
                    admin_control=sess.admin_control,
                    admin_control_at=sess.admin_control_at,
                    admin_control_by=sess.admin_control_by,
                    user_ip=sess.user_ip,
                    user_agent=sess.user_agent,
                    archived=sess.archived,
                    archived_at=sess.archived_at,
                    archived_by=sess.archived_by
                )
                for sess in sessions
            ]

    async def get_conversation_history(self, session_id: str) -> List[Dict[str, Any]]:
        """获取会话的完整聊天记录，用于CSV导出"""
        async with self.async_session() as session:
            # 查询该会话的所有消息，按时间排序
            result = await session.execute(
                select(ChatMessage)
                .where(ChatMessage.session_id == session_id)
                .order_by(ChatMessage.created_at)
            )
            messages = result.scalars().all()

            # 转换为字典格式
            history = []
            for msg in messages:
                history.append({
                    'timestamp': msg.created_at.isoformat() if msg.created_at else '',
                    'sender': msg.sender,  # 'user', 'ai', 'customer_service'
                    'message': msg.content,  # 🔧 修正：使用content字段而不是message字段
                    'session_id': msg.session_id,
                    'user_ip': getattr(msg, 'user_ip', '')  # 如果字段存在的话
                })

            return history

    async def delete_session_messages(self, session_id: str) -> int:
        """删除指定session的所有消息记录，返回删除的消息数量"""
        async with self.async_session() as session:
            # 查询要删除的消息数量
            count_result = await session.execute(
                select(ChatMessage)
                .where(ChatMessage.session_id == session_id)
            )
            message_count = len(count_result.scalars().all())

            # 删除所有消息
            await session.execute(
                ChatMessage.__table__.delete()
                .where(ChatMessage.session_id == session_id)
            )

            # 删除session记录（如果存在）
            await session.execute(
                ChatSession.__table__.delete()
                .where(ChatSession.id == session_id)
            )

            await session.commit()
            return message_count


# 全局数据库服务实例
_db_service = None

def get_db_service():
    """获取数据库服务实例"""
    global _db_service
    if _db_service is None:
        _db_service = DatabaseService()
    return _db_service

def get_db():
    """获取数据库会话生成器（兼容性函数）"""
    db_service = get_db_service()
    return db_service.async_session()
