#!/usr/bin/env python3
"""
表格查看器组件
用于显示和编辑数据库表格数据
"""

import pandas as pd
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLabel, QSpinBox,
                            QLineEdit, QComboBox, QMessageBox, QHeaderView,
                            QMenu, QAbstractItemView, QGroupBox)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QAction, QContextMenuEvent

from core.database import db_manager
from core.config import config_manager

class TableViewer(QWidget):
    # 信号定义
    data_changed = pyqtSignal(str)  # 数据变更信号
    
    def __init__(self):
        super().__init__()
        self.current_table = None
        self.current_data = pd.DataFrame()
        self.current_page = 0
        self.rows_per_page = 100
        self.total_rows = 0
        
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)
        
        # 表格
        self.table_widget = QTableWidget()
        self.table_widget.setAlternatingRowColors(True)
        self.table_widget.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table_widget.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.table_widget.customContextMenuRequested.connect(self.show_context_menu)
        self.table_widget.itemChanged.connect(self.on_item_changed)
        
        # 设置表格头部可调整大小
        header = self.table_widget.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        header.setStretchLastSection(True)
        
        layout.addWidget(self.table_widget)
        
        # 分页控制
        pagination_panel = self.create_pagination_panel()
        layout.addWidget(pagination_panel)
    
    def create_control_panel(self):
        """创建控制面板"""
        group = QGroupBox("数据控制")
        layout = QHBoxLayout(group)
        
        # 表名显示
        self.table_name_label = QLabel("未选择表")
        self.table_name_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        layout.addWidget(self.table_name_label)
        
        layout.addStretch()
        
        # 筛选输入
        layout.addWidget(QLabel("筛选:"))
        self.filter_input = QLineEdit()
        self.filter_input.setPlaceholderText("WHERE 条件 (例: id > 10)")
        self.filter_input.returnPressed.connect(self.apply_filter)
        layout.addWidget(self.filter_input)
        
        # 应用筛选按钮
        filter_btn = QPushButton("应用筛选")
        filter_btn.clicked.connect(self.apply_filter)
        layout.addWidget(filter_btn)
        
        # 清除筛选按钮
        clear_filter_btn = QPushButton("清除筛选")
        clear_filter_btn.clicked.connect(self.clear_filter)
        layout.addWidget(clear_filter_btn)
        
        layout.addStretch()
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_current_table)
        layout.addWidget(refresh_btn)
        
        # 添加记录按钮
        add_btn = QPushButton("添加记录")
        add_btn.clicked.connect(self.add_record)
        layout.addWidget(add_btn)
        
        return group
    
    def create_pagination_panel(self):
        """创建分页控制面板"""
        group = QGroupBox("分页控制")
        layout = QHBoxLayout(group)
        
        # 记录信息
        self.record_info_label = QLabel("0 条记录")
        layout.addWidget(self.record_info_label)
        
        layout.addStretch()
        
        # 每页行数设置
        layout.addWidget(QLabel("每页显示:"))
        self.rows_per_page_spin = QSpinBox()
        self.rows_per_page_spin.setRange(10, 1000)
        self.rows_per_page_spin.setValue(100)
        self.rows_per_page_spin.valueChanged.connect(self.on_rows_per_page_changed)
        layout.addWidget(self.rows_per_page_spin)
        layout.addWidget(QLabel("行"))
        
        layout.addStretch()
        
        # 分页按钮
        self.prev_btn = QPushButton("上一页")
        self.prev_btn.clicked.connect(self.prev_page)
        layout.addWidget(self.prev_btn)
        
        self.page_label = QLabel("第 0 页")
        layout.addWidget(self.page_label)
        
        self.next_btn = QPushButton("下一页")
        self.next_btn.clicked.connect(self.next_page)
        layout.addWidget(self.next_btn)
        
        return group
    
    def load_settings(self):
        """加载设置"""
        settings = config_manager.get_table_settings()
        self.rows_per_page = settings.get('rows_per_page', 100)
        self.rows_per_page_spin.setValue(self.rows_per_page)
    
    def load_table_data(self, table_name: str, where_clause: str = ""):
        """加载表格数据"""
        if not db_manager.is_connected:
            return
        
        try:
            self.current_table = table_name
            self.table_name_label.setText(f"表: {table_name}")
            
            # 获取总记录数
            self.total_rows = db_manager.get_table_count(table_name, where_clause)
            
            # 获取当前页数据
            offset = self.current_page * self.rows_per_page
            self.current_data = db_manager.get_table_data(
                table_name, self.rows_per_page, offset, where_clause
            )
            
            # 更新表格显示
            self.update_table_display()
            
            # 更新分页信息
            self.update_pagination_info()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载表格数据失败: {e}")
    
    def update_table_display(self):
        """更新表格显示"""
        if self.current_data.empty:
            self.table_widget.setRowCount(0)
            self.table_widget.setColumnCount(0)
            return
        
        # 设置表格大小
        self.table_widget.setRowCount(len(self.current_data))
        self.table_widget.setColumnCount(len(self.current_data.columns))
        
        # 设置列标题
        self.table_widget.setHorizontalHeaderLabels(list(self.current_data.columns))
        
        # 填充数据
        for row in range(len(self.current_data)):
            for col in range(len(self.current_data.columns)):
                value = self.current_data.iloc[row, col]
                
                # 处理特殊值
                if pd.isna(value):
                    display_value = ""
                elif isinstance(value, (dict, list)):
                    display_value = str(value)
                else:
                    display_value = str(value)
                
                item = QTableWidgetItem(display_value)
                
                # 设置主键列为只读
                if col == 0:  # 假设第一列是主键
                    item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                    item.setBackground(Qt.GlobalColor.lightGray)
                
                self.table_widget.setItem(row, col, item)
        
        # 调整列宽
        self.table_widget.resizeColumnsToContents()
    
    def update_pagination_info(self):
        """更新分页信息"""
        total_pages = (self.total_rows + self.rows_per_page - 1) // self.rows_per_page
        current_page_display = self.current_page + 1 if self.total_rows > 0 else 0
        
        self.record_info_label.setText(f"共 {self.total_rows} 条记录")
        self.page_label.setText(f"第 {current_page_display} / {total_pages} 页")
        
        # 更新按钮状态
        self.prev_btn.setEnabled(self.current_page > 0)
        self.next_btn.setEnabled((self.current_page + 1) * self.rows_per_page < self.total_rows)
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 0:
            self.current_page -= 1
            self.refresh_current_table()
    
    def next_page(self):
        """下一页"""
        if (self.current_page + 1) * self.rows_per_page < self.total_rows:
            self.current_page += 1
            self.refresh_current_table()
    
    def on_rows_per_page_changed(self, value):
        """每页行数改变"""
        self.rows_per_page = value
        self.current_page = 0  # 重置到第一页
        
        # 保存设置
        config_manager.update_table_settings({'rows_per_page': value})
        
        self.refresh_current_table()
    
    def apply_filter(self):
        """应用筛选"""
        if not self.current_table:
            return
        
        where_clause = self.filter_input.text().strip()
        self.current_page = 0  # 重置到第一页
        self.load_table_data(self.current_table, where_clause)
    
    def clear_filter(self):
        """清除筛选"""
        self.filter_input.clear()
        self.current_page = 0
        if self.current_table:
            self.load_table_data(self.current_table)
    
    def refresh_current_table(self):
        """刷新当前表格"""
        if self.current_table:
            where_clause = self.filter_input.text().strip()
            self.load_table_data(self.current_table, where_clause)
    
    def clear_data(self):
        """清空数据显示"""
        self.table_widget.setRowCount(0)
        self.table_widget.setColumnCount(0)
        self.current_table = None
        self.current_data = pd.DataFrame()
        self.table_name_label.setText("未选择表")
        self.record_info_label.setText("0 条记录")
        self.page_label.setText("第 0 页")
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        if self.table_widget.itemAt(position) is None:
            return
        
        menu = QMenu(self)
        
        # 删除记录
        delete_action = QAction("删除记录", self)
        delete_action.triggered.connect(self.delete_selected_record)
        menu.addAction(delete_action)
        
        # 复制单元格
        copy_action = QAction("复制单元格", self)
        copy_action.triggered.connect(self.copy_cell)
        menu.addAction(copy_action)
        
        menu.exec(self.table_widget.mapToGlobal(position))
    
    def on_item_changed(self, item):
        """单元格内容改变"""
        if not self.current_table or not db_manager.is_connected:
            return
        
        try:
            row = item.row()
            col = item.column()
            new_value = item.text()
            
            # 获取主键值（假设第一列是主键）
            pk_item = self.table_widget.item(row, 0)
            if not pk_item:
                return
            
            pk_value = pk_item.text()
            column_name = self.current_data.columns[col]
            
            # 更新数据库
            if db_manager.update_record(self.current_table, pk_value, column_name, new_value):
                self.data_changed.emit(f"记录已更新: {column_name} = {new_value}")
            else:
                # 如果更新失败，恢复原值
                original_value = self.current_data.iloc[row, col]
                item.setText(str(original_value))
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"更新记录失败: {e}")
            # 恢复原值
            if row < len(self.current_data) and col < len(self.current_data.columns):
                original_value = self.current_data.iloc[row, col]
                item.setText(str(original_value))
    
    def delete_selected_record(self):
        """删除选中的记录"""
        current_row = self.table_widget.currentRow()
        if current_row < 0:
            return
        
        # 确认删除
        reply = QMessageBox.question(self, "确认删除", "确定要删除这条记录吗？",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 获取主键值
                pk_item = self.table_widget.item(current_row, 0)
                if pk_item:
                    pk_value = pk_item.text()
                    
                    if db_manager.delete_record(self.current_table, pk_value):
                        self.data_changed.emit("记录已删除")
                        self.refresh_current_table()
                    
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除记录失败: {e}")
    
    def copy_cell(self):
        """复制单元格内容"""
        current_item = self.table_widget.currentItem()
        if current_item:
            from PyQt6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(current_item.text())
    
    def add_record(self):
        """添加新记录"""
        QMessageBox.information(self, "提示", "添加记录功能将在后续版本中实现")
        # TODO: 实现添加记录对话框
