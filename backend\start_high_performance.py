#!/usr/bin/env python3
"""
高性能启动脚本
专为Windows打包后的exe文件设计，防止进入效能模式
"""

import os
import sys
import time
import signal
import logging
import subprocess
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def check_admin_privileges():
    """检查是否有管理员权限"""
    if sys.platform != "win32":
        return True
        
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def request_admin_privileges():
    """请求管理员权限"""
    if sys.platform != "win32":
        return True
        
    try:
        import ctypes
        if ctypes.windll.shell32.IsUserAnAdmin():
            return True
        else:
            print("🔐 需要管理员权限来设置高性能模式")
            print("正在请求管理员权限...")
            
            # 重新以管理员身份运行
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            return False
    except Exception as e:
        print(f"❌ 请求管理员权限失败: {e}")
        return False

def set_windows_high_performance():
    """设置Windows高性能模式"""
    if sys.platform != "win32":
        print("ℹ️ 非Windows系统，跳过电源设置")
        return True
        
    try:
        print("⚡ 正在设置Windows高性能电源计划...")
        
        # 高性能电源计划GUID
        high_perf_guid = "8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c"
        
        # 设置高性能电源计划
        result = subprocess.run(
            ["powercfg", "/setactive", high_perf_guid],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ 已切换到高性能电源计划")
            
            # 禁用USB选择性暂停
            subprocess.run([
                "powercfg", "/setacvalueindex", high_perf_guid, 
                "2a737441-1930-4402-8d77-b2bebba308a3", 
                "48e6b7a6-50f5-4782-a5d4-53bb8f07e226", "0"
            ], capture_output=True)
            
            # 设置处理器最小状态为100%
            subprocess.run([
                "powercfg", "/setacvalueindex", high_perf_guid,
                "54533251-82be-4824-96c1-47b60b740d00",
                "893dee8e-2bef-41e0-89c6-b55d0929964c", "100"
            ], capture_output=True)
            
            # 设置处理器最大状态为100%
            subprocess.run([
                "powercfg", "/setacvalueindex", high_perf_guid,
                "54533251-82be-4824-96c1-47b60b740d00",
                "bc5038f7-23e0-4960-96da-33abaf5935ec", "100"
            ], capture_output=True)
            
            # 应用设置
            subprocess.run(["powercfg", "/setactive", high_perf_guid], capture_output=True)
            
            print("🚀 高性能设置已优化")
            return True
            
        else:
            print(f"⚠️ 设置高性能模式失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ 设置电源计划超时")
        return False
    except Exception as e:
        print(f"❌ 设置高性能模式失败: {e}")
        return False

def optimize_process_priority():
    """优化进程优先级"""
    if sys.platform != "win32":
        return True
        
    try:
        import psutil
        
        # 获取当前进程
        current_process = psutil.Process()
        
        # 设置高优先级
        current_process.nice(psutil.HIGH_PRIORITY_CLASS)
        print("⚡ 已设置进程为高优先级")
        
        return True
        
    except ImportError:
        print("ℹ️ psutil未安装，跳过进程优先级设置")
        return True
    except Exception as e:
        print(f"⚠️ 设置进程优先级失败: {e}")
        return True

def setup_signal_handlers():
    """设置信号处理器"""
    def signal_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在优雅关闭...")
        
        # 恢复默认电源设置
        if sys.platform == "win32":
            try:
                print("🔋 恢复默认电源设置...")
                # 切换回平衡模式
                subprocess.run([
                    "powercfg", "/setactive", "381b4222-f694-41f0-9685-ff5bb260df2e"
                ], capture_output=True, timeout=5)
                print("✅ 已恢复平衡电源模式")
            except:
                pass
        
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def main():
    """主启动函数"""
    print("🚀 YF AI Chat Backend - 高性能模式启动")
    print("=" * 60)
    
    # 设置信号处理
    setup_signal_handlers()
    
    # 检查管理员权限（仅用于电源设置）
    has_admin = check_admin_privileges()
    if not has_admin:
        print("⚠️ 没有管理员权限，某些优化功能可能无法使用")
        print("建议以管理员身份运行以获得最佳性能")
    
    # 设置高性能模式
    if has_admin:
        set_windows_high_performance()
    else:
        print("ℹ️ 跳过电源计划设置（需要管理员权限）")
    
    # 优化进程优先级
    optimize_process_priority()
    
    # 检查环境
    try:
        from utils.path_utils import get_env_file_path
        from dotenv import load_dotenv
        
        env_file = get_env_file_path()
        if env_file.exists():
            load_dotenv(dotenv_path=env_file)
            print("✅ 环境配置加载成功")
        else:
            print("⚠️ 环境配置文件不存在")
    except Exception as e:
        print(f"⚠️ 环境配置加载失败: {e}")
    
    # 启动应用
    try:
        print("📦 正在加载应用...")
        
        # 导入应用
        from main import app, logger
        import uvicorn
        from logging_config import setup_logging_filters, configure_uvicorn_logging
        from power_management import get_power_status
        
        # 显示电源状态
        power_status = get_power_status()
        print(f"🔋 当前电源计划: {power_status['current_power_plan']}")
        print(f"🛡️ 电源保护状态: {'已启用' if power_status['power_protection_active'] else '未启用'}")
        
        # 设置日志过滤器
        setup_logging_filters()
        
        # 获取配置
        host = os.getenv("HOST", "0.0.0.0")
        port = int(os.getenv("PORT", 8000))
        https_mode = os.getenv("HTTPS_MODE", "disabled").lower()
        
        print(f"🌐 服务器地址: {host}:{port}")
        print(f"🔒 HTTPS模式: {https_mode}")
        
        # 高性能Uvicorn配置
        uvicorn_config = {
            "app": app,
            "host": host,
            "port": port,
            "reload": False,
            "access_log": False,
            "log_level": "warning",
            "server_header": False,
            "date_header": False,
            "timeout_keep_alive": 10,  # 增加保持连接时间
            "timeout_graceful_shutdown": 15,  # 增加优雅关闭时间
            "limit_concurrency": 2000,  # 增加并发限制
            "limit_max_requests": 50000,  # 增加最大请求数
            "backlog": 4096,  # 增加连接队列
            "log_config": configure_uvicorn_logging(),
            "loop": "uvloop" if sys.platform != "win32" else "asyncio",  # Linux使用uvloop
            "http": "httptools" if sys.platform != "win32" else "h11"  # Linux使用httptools
        }
        
        # SSL配置
        if https_mode == "direct":
            from utils.path_utils import get_ssl_cert_path, get_ssl_key_path
            cert_path = get_ssl_cert_path()
            key_path = get_ssl_key_path()
            
            if cert_path and key_path:
                uvicorn_config["ssl_certfile"] = str(cert_path)
                uvicorn_config["ssl_keyfile"] = str(key_path)
                print("🔒 SSL证书已配置")
        
        print("=" * 60)
        print("🎯 高性能优化已启用:")
        print("   ⚡ Windows高性能电源计划")
        print("   🚀 进程高优先级")
        print("   🛡️ 防止进入效能模式")
        print("   🔄 优化的连接参数")
        print("   📊 增强的并发处理")
        print("=" * 60)
        
        # 启动服务器
        uvicorn.run(**uvicorn_config)
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
