#!/usr/bin/env python3
"""
SalesGPT配置管理器主窗口
"""

from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QSplitter, QTreeWidget, QTreeWidgetItem, QStackedWidget,
                            QMenuBar, QStatusBar, QPushButton, QLabel, QMessageBox,
                            QProgressBar, QToolBar, QFileDialog)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon, QAction, QPixmap, QPainter, QColor, QBrush, QPen
import os
import traceback
from pathlib import Path

# 导入其他组件
from file_manager_widget import FileManagerWidget
from agent_config_widget import AgentConfigWidget
from product_management_widget import ProductManagementWidget
from sales_process_widget import SalesProcessWidget
from after_sales_widget import AfterSalesWidget
from advanced_settings_widget import AdvancedSettingsWidget
# 导入配置管理器（使用OmegaConf版本）
try:
    from omega_config_manager import OmegaConfigManager
    from config_utils import ui_settings_manager as settings_manager
    from config_utils import ui_data_connector as data_connector
    print("✅ OmegaConf配置管理器加载成功")
    CONFIG_MANAGER_AVAILABLE = True
except ImportError as e:
    print(f"警告: 配置管理器导入失败: {e}")
    config_manager = None
    settings_manager = None
    data_connector = None
    CONFIG_MANAGER_AVAILABLE = False


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()

        # 初始化属性
        self.config_manager = None
        self.settings_manager = None
        self.current_config_loaded = False
        self.config_stack = None
        self.nav_tree = None
        self.file_manager = None
        self.agent_config = None
        self.product_catalog = None

        try:
            # 使用OmegaConf配置管理器
            if CONFIG_MANAGER_AVAILABLE:
                self.config_manager = OmegaConfigManager()
                self.settings_manager = settings_manager
                print("✅ OmegaConf配置管理器初始化成功")
            else:
                raise NameError("OmegaConf配置管理器不可用")
        except Exception as e:
            print(f"警告: 配置管理器初始化失败: {e}")
            # 创建简单的替代对象
            self.config_manager = type('MockConfigManager', (), {
                'load_config_file': lambda self, *args: False,
                'save_config_files': lambda self: False,
                'validate_config': lambda self: [],
                'get_agent_config': lambda self: {},
                'set_agent_config': lambda self, x: None,
                'get_product_catalog': lambda self: "",
                'set_product_catalog': lambda self, x: None,
                'is_modified': False
            })()
            self.settings_manager = type('MockSettingsManager', (), {
                'add_recent_config': lambda self, *args: None,
                'get_recent_configs': lambda self: [],
                'settings': {'recent_configs': []},
                'save_settings': lambda self: None
            })()

        self.init_ui()
        self.setup_connections()
        self.setup_status_bar()

        # 设置窗口属性
        self.setWindowTitle("杨杋AI Agent配置管理器 v1.0")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # 设置窗口图标
        self.set_window_icon()

        # 连接数据组件
        self.connect_data_components()

        # 居中显示
        self.center_window()
    
    def init_ui(self):
        """初始化界面"""
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧导航树
        self.create_navigation_tree()
        splitter.addWidget(self.nav_tree)
        
        # 右侧配置面板
        self.create_config_panels()
        splitter.addWidget(self.config_stack)
        
        # 设置分割器比例
        splitter.setSizes([250, 950])
        main_layout.addWidget(splitter)
        
        # 应用样式
        self.apply_styles()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 新建配置
        new_action = QAction("新建配置(&N)", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_config)
        file_menu.addAction(new_action)
        
        # 打开配置
        open_action = QAction("打开配置(&O)", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self.open_config)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # 保存配置
        self.save_action = QAction("保存配置(&S)", self)
        self.save_action.setShortcut("Ctrl+S")
        self.save_action.triggered.connect(self.save_config)
        self.save_action.setEnabled(False)
        file_menu.addAction(self.save_action)
        
        # 另存为
        save_as_action = QAction("另存为(&A)", self)
        save_as_action.setShortcut("Ctrl+Shift+S")
        save_as_action.triggered.connect(self.save_as_config)
        file_menu.addAction(save_as_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")
        
        # 验证配置
        validate_action = QAction("验证配置(&V)", self)
        validate_action.setShortcut("F5")
        validate_action.triggered.connect(self.validate_config)
        edit_menu.addAction(validate_action)
        
        # 格式化
        format_action = QAction("格式化配置(&F)", self)
        format_action.triggered.connect(self.format_config)
        edit_menu.addAction(format_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # 保存按钮
        self.save_btn = QPushButton("💾 保存")
        self.save_btn.clicked.connect(self.save_config)
        self.save_btn.setEnabled(False)
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        toolbar.addWidget(self.save_btn)
        
        toolbar.addSeparator()
        
        # 验证按钮
        self.validate_btn = QPushButton("✅ 验证")
        self.validate_btn.clicked.connect(self.validate_config)
        toolbar.addWidget(self.validate_btn)
        
        # 格式化按钮
        self.format_btn = QPushButton("🎨 格式化")
        self.format_btn.clicked.connect(self.format_config)
        toolbar.addWidget(self.format_btn)
        
        toolbar.addSeparator()
        
        # 状态指示器
        self.status_label = QLabel("未加载配置")
        self.status_label.setStyleSheet("color: #666; padding: 5px;")
        toolbar.addWidget(self.status_label)
    
    def create_navigation_tree(self):
        """创建导航树"""
        self.nav_tree = QTreeWidget()
        self.nav_tree.setHeaderLabel("配置项目")
        self.nav_tree.setFont(QFont("Microsoft YaHei", 10))
        
        # 添加导航项
        items = [
            ("📁 文件管理", "file_manager", "选择和管理配置文件"),
            ("🤖 Agent配置", "agent_config", "设置AI代理基本信息"),
            ("📦 产品管理", "product_management", "统一管理产品信息、购买渠道、竞品对比"),
            ("🎯 销售流程", "sales_process", "配置销售阶段和策略"),
            ("🔧 售后支持", "after_sales", "配置售后服务和支持"),
            ("⚙️ 高级设置", "advanced", "高级配置和变量管理")
        ]
        
        for text, key, tooltip in items:
            item = QTreeWidgetItem([text])
            item.setData(0, Qt.ItemDataRole.UserRole, key)
            item.setToolTip(0, tooltip)
            self.nav_tree.addTopLevelItem(item)
        
        # 连接信号
        self.nav_tree.currentItemChanged.connect(self.on_nav_item_changed)
        
        # 默认选中第一项
        self.nav_tree.setCurrentItem(self.nav_tree.topLevelItem(0))
    
    def create_config_panels(self):
        """创建配置面板"""
        self.config_stack = QStackedWidget()
        
        # 文件管理面板
        self.file_manager = FileManagerWidget(self.settings_manager)
        self.config_stack.addWidget(self.file_manager)
        
        # Agent配置面板
        self.agent_config = AgentConfigWidget()
        self.config_stack.addWidget(self.agent_config)
        
        # 统一产品管理面板
        try:
            self.product_management = ProductManagementWidget()
            self.config_stack.addWidget(self.product_management)
        except Exception as e:
            print(f"产品管理组件加载失败: {e}")
            placeholder = QLabel("产品管理 - 加载失败")
            placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
            placeholder.setStyleSheet("color: #666; font-size: 16px;")
            self.config_stack.addWidget(placeholder)

        # 销售流程面板
        try:
            self.sales_process = SalesProcessWidget()
            self.config_stack.addWidget(self.sales_process)
        except Exception as e:
            print(f"销售流程组件加载失败: {e}")
            placeholder = QLabel("销售流程配置 - 加载失败")
            placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
            placeholder.setStyleSheet("color: #666; font-size: 16px;")
            self.config_stack.addWidget(placeholder)

        # 售后支持面板
        try:
            self.after_sales = AfterSalesWidget()
            self.config_stack.addWidget(self.after_sales)
        except Exception as e:
            print(f"售后支持组件加载失败: {e}")
            placeholder = QLabel("售后支持配置 - 加载失败")
            placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
            placeholder.setStyleSheet("color: #666; font-size: 16px;")
            self.config_stack.addWidget(placeholder)

        # 高级设置面板
        try:
            self.advanced_settings = AdvancedSettingsWidget()
            self.config_stack.addWidget(self.advanced_settings)
        except Exception as e:
            print(f"高级设置组件加载失败: {e}")
            placeholder = QLabel("高级设置配置 - 加载失败")
            placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
            placeholder.setStyleSheet("color: #666; font-size: 16px;")
            self.config_stack.addWidget(placeholder)
    
    def setup_connections(self):
        """设置信号连接"""
        # 文件管理器信号
        self.file_manager.files_loaded.connect(self.on_files_loaded)
        
        # 配置改变信号
        self.agent_config.config_changed.connect(self.on_config_changed)
        if hasattr(self, 'product_management'):
            self.product_management.config_changed.connect(self.on_config_changed)
        if hasattr(self, 'sales_process'):
            self.sales_process.config_changed.connect(self.on_config_changed)
        if hasattr(self, 'after_sales'):
            self.after_sales.config_changed.connect(self.on_config_changed)
        if hasattr(self, 'advanced_settings'):
            self.advanced_settings.config_changed.connect(self.on_config_changed)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 添加进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # 添加状态标签
        self.status_bar.showMessage("就绪")
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTreeWidget {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 5px;
            }
            QTreeWidget::item {
                padding: 8px;
                border-radius: 3px;
            }
            QTreeWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QTreeWidget::item:hover {
                background-color: #f5f5f5;
            }
            QStackedWidget {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ddd;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
    
    def create_app_icon(self):
        """创建应用程序图标"""
        size = 64
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.GlobalColor.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 绘制背景圆形
        painter.setBrush(QBrush(QColor(76, 175, 80)))  # 绿色背景
        painter.setPen(QPen(QColor(56, 142, 60), 2))   # 深绿色边框
        painter.drawRoundedRect(4, 4, size-8, size-8, 8, 8)

        # 绘制文字
        painter.setPen(QPen(QColor(255, 255, 255)))
        font = QFont("Microsoft YaHei", 14, QFont.Weight.Bold)
        painter.setFont(font)

        # 绘制"杨杋"
        painter.drawText(12, 28, "杨杋")

        # 绘制"AI"
        font.setPointSize(10)
        painter.setFont(font)
        painter.drawText(22, 48, "AI")

        painter.end()

        return pixmap

    def set_window_icon(self):
        """设置窗口图标"""
        try:
            # 首先尝试加载外部图标文件
            icon_paths = [
                "resources/app_icon.png",
                "resources/text_icon.png",
                "resources/app_icon.ico",
                "../resources/app_icon.png"
            ]

            icon_loaded = False
            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    icon = QIcon(icon_path)
                    if not icon.isNull():
                        self.setWindowIcon(icon)
                        icon_loaded = True
                        print(f"✅ 已加载图标: {icon_path}")
                        break

            # 如果没有找到外部图标，创建一个内置图标
            if not icon_loaded:
                pixmap = self.create_app_icon()
                icon = QIcon(pixmap)
                self.setWindowIcon(icon)
                print("✅ 已使用内置图标")

        except Exception as e:
            print(f"⚠️ 设置图标失败: {e}")
            # 使用默认图标
            pass

    def connect_data_components(self):
        """连接数据组件（已简化：移除DataConnector）"""
        try:
            # 简化架构：GUI专注于配置文件编辑，不需要动态数据连接
            # 所有数据都直接从配置文件加载和保存
            if hasattr(self, 'product_management') and hasattr(self, 'sales_process'):
                print("✅ 组件初始化完成（配置文件驱动模式）")
            else:
                print("ℹ️ 部分组件未初始化")
        except Exception as e:
            print(f"❌ 组件初始化失败: {e}")

    def center_window(self):
        """居中显示窗口"""
        screen = self.screen().availableGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def on_nav_item_changed(self, current, previous):
        """导航项改变时的处理"""
        if current and self.config_stack:
            key = current.data(0, Qt.ItemDataRole.UserRole)
            index_map = {
                "file_manager": 0,
                "agent_config": 1,
                "product_management": 2,
                "sales_process": 3,
                "after_sales": 4,
                "advanced": 5
            }

            index = index_map.get(key, 0)
            self.config_stack.setCurrentIndex(index)
    
    def on_files_loaded(self, config_path):
        """文件加载完成时的处理"""
        import logging
        logging.info(f"开始加载配置文件: {config_path}")

        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            logging.info("进度条显示成功")

            # 加载统一配置文件
            logging.info("调用config_manager.load_config_file...")
            success = self.config_manager.load_config_file(config_path)
            logging.info(f"配置文件加载结果: {success}")
            self.progress_bar.setValue(20)

            if success:
                # 更新Agent配置面板
                logging.info("开始加载Agent配置...")
                self.agent_config.load_config(self.config_manager.get_agent_config())
                logging.info("Agent配置加载完成")
                self.progress_bar.setValue(35)

                # 加载产品管理数据
                logging.info("开始加载产品管理数据...")
                if hasattr(self, 'product_management') and hasattr(self.product_management, 'load_products_data'):
                    try:
                        # 从统一配置中获取产品数据
                        products_data = self.config_manager.get_products_config()
                        if products_data:
                            self.product_management.load_products_data(products_data)
                            logging.info(f"✅ 已加载 {len(products_data)} 个产品")
                            print(f"✅ 已加载 {len(products_data)} 个产品")
                        else:
                            logging.warning("⚠️ 配置文件中未找到产品数据")
                            print("⚠️ 配置文件中未找到产品数据")
                    except Exception as e:
                        logging.error(f"加载产品数据失败: {e}")
                        print(f"加载产品数据失败: {e}")
                logging.info("产品管理数据加载完成")
                self.progress_bar.setValue(50)

                # 加载销售流程数据
                logging.info("开始加载销售流程数据...")
                if hasattr(self, 'sales_process') and hasattr(self.sales_process, 'load_config'):
                    try:
                        sales_data = self.config_manager.get_sales_process_config()
                        if sales_data:
                            self.sales_process.load_config(sales_data)
                            logging.info("✅ 已加载销售流程配置")
                            print("✅ 已加载销售流程配置")
                        else:
                            logging.warning("⚠️ 配置文件中未找到销售流程数据")
                            print("⚠️ 配置文件中未找到销售流程数据")
                    except Exception as e:
                        logging.error(f"加载销售流程数据失败: {e}")
                        print(f"加载销售流程数据失败: {e}")
                logging.info("销售流程数据加载完成")
                self.progress_bar.setValue(65)

                # 加载售后支持数据
                logging.info("开始加载售后支持数据...")
                if hasattr(self, 'after_sales') and hasattr(self.after_sales, 'load_config'):
                    try:
                        after_sales_data = self.config_manager.get_after_sales_config()
                        print(f"🔍 获取到的售后支持数据: {list(after_sales_data.keys()) if after_sales_data else '无数据'}")
                        if after_sales_data:
                            self.after_sales.load_config(after_sales_data)
                            logging.info("✅ 已加载售后支持配置")
                            print("✅ 已加载售后支持配置")
                        else:
                            # 尝试从配置中查找所有可能的售后支持相关键
                            all_config = self.config_manager.get_config_data()
                            print(f"🔍 配置文件中的所有键: {list(all_config.keys()) if all_config else '无配置'}")

                            # 查找包含"after"、"support"等关键词的键
                            related_keys = [k for k in all_config.keys() if any(word in k.lower() for word in ['after', 'support', 'service'])]
                            if related_keys:
                                print(f"🔍 找到相关键: {related_keys}")

                            logging.warning("⚠️ 配置文件中未找到售后支持数据")
                            print("⚠️ 配置文件中未找到售后支持数据")
                    except Exception as e:
                        logging.error(f"加载售后支持数据失败: {e}")
                        print(f"加载售后支持数据失败: {e}")
                logging.info("售后支持数据加载完成")
                self.progress_bar.setValue(80)

                # 加载高级设置数据
                logging.info("开始加载高级设置数据...")
                if hasattr(self, 'advanced_settings') and hasattr(self.advanced_settings, 'load_config'):
                    try:
                        # 收集所有高级配置
                        advanced_data = {
                            'escalation_rules': self.config_manager.get_escalation_rules_config(),
                            'conversation_rules': self.config_manager.get_conversation_rules_config(),
                            'personalization': self.config_manager.get_personalization_config()
                        }
                        self.advanced_settings.load_config(advanced_data)
                        logging.info("✅ 已加载高级设置配置")
                        print("✅ 已加载高级设置配置")
                    except Exception as e:
                        logging.error(f"加载高级设置数据失败: {e}")
                        print(f"加载高级设置数据失败: {e}")
                logging.info("高级设置数据加载完成")
                self.progress_bar.setValue(100)

                self.current_config_loaded = True
                self.save_action.setEnabled(True)
                self.save_btn.setEnabled(True)

                self.status_label.setText("✅ 配置已加载")
                self.status_bar.showMessage(f"已加载: {config_path}")
                logging.info("配置加载完全成功")

                # 移除加载成功弹窗，只在状态栏显示
            else:
                logging.error("配置文件加载失败")
                QMessageBox.critical(self, "错误", "配置文件加载失败！")

        except Exception as e:
            logging.error(f"配置加载过程中发生异常: {e}")
            logging.error(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"配置加载失败: {e}")
        finally:
            self.progress_bar.setVisible(False)
            logging.info("配置加载过程结束")
    
    def on_config_changed(self):
        """配置改变时的处理"""
        if self.current_config_loaded:
            self.config_manager.is_modified = True
            self.status_label.setText("⚠️ 配置已修改")
            self.setWindowTitle("杨杋AI Agent配置管理器 v1.0 *")
    
    def new_config(self):
        """新建配置"""
        # TODO: 实现新建配置功能
        QMessageBox.information(self, "提示", "新建配置功能开发中...")
    
    def open_config(self):
        """打开配置"""
        # TODO: 实现打开配置功能
        QMessageBox.information(self, "提示", "打开配置功能开发中...")
    
    def save_config(self):
        """保存配置"""
        if not self.current_config_loaded:
            QMessageBox.warning(self, "警告", "请先加载配置文件！")
            return

        try:
            print("🚀 开始OmegaConf保存流程...")

            # 🔄 在获取配置前，先调用各模块的update方法同步UI数据
            print("🔄 开始同步UI数据到内部存储...")

            # 同步销售流程数据
            if hasattr(self, 'sales_process'):
                if hasattr(self.sales_process, 'update_current_stage_data'):
                    self.sales_process.update_current_stage_data()
                if hasattr(self.sales_process, 'update_current_objection_data'):
                    self.sales_process.update_current_objection_data()
                print("✅ 销售流程数据已同步")

            # 同步售后支持数据
            if hasattr(self, 'after_sales'):
                if hasattr(self.after_sales, 'update_current_field_data'):
                    self.after_sales.update_current_field_data()
                print("✅ 售后支持数据已同步")

            # 同步高级设置数据
            if hasattr(self, 'advanced_settings'):
                if hasattr(self.advanced_settings, 'update_current_trigger_data'):
                    self.advanced_settings.update_current_trigger_data()
                print("✅ 高级设置数据已同步")

            print("🔄 UI数据同步完成，开始收集配置数据...")

            # 🎯 使用OmegaConf直接更新配置
            # Agent配置
            if hasattr(self, 'agent_config'):
                agent_config = self.agent_config.get_config()
                if agent_config:
                    self.config_manager.config.agent_profile = agent_config
                    print("💾 Agent配置已更新到OmegaConf")

            # 产品配置
            if hasattr(self, 'product_management'):
                if hasattr(self.product_management, 'get_config_data'):
                    products_data = self.product_management.get_config_data()
                    if products_data:
                        self.config_manager.config.products = products_data
                        print(f"💾 产品配置已更新到OmegaConf，包含 {len(products_data)} 个产品")
                elif hasattr(self.product_management, 'get_config'):
                    products_data = self.product_management.get_config()
                    if products_data:
                        self.config_manager.config.products = products_data
                        print(f"💾 产品配置已更新到OmegaConf，包含 {len(products_data)} 个产品")

            # 销售流程配置
            if hasattr(self, 'sales_process') and hasattr(self.sales_process, 'get_config'):
                print("🔍 开始获取销售流程配置...")
                sales_config = self.sales_process.get_config()
                if sales_config:
                    print(f"💾 销售流程配置，包含 {len(sales_config.get('stages', {}))} 个阶段")
                    for stage_key, stage_data in sales_config.get('stages', {}).items():
                        print(f"  📝 保存阶段 {stage_key}:")
                        print(f"    名称: {stage_data.get('name', 'No Name')}")
                        print(f"    目标: {stage_data.get('objective', 'No Objective')[:50]}...")
                        print(f"    关键行动: {len(stage_data.get('key_actions', []))} 项")
                        print(f"    触发条件: {len(stage_data.get('transition_triggers', []))} 项")
                        print(f"    建议问题: {len(stage_data.get('questions', []))} 项")

                    # 保存前检查当前OmegaConf中的数据
                    if hasattr(self.config_manager.config, 'sales_process') and hasattr(self.config_manager.config.sales_process, 'stages'):
                        old_stages = self.config_manager.config.sales_process.stages
                        print(f"  🔍 OmegaConf中原有 {len(old_stages)} 个阶段")
                        for stage_key in sales_config.get('stages', {}):
                            if stage_key in old_stages:
                                old_objective = old_stages[stage_key].get('objective', '')
                                new_objective = sales_config['stages'][stage_key].get('objective', '')
                                if old_objective != new_objective:
                                    print(f"    🔄 阶段 {stage_key} 目标发生变化:")
                                    print(f"      旧: {old_objective[:50]}...")
                                    print(f"      新: {new_objective[:50]}...")

                    # 使用和Agent配置一样的直接赋值方式
                    self.config_manager.config.sales_process = sales_config
                    print("💾 销售流程配置已更新到OmegaConf")

            # 售后支持配置
            if hasattr(self, 'after_sales') and hasattr(self.after_sales, 'get_config'):
                after_sales_config = self.after_sales.get_config()
                if after_sales_config:
                    # 使用和Agent配置一样的直接赋值方式
                    self.config_manager.config.after_sales_support = after_sales_config
                    print("💾 售后支持配置已更新到OmegaConf")

            # 高级设置配置
            if hasattr(self, 'advanced_settings') and hasattr(self.advanced_settings, 'get_config'):
                advanced_config = self.advanced_settings.get_config()
                if advanced_config:
                    # 分别保存各个高级配置部分
                    if 'escalation_rules' in advanced_config:
                        self.config_manager.config.escalation_rules = advanced_config['escalation_rules']
                    if 'conversation_rules' in advanced_config:
                        self.config_manager.config.conversation_rules = advanced_config['conversation_rules']
                    if 'personalization' in advanced_config:
                        self.config_manager.config.personalization = advanced_config['personalization']
                    print("💾 高级设置配置已更新到OmegaConf")

            # 🎯 直接保存OmegaConf配置（不需要set_config_data）
            print("💾 开始保存OmegaConf配置到文件...")
            success = self.config_manager.save_config_file()

            if success:
                self.status_label.setText("✅ 配置已保存")
                self.setWindowTitle("杨杋AI Agent配置管理器 v1.0")
                self.status_bar.showMessage("配置保存成功", 3000)
                print("✅ 配置保存成功")

                # 验证保存结果
                print("🔍 验证保存结果...")
                if hasattr(self.config_manager.config, 'sales_process') and hasattr(self.config_manager.config.sales_process, 'stages'):
                    saved_stages = self.config_manager.config.sales_process.stages
                    print(f"  📁 文件中保存了 {len(saved_stages)} 个销售阶段")
                    for stage_key, stage_data in saved_stages.items():
                        print(f"    阶段 {stage_key}: {stage_data.get('name', 'No Name')} - {stage_data.get('objective', '')[:50]}...")

                QMessageBox.information(self, "成功", "配置保存成功！")
            else:
                QMessageBox.critical(self, "错误", "配置保存失败！")
                print("❌ 配置保存失败")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存配置时发生错误：{str(e)}")
            print(f"保存配置失败: {e}")
    
    def save_as_config(self):
        """另存为配置"""
        # TODO: 实现另存为功能
        QMessageBox.information(self, "提示", "另存为功能开发中...")
    
    def validate_config(self):
        """基本配置检查"""
        if not self.current_config_loaded:
            QMessageBox.warning(self, "警告", "请先加载配置文件！")
            return

        # 简化验证：只检查基本格式
        errors = self.config_manager.validate_config()

        if errors:
            error_text = "发现以下格式问题:\n\n" + "\n".join(f"• {error}" for error in errors)
            QMessageBox.warning(self, "格式检查", error_text)
        else:
            QMessageBox.information(self, "格式检查", "✅ 配置文件格式正确！")
    
    def format_config(self):
        """格式化配置（简化版）"""
        # 配置文件编辑器不需要复杂的格式化功能
        # JSON格式化由编辑器自动处理
        QMessageBox.information(self, "提示", "配置文件格式化由编辑器自动处理")
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", """
杨杋agent配置管理器 v1.0

一个专为杨杋AI销售代理设计的配置文件编辑工具

功能特点:
• 可视化配置编辑
• 实时配置验证
• 文件历史记录
• 模板和向导
• 中文界面支持

开发: jiang <EMAIL>
版本: 1.0.0
        """)
    
    def closeEvent(self, event):
        """关闭事件处理"""
        if self.config_manager.is_modified:
            reply = QMessageBox.question(
                self,
                "确认退出",
                "配置已修改但未保存，确定要退出吗？",
                QMessageBox.StandardButton.Save | 
                QMessageBox.StandardButton.Discard | 
                QMessageBox.StandardButton.Cancel
            )
            
            if reply == QMessageBox.StandardButton.Save:
                self.save_config()
                event.accept()
            elif reply == QMessageBox.StandardButton.Discard:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
