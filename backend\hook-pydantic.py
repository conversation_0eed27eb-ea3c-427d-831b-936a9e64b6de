# PyInstaller hook for pydantic
from PyInstaller.utils.hooks import collect_all

# 收集所有pydantic相关的模块
datas, binaries, hiddenimports = collect_all('pydantic')

# 添加额外的隐藏导入
additional_imports = [
    'pydantic.deprecated',
    'pydantic.deprecated.class_validators',
    'pydantic.deprecated.config',
    'pydantic.deprecated.copy_internals',
    'pydantic.deprecated.decorator',
    'pydantic.deprecated.json',
    'pydantic.deprecated.parse',
    'pydantic.deprecated.tools',
    'pydantic._internal',
    'pydantic._internal._validators',
    'pydantic._internal._fields',
    'pydantic._internal._model_construction',
    'pydantic._internal._typing_extra',
    'pydantic._internal._utils',
    'pydantic._internal._core_utils',
    'pydantic._internal._decorators',
    'pydantic._internal._generate_schema',
    'pydantic._internal._mock_val_ser',
    'pydantic._internal._std_types_schema',
    'pydantic.v1',
    'pydantic_core',
    'pydantic_core._pydantic_core',
]

hiddenimports.extend(additional_imports)
