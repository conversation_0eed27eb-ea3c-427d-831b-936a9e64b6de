#!/usr/bin/env python3
"""
YF AI Chat Token管理工具
用于生成和管理不同类型的访问令牌
"""

import os
import sys
import secrets
import string
import json
from datetime import datetime
from pathlib import Path

def generate_secure_token(length=32):
    """生成安全的随机token"""
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def save_tokens_to_file(tokens):
    """将生成的token保存到exe所在文件夹的文件中"""
    # 获取exe所在的目录
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe文件
        exe_dir = Path(os.path.dirname(sys.executable))
    else:
        # 如果是Python脚本
        exe_dir = Path(os.path.dirname(os.path.abspath(__file__)))

    tokens_file = exe_dir / "generated_tokens.json"

    # 创建token记录
    token_record = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "tokens": tokens
    }

    # 读取现有记录
    existing_records = []
    if tokens_file.exists():
        try:
            with open(tokens_file, 'r', encoding='utf-8') as f:
                existing_records = json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            existing_records = []

    # 添加新记录
    existing_records.append(token_record)

    # 保存到文件
    with open(tokens_file, 'w', encoding='utf-8') as f:
        json.dump(existing_records, f, ensure_ascii=False, indent=2)

    print(f"✅ Token已保存到: {tokens_file}")
    return tokens_file



def generate_tokens():
    """生成所有类型的token"""
    print("🔐 YF AI Chat Token管理器")
    print("=" * 50)

    # 生成新token
    tokens = {
        "WORDPRESS_TOKEN": generate_secure_token(),
        "ADMIN_TOKEN": generate_secure_token(),
        "API_TOKEN": generate_secure_token()
    }

    print("✅ 已生成新的安全token:")
    print()

    for token_type, token_value in tokens.items():
        print(f"{token_type}:")
        print(f"  {token_value}")
        print()

    # 保存token到文件
    save_tokens_to_file(tokens)
    print()

    print("📋 Token用途说明:")
    print("  WORDPRESS_TOKEN: WordPress插件使用，只能访问聊天API")
    print("  ADMIN_TOKEN: 客服端使用，可以访问管理功能")
    print("  API_TOKEN: 通用API访问，保持向后兼容")
    print()
    print("🔒 安全建议:")
    print("  1. 定期更换token")
    print("  2. 不要在代码中硬编码token")
    print("  3. 为不同环境使用不同的token")
    print("  4. 妥善保管token，避免泄露")

    return tokens

def main():
    """主函数"""
    print("🔐 YF AI Chat Token管理工具")
    print("=" * 60)
    print()
    print("选择操作:")
    print("1. 生成新的token")
    print("2. 退出")
    print()

    while True:
        choice = input("请选择 (1-2): ").strip()

        if choice == "1":
            print()
            generate_tokens()
            print()
            input("按回车键退出...")
            break
        elif choice == "2":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请输入 1-2")

if __name__ == "__main__":
    main()
