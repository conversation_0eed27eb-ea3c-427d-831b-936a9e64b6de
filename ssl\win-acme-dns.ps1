# Win-ACME Cloudflare DNS Hook Script
# 用于自动创建和删除DNS TXT记录进行域名验证

param(
    [string]$create,
    [string]$delete
)

# Cloudflare配置
$CF_API_TOKEN = "****************************************"
$CF_ZONE_ID = "e92deb270a88a0a6865e6347cd88d96e"
$CF_API_BASE = "https://api.cloudflare.com/client/v4"

# 日志文件
$LogFile = "ssl\win-acme-dns.log"

function Write-Log {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] $Message"
    Write-Host $logMessage
    Add-Content -Path $LogFile -Value $logMessage -Encoding UTF8
}

function Create-TXTRecord {
    param(
        [string]$RecordName,
        [string]$RecordValue
    )
    
    Write-Log "创建TXT记录: $RecordName"
    Write-Log "记录值: $RecordValue"
    
    $headers = @{
        "Authorization" = "Bearer $CF_API_TOKEN"
        "Content-Type" = "application/json"
    }
    
    $body = @{
        type = "TXT"
        name = $RecordName
        content = $RecordValue
        ttl = 120
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri "$CF_API_BASE/zones/$CF_ZONE_ID/dns_records" -Method POST -Headers $headers -Body $body
        
        if ($response.success) {
            Write-Log "TXT记录创建成功，ID: $($response.result.id)"
            
            # 保存记录ID用于后续删除
            $recordInfo = "$RecordName|$($response.result.id)"
            Add-Content -Path "ssl\dns-records.txt" -Value $recordInfo -Encoding UTF8
            
            # 等待DNS传播
            Write-Log "等待DNS传播 (60秒)..."
            Start-Sleep -Seconds 60
            
            return $true
        } else {
            Write-Log "创建失败: $($response.errors | ConvertTo-Json)"
            return $false
        }
    } catch {
        Write-Log "API调用异常: $($_.Exception.Message)"
        return $false
    }
}

function Delete-TXTRecord {
    param([string]$RecordName)
    
    Write-Log "删除TXT记录: $RecordName"
    
    $recordsFile = "ssl\dns-records.txt"
    if (Test-Path $recordsFile) {
        $records = Get-Content $recordsFile -Encoding UTF8
        foreach ($record in $records) {
            if ($record -match "^$RecordName\|(.+)$") {
                $recordId = $matches[1]
                
                $headers = @{
                    "Authorization" = "Bearer $CF_API_TOKEN"
                }
                
                try {
                    $response = Invoke-RestMethod -Uri "$CF_API_BASE/zones/$CF_ZONE_ID/dns_records/$recordId" -Method DELETE -Headers $headers
                    
                    if ($response.success) {
                        Write-Log "TXT记录删除成功: $recordId"
                        
                        # 从文件中移除记录
                        $remainingRecords = $records | Where-Object { $_ -ne $record }
                        if ($remainingRecords) {
                            $remainingRecords | Set-Content $recordsFile -Encoding UTF8
                        } else {
                            Remove-Item $recordsFile -Force
                        }
                        
                        return $true
                    } else {
                        Write-Log "删除失败: $($response.errors | ConvertTo-Json)"
                    }
                } catch {
                    Write-Log "删除API调用异常: $($_.Exception.Message)"
                }
                break
            }
        }
    }
    
    Write-Log "未找到记录: $RecordName"
    return $false
}

# 确保ssl目录存在
if (!(Test-Path "ssl")) {
    New-Item -ItemType Directory -Path "ssl" -Force
}

# 主逻辑
Write-Log "=== Win-ACME DNS Hook 开始 ==="
Write-Log "参数 - create: $create"
Write-Log "参数 - delete: $delete"

if ($create) {
    # 解析创建参数 (格式: "recordname:recordvalue")
    if ($create -match "^(.+):(.+)$") {
        $recordName = $matches[1]
        $recordValue = $matches[2]
        
        $success = Create-TXTRecord -RecordName $recordName -RecordValue $recordValue
        if ($success) {
            Write-Log "DNS验证记录创建完成"
            exit 0
        } else {
            Write-Log "DNS验证记录创建失败"
            exit 1
        }
    } else {
        Write-Log "创建参数格式错误: $create"
        exit 1
    }
}

if ($delete) {
    $success = Delete-TXTRecord -RecordName $delete
    if ($success) {
        Write-Log "DNS验证记录删除完成"
        exit 0
    } else {
        Write-Log "DNS验证记录删除失败"
        exit 1
    }
}

Write-Log "未指定有效操作"
exit 1
