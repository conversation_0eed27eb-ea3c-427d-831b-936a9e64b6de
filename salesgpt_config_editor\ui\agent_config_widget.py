#!/usr/bin/env python3
"""
Agent配置组件
负责AI代理基本信息的编辑
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QTextEdit, QComboBox, QGroupBox,
                            QFormLayout, QScrollArea, QListWidget, QListWidgetItem,
                            QPushButton, QInputDialog, QMessageBox)
from PyQt6.QtCore import pyqtSignal, Qt
from PyQt6.QtGui import QFont
# variable_text_editor已移除，使用简单的QLineEdit替代


class AgentConfigWidget(QWidget):
    """Agent配置组件"""
    
    # 信号定义
    config_changed = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.config_data = {}
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 主容器
        main_widget = QWidget()
        layout = QVBoxLayout(main_widget)
        
        # 基本信息组
        basic_group = QGroupBox("🤖 AI代理基本信息")
        basic_group.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        basic_layout = QFormLayout(basic_group)
        
        # Agent名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("例如: Dylan, 小智, 客服助手")
        self.name_edit.textChanged.connect(self.on_config_changed)
        basic_layout.addRow("Agent名称:", self.name_edit)
        
        # 添加说明标签
        name_help = QLabel("💡 这是客户看到的AI代理名称，建议使用友好、易记的名字")
        name_help.setStyleSheet("color: #666; font-size: 11px; margin-left: 20px;")
        basic_layout.addRow("", name_help)
        
        # Agent角色
        self.role_combo = QComboBox()
        self.role_combo.setEditable(True)
        self.role_combo.addItems([
            "Sales & Customer Service Specialist",  # 销售和客服专家（推荐）
            "Customer Success Manager",             # 客户成功经理
            "Sales & Support Consultant",           # 销售和支持顾问
            "Product & Service Advisor",            # 产品和服务顾问
            "Integrated Service Representative",    # 综合服务代表
            "Sales Consultant",                     # 传统销售顾问
            "Customer Service Representative",      # 传统客服代表
            "Technical Support Specialist"          # 技术支持专家
        ])
        self.role_combo.currentTextChanged.connect(self.on_config_changed)
        basic_layout.addRow("Agent角色:", self.role_combo)
        
        role_help = QLabel("💡 AI的职位角色，影响对话风格和专业程度。推荐选择'Sales & Customer Service Specialist'用于销售+售后一体化")
        role_help.setStyleSheet("color: #666; font-size: 11px; margin-left: 20px;")
        role_help.setWordWrap(True)
        basic_layout.addRow("", role_help)
        
        # 公司名称
        self.company_edit = QLineEdit()
        self.company_edit.setPlaceholderText("例如: KNKA Environmental Appliances")
        self.company_edit.textChanged.connect(self.on_config_changed)
        basic_layout.addRow("公司名称:", self.company_edit)
        
        company_help = QLabel("💡 AI会向客户介绍自己来自这家公司")
        company_help.setStyleSheet("color: #666; font-size: 11px; margin-left: 20px;")
        basic_layout.addRow("", company_help)
        
        # 公司网站
        self.website_edit = QLineEdit()
        self.website_edit.setPlaceholderText("例如: knkalife.com")
        self.website_edit.textChanged.connect(self.on_config_changed)
        basic_layout.addRow("公司网站:", self.website_edit)
        
        website_help = QLabel("💡 AI会向客户推荐访问的网站地址")
        website_help.setStyleSheet("color: #666; font-size: 11px; margin-left: 20px;")
        basic_layout.addRow("", website_help)
        
        layout.addWidget(basic_group)
        
        # 业务描述组
        business_group = QGroupBox("🏢 公司业务描述")
        business_group.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        business_layout = QVBoxLayout(business_group)
        
        business_help = QLabel("💡 详细描述公司的业务范围和特色，AI会用这些信息介绍公司背景")
        business_help.setStyleSheet("color: #666; font-size: 11px;")
        business_layout.addWidget(business_help)
        
        self.business_edit = QTextEdit()
        self.business_edit.setPlaceholderText(
            "例如: 专业的环境电器公司，专注于智能空气解决方案，"
            "包括除湿机和空气净化器，产品既高效又美观..."
        )
        self.business_edit.setMaximumHeight(100)
        self.business_edit.textChanged.connect(self.on_config_changed)
        business_layout.addWidget(self.business_edit)
        
        layout.addWidget(business_group)
        
        # 个性化设置组
        personality_group = QGroupBox("🎭 AI个性化设置")
        personality_group.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        personality_layout = QFormLayout(personality_group)
        
        # 对话风格
        self.style_edit = QLineEdit()
        self.style_edit.setPlaceholderText("例如: professional, consultative, helpful")
        self.style_edit.textChanged.connect(self.on_config_changed)
        personality_layout.addRow("对话风格:", self.style_edit)
        
        style_help = QLabel("💡 定义AI的对话风格，如专业的、咨询式的、乐于助人的")
        style_help.setStyleSheet("color: #666; font-size: 11px; margin-left: 20px;")
        personality_layout.addRow("", style_help)
        
        # 语调
        self.tone_edit = QLineEdit()
        self.tone_edit.setPlaceholderText("例如: friendly but authoritative")
        self.tone_edit.textChanged.connect(self.on_config_changed)
        personality_layout.addRow("语调:", self.tone_edit)
        
        tone_help = QLabel("💡 AI说话的语调，如友好但权威的、轻松的、正式的")
        tone_help.setStyleSheet("color: #666; font-size: 11px; margin-left: 20px;")
        personality_layout.addRow("", tone_help)
        
        # 方法
        self.approach_edit = QLineEdit()
        self.approach_edit.setPlaceholderText("例如: solution-oriented")
        self.approach_edit.textChanged.connect(self.on_config_changed)
        personality_layout.addRow("方法:", self.approach_edit)
        
        approach_help = QLabel("💡 AI的工作方法，如以解决方案为导向、以客户为中心")
        approach_help.setStyleSheet("color: #666; font-size: 11px; margin-left: 20px;")
        personality_layout.addRow("", approach_help)
        
        layout.addWidget(personality_group)
        
        # 专业领域组
        expertise_group = QGroupBox("🎯 专业领域")
        expertise_group.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        expertise_layout = QVBoxLayout(expertise_group)
        
        expertise_help = QLabel("💡 定义AI的专业领域，影响产品推荐和专业建议")
        expertise_help.setStyleSheet("color: #666; font-size: 11px;")
        expertise_layout.addWidget(expertise_help)
        
        # 专业领域列表
        expertise_list_layout = QHBoxLayout()
        
        self.expertise_list = QListWidget()
        self.expertise_list.setMaximumHeight(120)
        expertise_list_layout.addWidget(self.expertise_list)
        
        # 专业领域操作按钮
        expertise_btn_layout = QVBoxLayout()
        
        self.add_expertise_btn = QPushButton("➕ 添加")
        self.add_expertise_btn.clicked.connect(self.add_expertise)
        expertise_btn_layout.addWidget(self.add_expertise_btn)
        
        self.remove_expertise_btn = QPushButton("➖ 删除")
        self.remove_expertise_btn.clicked.connect(self.remove_expertise)
        expertise_btn_layout.addWidget(self.remove_expertise_btn)
        
        expertise_btn_layout.addStretch()
        expertise_list_layout.addLayout(expertise_btn_layout)
        
        expertise_layout.addLayout(expertise_list_layout)
        
        layout.addWidget(expertise_group)
        
        layout.addStretch()
        
        # 设置滚动区域
        scroll.setWidget(main_widget)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(scroll)
    
    def load_config(self, config_data):
        """加载配置数据"""
        self.config_data = config_data
        
        # 加载基本信息
        self.name_edit.setText(config_data.get('name', ''))
        self.role_combo.setCurrentText(config_data.get('role', ''))
        self.company_edit.setText(config_data.get('company', ''))
        self.website_edit.setText(config_data.get('website', ''))
        self.business_edit.setPlainText(config_data.get('business_description', ''))
        
        # 加载个性化设置
        personality = config_data.get('personality', {})
        self.style_edit.setText(personality.get('style', ''))
        self.tone_edit.setText(personality.get('tone', ''))
        self.approach_edit.setText(personality.get('approach', ''))
        
        # 加载专业领域
        self.expertise_list.clear()
        expertise = config_data.get('expertise', [])
        for item in expertise:
            self.expertise_list.addItem(item)
    
    def get_config(self):
        """获取当前配置"""
        # 收集专业领域
        expertise = []
        for i in range(self.expertise_list.count()):
            expertise.append(self.expertise_list.item(i).text())
        
        return {
            'name': self.name_edit.text(),
            'role': self.role_combo.currentText(),
            'company': self.company_edit.text(),
            'website': self.website_edit.text(),
            'business_description': self.business_edit.toPlainText(),
            'personality': {
                'style': self.style_edit.text(),
                'tone': self.tone_edit.text(),
                'approach': self.approach_edit.text()
            },
            'expertise': expertise
        }
    
    def add_expertise(self):
        """添加专业领域"""
        text, ok = QInputDialog.getText(
            self, 
            "添加专业领域", 
            "请输入专业领域:",
            text="air_purifiers"
        )
        
        if ok and text:
            self.expertise_list.addItem(text)
            self.on_config_changed()
    
    def remove_expertise(self):
        """删除专业领域"""
        current_row = self.expertise_list.currentRow()
        if current_row >= 0:
            self.expertise_list.takeItem(current_row)
            self.on_config_changed()
    
    def on_config_changed(self):
        """配置改变时的处理"""
        self.config_changed.emit()
