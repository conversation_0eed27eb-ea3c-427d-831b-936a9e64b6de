<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YF AI Chat Shopify Plugin Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        
        #console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 YF AI Chat Shopify Plugin 测试页面</h1>
        
        <div class="test-section">
            <h2>📋 测试状态</h2>
            <div id="config-status" class="status info">正在检查配置...</div>
            <div id="files-status" class="status info">正在检查文件...</div>
            <div id="init-status" class="status info">正在检查初始化...</div>
        </div>
        
        <div class="test-section">
            <h2>🔧 手动测试</h2>
            <button onclick="testConfiguration()">测试配置</button>
            <button onclick="testAPI()">测试API连接</button>
            <button onclick="testChatWidget()">测试聊天组件</button>
            <button onclick="clearConsole()">清除日志</button>
        </div>
        
        <div class="test-section">
            <h2>📝 控制台输出</h2>
            <div id="console-output"></div>
        </div>
        
        <div class="test-section">
            <h2>📖 说明</h2>
            <p><strong>使用方法：</strong></p>
            <ol>
                <li>确保已将插件文件上传到Shopify主题</li>
                <li>在theme.liquid中添加了 <code>{% include 'yf-chat-widget' %}</code></li>
                <li>配置了正确的API URL和Token</li>
                <li>点击上方按钮进行各项测试</li>
            </ol>
            
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>✅ 配置状态：绿色表示配置正确</li>
                <li>✅ 文件状态：绿色表示文件加载成功</li>
                <li>✅ 初始化状态：绿色表示聊天组件初始化成功</li>
                <li>✅ 页面右下角应该显示聊天按钮</li>
            </ul>
        </div>
    </div>

    <!-- 模拟Shopify环境 -->
    <script>
        // 模拟Shopify Analytics对象
        window.ShopifyAnalytics = {
            meta: {
                product: {
                    id: 12345,
                    title: "测试商品",
                    vendor: "测试品牌",
                    type: "测试类型",
                    price: 9999
                }
            }
        };
    </script>

    <!-- YF AI Chat 配置 -->
    <script>
        // 配置对象
        window.YF_CHAT_CONFIG = {
            apiUrl: 'https://chat.22668.xyz',
            apiToken: 'qQiV0ZZJeL9A7rAgAt1BA89vNyVPAGVb',
            position: 'bottom-right',
            margin: 100,
            strings: {
                title: 'AI智能客服',
                placeholder: '输入您的问题...',
                welcomeMessage: '您好！我是Dylan，您的专属环保电器顾问。有什么可以帮助您的吗？',
                assistantName: 'Dylan',
                error: '抱歉，出现了一些问题，请稍后再试。'
            },
            settings: {
                sessionTimeout: 30 * 60 * 1000,
                saveHistory: true
            }
        };

        // 控制台日志捕获
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function appendToConsole(message, type = 'log') {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : 'ℹ️';
            output.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            appendToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            appendToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            appendToConsole(args.join(' '), 'warn');
        };

        // 测试函数
        function testConfiguration() {
            console.log('=== 配置测试开始 ===');
            
            if (typeof window.YF_CHAT_CONFIG === 'undefined') {
                console.error('配置对象未找到');
                return;
            }
            
            const config = window.YF_CHAT_CONFIG;
            console.log('✅ 配置对象已加载');
            console.log('API URL:', config.apiUrl ? '已配置' : '❌ 未配置');
            console.log('API Token:', config.apiToken && config.apiToken.length > 10 ? '已配置' : '❌ 未配置或过短');
            console.log('界面配置:', config.strings ? '已配置' : '❌ 未配置');
            console.log('功能配置:', config.settings ? '已配置' : '❌ 未配置');
        }
        
        function testAPI() {
            console.log('=== API连接测试开始 ===');
            
            if (!window.YF_CHAT_CONFIG || !window.YF_CHAT_CONFIG.apiUrl) {
                console.error('API URL未配置');
                return;
            }
            
            const apiUrl = window.YF_CHAT_CONFIG.apiUrl;
            console.log('正在测试连接:', apiUrl);
            
            // 测试基本连接
            fetch(apiUrl + '/api/health', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                console.log('API响应状态:', response.status);
                if (response.ok) {
                    console.log('✅ API连接成功');
                } else {
                    console.warn('⚠️ API响应异常，状态码:', response.status);
                }
            })
            .catch(error => {
                console.error('❌ API连接失败:', error.message);
            });
        }
        
        function testChatWidget() {
            console.log('=== 聊天组件测试开始 ===');
            
            const button = document.getElementById('yf-chat-button');
            const window_elem = document.getElementById('yf-chat-window');
            
            console.log('聊天按钮:', button ? '✅ 已创建' : '❌ 未找到');
            console.log('聊天窗口:', window_elem ? '✅ 已创建' : '❌ 未找到');
            
            if (button) {
                console.log('按钮位置:', button.style.position || '默认');
                console.log('按钮可见性:', button.style.display !== 'none' ? '可见' : '隐藏');
            }
        }
        
        function clearConsole() {
            document.getElementById('console-output').textContent = '';
        }
        
        // 页面加载完成后的自动检查
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                // 检查配置
                const configStatus = document.getElementById('config-status');
                if (typeof window.YF_CHAT_CONFIG !== 'undefined') {
                    configStatus.textContent = '✅ 配置已加载';
                    configStatus.className = 'status success';
                } else {
                    configStatus.textContent = '❌ 配置未找到';
                    configStatus.className = 'status error';
                }
                
                // 检查文件
                const filesStatus = document.getElementById('files-status');
                const cssLoaded = document.querySelector('link[href*="yf-chat-shopify.css"]');
                const jsLoaded = document.querySelector('script[src*="yf-chat-shopify.js"]');
                
                if (cssLoaded && jsLoaded) {
                    filesStatus.textContent = '✅ CSS和JS文件已加载';
                    filesStatus.className = 'status success';
                } else {
                    filesStatus.textContent = '⚠️ 部分文件可能未加载';
                    filesStatus.className = 'status warning';
                }
                
                // 检查初始化
                setTimeout(() => {
                    const initStatus = document.getElementById('init-status');
                    const chatButton = document.getElementById('yf-chat-button');
                    
                    if (chatButton) {
                        initStatus.textContent = '✅ 聊天组件初始化成功';
                        initStatus.className = 'status success';
                    } else {
                        initStatus.textContent = '❌ 聊天组件初始化失败';
                        initStatus.className = 'status error';
                    }
                }, 2000);
                
            }, 1000);
        });
    </script>

    <!-- 加载CSS文件 -->
    <link rel="stylesheet" href="assets/yf-chat-shopify.css">
    
    <!-- 加载JS文件 -->
    <script src="assets/yf-chat-shopify.js"></script>
</body>
</html>
