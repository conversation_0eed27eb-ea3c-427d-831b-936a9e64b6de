# YF AI Chat Now - 更新日志

## 版本 1.7.4 (2025-07-02)

### 🔧 关键修复
- **用户消息显示问题**：修复WordPress端用户消息不显示的CSS样式冲突问题
  - 为用户消息样式添加 `!important` 规则，确保正确显示
  - 修复 `.yf-chat-message.user` 和 `.yf-chat-message.user .yf-chat-message-content` 样式
  - 解决与主题CSS冲突导致的消息不可见问题

- **CSV导出功能修复**：修复后端清空聊天记录时的CSV导出错误
  - 修正 `backend/services/database.py` 中的字段名错误（`msg.message` → `msg.content`）
  - 确保清空聊天记录时能正确导出CSV文件到 `backend/history/` 文件夹
  - CSV文件命名格式：`年月日时分秒_会话ID_user_cleared.csv`

### 🚀 功能完善
- **清空历史记录功能**：完整的清空流程现已正常工作
  - 前端UI正确清空聊天窗口
  - 后端自动导出CSV备份文件
  - 数据库中的会话记录完全清除
  - 用户操作后显示确认提示

### 🎯 同步更新
- **Shopify版本同步**：将修复同步到Shopify版本
  - 升级Shopify版本号至 2.1.1
  - 同步应用用户消息显示的CSS修复
  - 保持WordPress和Shopify版本功能一致性

---

## 版本 1.6.2 (2025-06-23)

### 🔧 关键修复
- **DOM排序问题**：彻底解决消息显示顺序错误的问题
  - 新增 `insertMessageAtCorrectPosition()` 方法，按消息ID正确插入DOM
  - 修改所有消息添加方法，支持按ID排序插入而不是简单append
  - 为消息元素添加 `data-message-id` 属性，便于排序定位
  - 确保历史记录恢复和新消息都按正确顺序显示

### 🚀 技术改进
- **消息插入逻辑**：完全重构消息DOM插入机制
  - `addMessageToUI()` 方法支持消息ID参数
  - `addAdminMessageToUI()` 方法支持消息ID参数
  - 智能位置插入：根据消息ID找到正确位置插入
  - 向后兼容：没有ID的消息（如欢迎消息）仍使用append

### 🎯 解决的问题
- **第一次对话顺序错误**：修复刷新页面后第一次对话显示顺序颠倒的问题
- **轮询消息排序**：确保轮询获取的消息按ID顺序正确显示
- **历史记录混合**：解决历史记录与新消息混合时的排序问题

### 📋 工作原理
- **数据排序**：后端和前端都按消息ID排序 ✅
- **DOM排序**：现在DOM插入也按消息ID排序 ✅
- **完整流程**：数据→排序→正确位置插入DOM→正确显示 ✅

---

## 版本 1.6.1 (2025-06-23)

### 🔧 重要修复
- **初始化逻辑优化**：修复清空浏览器缓存后历史记录丢失的问题
  - 新增后端session存在性检查API (`/api/session/{id}/exists`)
  - 前端初始化时优先检查后端数据库是否有历史记录
  - 如果有后端历史记录，自动从服务器恢复完整聊天记录
  - 确保用户体验的连续性，避免历史记录意外丢失

### 🚀 消息排序优化
- **完全基于消息ID排序**：彻底解决消息顺序问题
  - 所有消息严格按数据库ID排序（ID越小越早显示）
  - 避免时区相关的排序问题
  - 确保"你一句我一句"的正确对话顺序
  - 轮询获取消息时自动按ID排序处理

### 🛠️ 后端日志优化
- **日志重复问题修复**：解决一条消息显示3次的问题
  - 优化终端输出逻辑，使用单一输出方式
  - 保持日志清洁，提高可读性
  - 减少终端噪音，便于监控和调试

### 🔄 前端架构改进
- **异步初始化**：改进WordPress前端的初始化流程
  - 支持异步session检查和历史记录加载
  - 增强错误处理和回退机制
  - 提高初始化的可靠性和用户体验

### 🎯 去重机制完善
- **基于ID的去重**：完善消息去重逻辑
  - 完全基于数据库消息ID进行去重
  - 避免重复显示相同消息
  - 提高消息处理的准确性

---

## 版本 1.6.0 (2025-06-20)

### 🔧 重要修复
- **消息顺序问题**：修复了WordPress前端和客服端消息显示顺序错误的问题
  - 移除数据库查询中的 `reversed()` 操作
  - 确保消息按正确的时间顺序显示（用户→AI→用户→AI）
  - 解决了"你一句我一句"对话顺序被颠倒的问题

### 🚀 前端优化
- **消息处理改进**：优化WordPress前端的消息处理逻辑
  - 修复控制台"消息缺少数据库ID"警告
  - 改进用户消息发送流程，避免重复显示
  - 增强错误处理机制，允许临时消息没有数据库ID
  - 添加立即轮询机制，确保消息快速显示

### 🛠️ 技术改进
- **去重机制优化**：完善消息去重逻辑
  - 支持带ID和不带ID的消息处理
  - 改进用户体验，减少不必要的警告
  - 确保所有类型消息都能正确显示

### 🐛 问题修复
- 修复用户发送消息时的控制台警告
- 修复错误处理时的消息显示问题
- 优化消息显示时机和顺序
- 改进轮询机制的可靠性

---

## 版本 1.5.0 (2025-06-18)

### 🚀 重大架构升级 - API防护系统重构
- **防护功能后端化**：将所有API防护功能从WordPress移至后端统一处理
  - 减少WordPress负担，提升性能
  - 统一管理所有客户端的防护策略
  - 支持实时热重载，无需重启服务

### 🛡️ 全新防护系统特性
- **智能防护规则**：
  - 频率限制：支持每分钟/每小时请求限制
  - IP黑名单：自动和手动封禁管理
  - 内容过滤：垃圾内容、重复字符、恶意URL检测
  - LLM滥用防护：防止提示注入、越狱尝试、角色扮演攻击

### 📁 配置文件系统
- **实时配置管理**：
  - 配置文件：`backend/config/protection_rules.json`
  - 支持热重载：修改配置无需重启后端
  - 可视化编辑器：`backend/config/edit_protection_rules.py`
  - 详细的防护规则和响应消息配置

### 🔧 后端API增强
- **新增管理接口**：
  - `GET /api/admin/protection/stats` - 获取防护统计
  - `GET /api/admin/protection/config` - 获取防护配置
  - 集成到聊天API的实时防护检查

### 📊 WordPress插件优化
- **界面简化**：移除复杂的防护设置界面
- **性能提升**：移除前端防护检查逻辑
- **用户体验**：清晰的防护功能说明和配置指引

### 🎯 预设防护规则
- **常见垃圾内容**：重复字符、特殊字符滥用、垃圾词汇
- **恶意行为**：提示注入、系统指令绕过、敏感信息获取
- **网络攻击**：代码执行尝试、越狱攻击、角色扮演提权

---

## 版本 1.4.0 (2025-06-18)

### 🎉 重大修复 - 客服消息接收问题
- **完全解决WordPress端无法接收客服消息的问题**
  - 修复since参数时间过滤逻辑错误
  - 采用保守的时间偏移策略（-1秒而非+100ms）
  - 确保管理员消息100%可靠接收

### 🔧 时间处理优化
- **改进lastMessageTime机制**：
  - 历史记录恢复时使用更保守的时间（减1分钟）
  - 空响应时设置为当前时间减1分钟
  - 消息时间更新使用减1秒策略

### 🚀 性能提升
- **智能消息过滤**：恢复since参数，减少不必要的数据传输
- **可靠的去重机制**：基于数据库唯一ID的消息去重
- **避免消息重复**：解决AI回复重复显示的问题

### 🐛 问题修复
- 修复页面刷新后管理员消息丢失问题
- 修复时间过滤导致的消息遗漏问题
- 修复轮询机制的时间同步问题

### 📈 稳定性改进
- 增强错误处理和日志记录
- 改进网络请求的容错机制
- 优化轮询频率和超时处理

---

## 版本 1.3.6 (2025-06-18)

### 🔧 关键修复
- **WordPress消息显示问题**：修复WordPress端无法显示客服端回复消息的关键问题
  - 移除了过于严格的`lastMessageTime`条件判断
  - 确保管理员消息在任何情况下都能被正确处理和显示
  - 优化轮询逻辑，提高消息接收的可靠性

### 🐛 调试增强
- **详细日志输出**：添加详细的调试日志，便于问题诊断
  - 增强轮询过程的日志记录
  - 添加消息处理各阶段的状态输出
  - 改进错误处理和异常报告

### 🛠️ 开发工具
- **轮询测试工具**：新增专门的WordPress轮询测试工具
  - 可独立测试API连接和消息轮询
  - 实时查看消息处理过程
  - 便于开发和调试

---

## 版本 1.3.5 (2025-06-18)

### 🔧 重要修复
- **WordPress端消息显示问题**：修复WordPress端聊天窗口无法显示客服端介入后回复消息的问题
  - 优化消息去重逻辑，只基于唯一数据库ID进行去重
  - 移除不必要的内容重复检查，提高消息显示可靠性
  - 修复会话清除时messageIds集合未正确清理的问题

### 🚀 性能优化
- **消息处理简化**：移除冗余的消息重复检查方法
  - 删除`isMessageInUI`和`removeDuplicateMessages`方法
  - 简化消息处理逻辑，提高性能
  - 确保管理员消息能及时准确显示

### 🛠️ 技术改进
- **去重机制优化**：完全依赖数据库唯一ID进行消息去重
- **错误处理增强**：改进会话清除和错误恢复机制
- **代码清理**：移除不再需要的冗余代码

---

## 版本 1.3.0 (2025-06-18)

### 🔧 重要修复
- **消息去重机制**：完全重构消息去重系统，解决消息重复显示问题
  - 使用数据库ID作为主要去重标识
  - 备用标识：`{sender}_{content}_{created_at}` 组合
  - 会话切换时自动清除消息ID集合
- **WordPress前端消息接收**：修复管理员接管后前端收不到回复的问题
  - 优化消息轮询逻辑
  - 增强消息ID去重检测
  - 改进消息显示时机

### 📊 日志系统增强
- **客服端日志**：完善客服端日志记录系统
  - 详细记录应用启动、认证、WebSocket连接等关键操作
  - 分级日志（INFO、DEBUG、WARNING、ERROR）
  - 自动生成 `client.log` 文件
- **调试信息**：增加详细的调试日志，便于问题诊断
  - 消息发送和接收日志
  - 会话管理操作日志
  - API请求和响应状态记录

### 🚀 性能优化
- **消息流程优化**：简化消息发送流程，避免重复处理
  - 客服端发送 → 后端保存 → WebSocket推送 → 显示
  - 移除HTTP响应中的重复消息显示逻辑
- **WebSocket通信**：改进WebSocket消息处理机制
  - 更稳定的连接管理
  - 更准确的消息推送

### 🔒 数据一致性
- **消息ID管理**：确保所有消息都有唯一的数据库ID
  - 后端API返回完整消息数据
  - 前端使用统一的ID标识系统
- **状态同步**：改进客服端和WordPress前端的状态同步

### 🛠️ 技术改进
- **错误处理**：增强错误处理和异常记录
- **代码优化**：重构消息处理相关代码，提高可维护性
- **兼容性**：保持向后兼容，不影响现有功能

---

## 版本 1.2.6 (2025-06-17)

### 🕐 时区配置系统
- **WordPress后台时区设置**：在设置页面添加时区配置选项
  - 支持9种常用时区：洛杉矶、纽约、芝加哥、丹佛、上海、东京、伦敦、巴黎、UTC
  - 可自定义时区显示名称
  - 自动同步WordPress系统时区
- **统一时间显示**：所有时间显示现在使用配置的时区
  - 安全日志时间显示
  - 封禁记录时间
  - 请求记录时间戳

### 🔧 技术改进
- **动态时区处理**：优化时区处理函数，支持运行时配置更改
- **错误处理增强**：改进时区配置的错误处理和备用方案
- **兼容性提升**：增强不同PHP版本的时区处理兼容性

### 🛠️ 配置管理
- **灵活配置**：时区设置更加人性化和用户友好
- **实时生效**：时区更改立即生效，无需重启
- **配置验证**：添加时区配置验证和错误恢复机制

---

## 版本 1.2.0 (2025-06-17)

### 🎉 新功能
- **实时消息接收**：前端用户现在可以实时接收管理员发送的消息
- **消息轮询机制**：每5秒自动检查新的管理员消息
- **管理员消息样式**：专门的管理员消息UI，橙色主题突出显示
- **消息通知**：聊天窗口关闭时显示新消息通知动画

### 🔧 修复
- **消息换行显示**：修复了管理员消息不换行的显示问题
- **消息格式化**：支持多行文本的正确格式化和显示
- **API通信**：修复了前端用户无法接收管理员消息的问题
- **数据转换**：修复了后端Pydantic模型转换问题

### 🎨 界面改进
- **管理员消息标识**：简化为"👨‍💼 人工客服"，更加简洁
- **消息样式**：添加了专门的CSS样式支持换行和格式化
- **通知动画**：新消息通知使用脉冲动画效果
- **响应式设计**：优化了不同屏幕尺寸的显示效果

### 🚀 性能优化
- **轮询优化**：智能的消息轮询，只获取新消息
- **缓存机制**：改进了消息历史记录的本地存储
- **网络优化**：减少了不必要的API请求

### 🔒 安全增强
- **API权限**：新增了专门的前端消息获取API
- **时间过滤**：支持基于时间的消息过滤
- **错误处理**：改进了网络错误的处理机制

### 📱 兼容性
- **浏览器支持**：兼容所有现代浏览器
- **移动端优化**：改进了移动设备上的显示效果
- **WordPress兼容**：兼容WordPress 5.0+

### 🛠️ 技术改进
- **API端点**：新增 `/api/session/{session_id}/messages` 端点
- **数据格式**：统一了消息数据格式
- **错误日志**：改进了错误日志记录

---

## 版本 1.1.0 (之前版本)

### 功能特性
- AI聊天小部件基础功能
- 管理员后台设置界面
- 安全防护机制
- 个性化配置选项
- 会话管理功能

---

## 升级说明

### 从 1.2.x 升级到 1.3.0

1. **备份数据**：升级前请备份您的WordPress网站和数据库
2. **更新文件**：替换插件文件夹中的所有文件
3. **更新后端**：确保后端服务器同时更新到最新版本
4. **清除缓存**：清除浏览器缓存和WordPress缓存
5. **测试功能**：重点测试消息发送和接收功能

### 配置要求
- **后端API**：必须同时更新后端服务器到最新版本
- **客服端**：建议同时更新客服端应用程序
- **API Token**：确认API Token配置正确
- **网络连接**：前端需要能够访问后端API

### 重要改进
- **消息去重**：本次更新完全解决了消息重复显示问题
- **日志系统**：客服端现在会生成详细的运行日志
- **通信稳定性**：大幅改进了WordPress前端和客服端的消息通信

### 注意事项
- 本次更新包含了重要的消息处理机制改进
- 建议在升级后重点测试管理员消息发送和接收功能
- 如遇到问题，请检查客服端的 `client.log` 文件
- 确保所有组件（WordPress插件、后端、客服端）同时更新

---

## 技术支持

如果您在使用过程中遇到任何问题，请：

1. 检查WordPress错误日志
2. 查看浏览器控制台错误
3. 确认API配置是否正确
4. 联系技术支持团队

---

**开发团队**：jiang
**更新日期**：2025年6月23日
**插件主页**：https://chat.22668.xyz
