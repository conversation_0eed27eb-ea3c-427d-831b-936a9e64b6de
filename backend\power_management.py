"""
Windows电源管理模块
防止系统进入效能模式，保持后端高性能运行
"""

import os
import sys
import time
import threading
import logging
from typing import Optional

# Windows API相关导入
if sys.platform == "win32":
    try:
        import ctypes
        from ctypes import wintypes
        WINDOWS_AVAILABLE = True
    except ImportError:
        WINDOWS_AVAILABLE = False
else:
    WINDOWS_AVAILABLE = False

logger = logging.getLogger(__name__)

class WindowsPowerManager:
    """Windows电源管理器"""
    
    def __init__(self):
        self.keep_alive_thread: Optional[threading.Thread] = None
        self.running = False
        
        if not WINDOWS_AVAILABLE:
            logger.warning("Windows API不可用，电源管理功能将被禁用")
            return
            
        # Windows电源管理常量
        self.ES_CONTINUOUS = 0x80000000
        self.ES_SYSTEM_REQUIRED = 0x00000001
        self.ES_DISPLAY_REQUIRED = 0x00000002
        self.ES_AWAYMODE_REQUIRED = 0x00000040
        
        # 获取kernel32和user32
        try:
            self.kernel32 = ctypes.windll.kernel32
            self.user32 = ctypes.windll.user32
            logger.info("✅ Windows电源管理API初始化成功")
        except Exception as e:
            logger.error(f"❌ Windows电源管理API初始化失败: {e}")
            self.kernel32 = None
            self.user32 = None
    
    def prevent_sleep(self):
        """防止系统进入睡眠/效能模式"""
        if not WINDOWS_AVAILABLE or not self.kernel32:
            return False
            
        try:
            # 设置系统执行状态，防止睡眠和显示器关闭
            result = self.kernel32.SetThreadExecutionState(
                self.ES_CONTINUOUS | 
                self.ES_SYSTEM_REQUIRED | 
                self.ES_DISPLAY_REQUIRED |
                self.ES_AWAYMODE_REQUIRED
            )
            
            if result:
                logger.info("🔋 已设置系统保持唤醒状态")
                return True
            else:
                logger.warning("⚠️ 设置系统唤醒状态失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 防止睡眠设置失败: {e}")
            return False
    
    def allow_sleep(self):
        """允许系统进入睡眠模式"""
        if not WINDOWS_AVAILABLE or not self.kernel32:
            return False
            
        try:
            # 恢复默认电源管理
            result = self.kernel32.SetThreadExecutionState(self.ES_CONTINUOUS)
            
            if result:
                logger.info("🔋 已恢复系统默认电源管理")
                return True
            else:
                logger.warning("⚠️ 恢复电源管理失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 恢复电源管理失败: {e}")
            return False
    
    def set_high_performance_mode(self):
        """设置高性能电源计划"""
        if not WINDOWS_AVAILABLE:
            return False
            
        try:
            # 使用powercfg命令设置高性能模式
            # 高性能GUID: 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c
            cmd = 'powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c'
            result = os.system(cmd)
            
            if result == 0:
                logger.info("⚡ 已切换到高性能电源计划")
                return True
            else:
                logger.warning("⚠️ 切换高性能模式失败，可能需要管理员权限")
                return False
                
        except Exception as e:
            logger.error(f"❌ 设置高性能模式失败: {e}")
            return False
    
    def keep_system_active(self):
        """保持系统活跃的后台线程"""
        logger.info("🔄 启动系统活跃保持线程")

        while self.running:
            try:
                # 每30秒重新设置一次执行状态
                success = self.prevent_sleep()
                if success:
                    logger.debug("🔋 系统保持唤醒状态已刷新")
                else:
                    logger.warning("⚠️ 刷新系统唤醒状态失败")

                time.sleep(30)  # 30秒间隔

            except Exception as e:
                logger.error(f"❌ 保持系统活跃时出错: {e}")
                time.sleep(60)  # 出错时等待更长时间
    
    def start_keep_alive(self):
        """启动保持活跃服务"""
        if self.running:
            logger.warning("⚠️ 保持活跃服务已在运行")
            return
            
        logger.info("🚀 启动Windows电源管理保护")
        
        # 立即设置防睡眠
        self.prevent_sleep()
        
        # 尝试设置高性能模式
        self.set_high_performance_mode()
        
        # 启动后台保持线程
        self.running = True
        self.keep_alive_thread = threading.Thread(
            target=self.keep_system_active,
            daemon=True,
            name="PowerKeepAlive"
        )
        self.keep_alive_thread.start()
        
        logger.info("✅ Windows电源管理保护已启动")
    
    def stop_keep_alive(self):
        """停止保持活跃服务"""
        if not self.running:
            return
            
        logger.info("🛑 停止Windows电源管理保护")
        
        self.running = False
        
        # 等待线程结束
        if self.keep_alive_thread and self.keep_alive_thread.is_alive():
            self.keep_alive_thread.join(timeout=5)
        
        # 恢复默认电源管理
        self.allow_sleep()
        
        logger.info("✅ Windows电源管理保护已停止")
    
    def get_current_power_plan(self):
        """获取当前电源计划"""
        if not WINDOWS_AVAILABLE:
            return "未知（非Windows系统）"
            
        try:
            import subprocess
            result = subprocess.run(
                ['powercfg', '/getactivescheme'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                output = result.stdout.strip()
                if "高性能" in output or "High performance" in output:
                    return "高性能"
                elif "平衡" in output or "Balanced" in output:
                    return "平衡"
                elif "节能" in output or "Power saver" in output:
                    return "节能"
                else:
                    return output.split('\n')[0] if output else "未知"
            else:
                return "获取失败"
                
        except Exception as e:
            logger.error(f"❌ 获取电源计划失败: {e}")
            return "获取失败"

# 全局电源管理器实例
power_manager = WindowsPowerManager()

def start_power_protection():
    """启动电源保护"""
    if sys.platform == "win32":
        power_manager.start_keep_alive()
        return True
    else:
        logger.info("ℹ️ 非Windows系统，跳过电源管理")
        return False

def stop_power_protection():
    """停止电源保护"""
    if sys.platform == "win32":
        power_manager.stop_keep_alive()
        return True
    else:
        return False

def get_power_status():
    """获取电源状态信息"""
    return {
        "platform": sys.platform,
        "power_protection_active": power_manager.running,
        "current_power_plan": power_manager.get_current_power_plan(),
        "windows_api_available": WINDOWS_AVAILABLE
    }
