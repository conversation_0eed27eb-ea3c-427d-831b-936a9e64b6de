<?php
/**
 * Plugin Name: YF AI Chat Now
 * Plugin URI: https://chat.22668.xyz
 * Description: AI-powered customer service chat widget for WordPress
 * Version: 1.7.5
 * Author: jiang
 * Text Domain: yf-ai-chat-now
 * Domain Path: /languages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('YF_AI_CHAT_VERSION', '1.7.5');
define('YF_AI_CHAT_PLUGIN_URL', plugin_dir_url(__FILE__));
define('YF_AI_CHAT_PLUGIN_PATH', plugin_dir_path(__FILE__));

class YF_AI_Chat_Now {

    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'admin_menu'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_footer', array($this, 'render_chat_widget'));
        add_action('wp_ajax_yf_chat_message', array($this, 'handle_chat_message'));
        add_action('wp_ajax_nopriv_yf_chat_message', array($this, 'handle_chat_message'));

        // Auto increment version on plugin update
        add_action('upgrader_process_complete', array($this, 'auto_increment_version'), 10, 2);

        // 修复数据库中的转义字符问题
        add_action('admin_init', array($this, 'fix_escaped_strings'));

        // 设置时区为洛杉矶时间
        $this->setup_timezone();
    }

    private function setup_timezone() {
        // 从插件设置获取时区配置
        $plugin_timezone = get_option('yf_ai_chat_timezone', 'America/Los_Angeles');

        // 设置WordPress时区
        if (get_option('timezone_string') !== $plugin_timezone) {
            update_option('timezone_string', $plugin_timezone);
        }
    }

    private function format_timestamp($timestamp, $format = 'Y-m-d H:i:s') {
        // 将时间戳转换为配置的时区显示
        $plugin_timezone = get_option('yf_ai_chat_timezone', 'America/Los_Angeles');
        try {
            $timezone = new DateTimeZone($plugin_timezone);
            $date = new DateTime('@' . $timestamp);
            $date->setTimezone($timezone);
            return $date->format($format);
        } catch (Exception $e) {
            // 如果时区无效，使用默认时区
            $timezone = new DateTimeZone('America/Los_Angeles');
            $date = new DateTime('@' . $timestamp);
            $date->setTimezone($timezone);
            return $date->format($format);
        }
    }

    private function get_current_la_time() {
        // 获取当前配置时区的时间
        $plugin_timezone = get_option('yf_ai_chat_timezone', 'America/Los_Angeles');
        try {
            $timezone = new DateTimeZone($plugin_timezone);
            $date = new DateTime('now', $timezone);
            return $date->getTimestamp();
        } catch (Exception $e) {
            // 如果时区无效，使用默认时区
            $timezone = new DateTimeZone('America/Los_Angeles');
            $date = new DateTime('now', $timezone);
            return $date->getTimestamp();
        }
    }
    
    public function init() {
        load_plugin_textdomain('yf-ai-chat-now', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }

    public function fix_escaped_strings() {
        // 只在管理员页面运行一次修复
        if (!is_admin() || get_option('yf_ai_chat_strings_fixed', false)) {
            return;
        }

        // 修复默认错误回应中的转义字符
        $default_response = get_option('yf_ai_chat_default_response', '');
        if (!empty($default_response) && strpos($default_response, '\\') !== false) {
            // 移除多余的反斜杠
            $fixed_response = str_replace(array('\\\\\\', '\\\\', '\\\'', '\\"'), array('', '', "'", '"'), $default_response);
            // 如果还是包含转义字符，使用默认文本
            if (strpos($fixed_response, '\\') !== false) {
                $fixed_response = 'I apologize, but I am experiencing technical difficulties. Please try again later.';
            }
            update_option('yf_ai_chat_default_response', $fixed_response);
        }

        // 修复欢迎消息中的转义字符
        $welcome_message = get_option('yf_ai_chat_welcome_message', '');
        if (!empty($welcome_message) && strpos($welcome_message, '\\') !== false) {
            $fixed_welcome = str_replace(array('\\\\\\', '\\\\', '\\\'', '\\"'), array('', '', "'", '"'), $welcome_message);
            if (strpos($fixed_welcome, '\\') !== false) {
                $fixed_welcome = 'Hello! How can I help you today?';
            }
            update_option('yf_ai_chat_welcome_message', $fixed_welcome);
        }

        // 标记为已修复，避免重复执行
        update_option('yf_ai_chat_strings_fixed', true);
    }
    
    public function admin_menu() {
        add_options_page(
            'YF AI Chat 设置',
            'YF AI Chat',
            'manage_options',
            'yf-ai-chat-settings',
            array($this, 'admin_page')
        );

        add_management_page(
            'YF AI Chat 安全',
            'YF AI Chat 安全',
            'manage_options',
            'yf-ai-chat-security',
            array($this, 'security_page')
        );
    }
    
    public function admin_page() {
        if (isset($_POST['submit'])) {
            update_option('yf_ai_chat_api_token', sanitize_text_field($_POST['api_token']));
            update_option('yf_ai_chat_api_url', esc_url_raw($_POST['api_url']));
            update_option('yf_ai_chat_position', sanitize_text_field($_POST['position']));
            update_option('yf_ai_chat_margin', intval($_POST['margin']));
            update_option('yf_ai_chat_enabled', isset($_POST['enabled']) ? 1 : 0);

            // 新增的个性化设置
            update_option('yf_ai_chat_assistant_name', sanitize_text_field($_POST['assistant_name']));
            update_option('yf_ai_chat_welcome_message', sanitize_textarea_field($_POST['welcome_message']));
            update_option('yf_ai_chat_default_response', sanitize_textarea_field($_POST['default_response']));
            update_option('yf_ai_chat_chat_title', sanitize_text_field($_POST['chat_title']));
            update_option('yf_ai_chat_placeholder_text', sanitize_text_field($_POST['placeholder_text']));

            // 会话管理设置
            update_option('yf_ai_chat_session_timeout', intval($_POST['session_timeout']));
            update_option('yf_ai_chat_save_history', isset($_POST['save_history']) ? 1 : 0);

            // 防护设置已移至后端处理，此处保留配置兼容性
            // update_option('yf_ai_chat_rate_limit_enabled', isset($_POST['rate_limit_enabled']) ? 1 : 0);
            // update_option('yf_ai_chat_max_requests_per_minute', intval($_POST['max_requests_per_minute']));
            // update_option('yf_ai_chat_max_message_length', intval($_POST['max_message_length']));
            // update_option('yf_ai_chat_auto_ban_enabled', isset($_POST['auto_ban_enabled']) ? 1 : 0);
            // update_option('yf_ai_chat_ban_threshold', intval($_POST['ban_threshold']));

            // 时区设置
            update_option('yf_ai_chat_timezone', sanitize_text_field($_POST['timezone']));
            update_option('yf_ai_chat_timezone_name', sanitize_text_field($_POST['timezone_name']));

            echo '<div class="notice notice-success"><p>设置已保存！</p></div>';
        }

        // 处理修复转义字符的请求
        if (isset($_POST['fix_strings'])) {
            // 重置修复标记，强制重新修复
            update_option('yf_ai_chat_strings_fixed', false);
            $this->fix_escaped_strings();
            echo '<div class="notice notice-success"><p>转义字符已修复！页面将自动刷新。</p></div>';
            echo '<script>setTimeout(function(){ location.reload(); }, 2000);</script>';
        }
        
        $api_token = get_option('yf_ai_chat_api_token', '');
        $api_url = get_option('yf_ai_chat_api_url', 'https://chat.22668.xyz');
        $position = get_option('yf_ai_chat_position', 'bottom-right');
        $margin = get_option('yf_ai_chat_margin', 100);
        $enabled = get_option('yf_ai_chat_enabled', 1);

        // 个性化设置
        $assistant_name = get_option('yf_ai_chat_assistant_name', 'Dylan');
        $welcome_message = get_option('yf_ai_chat_welcome_message', 'Hello! How can I help you today?');
        $default_response = get_option('yf_ai_chat_default_response', 'I apologize, but I am experiencing technical difficulties. Please try again later.');
        $chat_title = get_option('yf_ai_chat_chat_title', 'Customer Support');
        $placeholder_text = get_option('yf_ai_chat_placeholder_text', 'Type your message...');

        // 会话管理设置
        $session_timeout = get_option('yf_ai_chat_session_timeout', 30);
        $save_history = get_option('yf_ai_chat_save_history', 1);

        // 时区设置
        $timezone = get_option('yf_ai_chat_timezone', 'America/Los_Angeles');
        $timezone_name = get_option('yf_ai_chat_timezone_name', '洛杉矶时间');
        ?>
        <div class="wrap">
            <h1>YF AI Chat 设置</h1>
            <form method="post" action="">
                <table class="form-table">
                    <tr>
                        <th scope="row">启用聊天功能</th>
                        <td>
                            <input type="checkbox" name="enabled" value="1" <?php checked($enabled, 1); ?> />
                            <label>启用 AI 聊天小部件</label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">API Token</th>
                        <td>
                            <input type="text" name="api_token" value="<?php echo esc_attr($api_token); ?>" class="regular-text" />
                            <p class="description">输入您的 API 访问令牌</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">API 地址</th>
                        <td>
                            <input type="url" name="api_url" value="<?php echo esc_attr($api_url); ?>" class="regular-text" />
                            <p class="description">AI 聊天服务器地址</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">悬浮按钮位置</th>
                        <td>
                            <select name="position">
                                <option value="top-left" <?php selected($position, 'top-left'); ?>>左上角</option>
                                <option value="top-right" <?php selected($position, 'top-right'); ?>>右上角</option>
                                <option value="bottom-left" <?php selected($position, 'bottom-left'); ?>>左下角</option>
                                <option value="bottom-right" <?php selected($position, 'bottom-right'); ?>>右下角</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">边距 (px)</th>
                        <td>
                            <input type="number" name="margin" value="<?php echo esc_attr($margin); ?>" min="10" max="500" />
                            <p class="description">悬浮按钮距离屏幕边缘的距离</p>
                        </td>
                    </tr>
                </table>

                <h2>个性化设置</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">助手名称</th>
                        <td>
                            <input type="text" name="assistant_name" value="<?php echo esc_attr($assistant_name); ?>" class="regular-text" />
                            <p class="description">AI助手的显示名称</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">聊天窗口标题</th>
                        <td>
                            <input type="text" name="chat_title" value="<?php echo esc_attr($chat_title); ?>" class="regular-text" />
                            <p class="description">聊天窗口顶部显示的标题</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">欢迎消息</th>
                        <td>
                            <textarea name="welcome_message" rows="3" class="large-text"><?php echo esc_textarea($welcome_message); ?></textarea>
                            <p class="description">用户打开聊天窗口时显示的第一条消息</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">输入框提示文字</th>
                        <td>
                            <input type="text" name="placeholder_text" value="<?php echo esc_attr($placeholder_text); ?>" class="regular-text" />
                            <p class="description">输入框中的占位符文字</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">默认错误回应</th>
                        <td>
                            <textarea name="default_response" rows="3" class="large-text"><?php echo esc_textarea($default_response); ?></textarea>
                            <p class="description">当AI无法正常回应时显示的默认消息</p>
                        </td>
                    </tr>
                </table>

                <h2>会话管理设置</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">保存聊天记录</th>
                        <td>
                            <input type="checkbox" name="save_history" value="1" <?php checked($save_history, 1); ?> />
                            <label>在用户浏览器中保存聊天记录</label>
                            <p class="description">启用后，用户刷新页面时聊天记录不会丢失</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">会话超时时间 (分钟)</th>
                        <td>
                            <input type="number" name="session_timeout" value="<?php echo esc_attr($session_timeout); ?>" min="5" max="1440" />
                            <p class="description">会话在多长时间无活动后自动过期（5-1440分钟）</p>
                        </td>
                    </tr>
                </table>

                <h2>时区设置</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">时区</th>
                        <td>
                            <select name="timezone">
                                <option value="America/Los_Angeles" <?php selected($timezone, 'America/Los_Angeles'); ?>>洛杉矶时间 (America/Los_Angeles)</option>
                                <option value="America/New_York" <?php selected($timezone, 'America/New_York'); ?>>纽约时间 (America/New_York)</option>
                                <option value="America/Chicago" <?php selected($timezone, 'America/Chicago'); ?>>芝加哥时间 (America/Chicago)</option>
                                <option value="America/Denver" <?php selected($timezone, 'America/Denver'); ?>>丹佛时间 (America/Denver)</option>
                                <option value="Asia/Shanghai" <?php selected($timezone, 'Asia/Shanghai'); ?>>上海时间 (Asia/Shanghai)</option>
                                <option value="Asia/Tokyo" <?php selected($timezone, 'Asia/Tokyo'); ?>>东京时间 (Asia/Tokyo)</option>
                                <option value="Europe/London" <?php selected($timezone, 'Europe/London'); ?>>伦敦时间 (Europe/London)</option>
                                <option value="Europe/Paris" <?php selected($timezone, 'Europe/Paris'); ?>>巴黎时间 (Europe/Paris)</option>
                                <option value="UTC" <?php selected($timezone, 'UTC'); ?>>UTC 协调世界时</option>
                            </select>
                            <p class="description">选择系统使用的时区，影响所有时间显示</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">时区显示名称</th>
                        <td>
                            <input type="text" name="timezone_name" value="<?php echo esc_attr($timezone_name); ?>" class="regular-text" />
                            <p class="description">时区的显示名称，用于界面显示</p>
                        </td>
                    </tr>
                </table>

                <h2>🛡️ API防护设置</h2>
                <div class="notice notice-info">
                    <p><strong>✨ 防护功能已升级！</strong></p>
                    <p>API防护功能已移至后端统一处理，提供更强大和实时的防护能力。</p>
                    <p><strong>新功能包括：</strong></p>
                    <ul>
                        <li>🚀 <strong>实时热重载</strong>：修改配置无需重启服务</li>
                        <li>🛡️ <strong>智能防护</strong>：频率限制、IP黑名单、内容过滤</li>
                        <li>🤖 <strong>LLM滥用防护</strong>：防止提示注入和越狱尝试</li>
                        <li>📊 <strong>详细统计</strong>：实时监控和日志记录</li>
                    </ul>
                    <p><strong>配置文件位置：</strong> <code>backend/config/protection_rules.json</code></p>
                    <p><strong>管理接口：</strong> 可通过后端API查看统计和配置信息</p>
                </div>

                <?php submit_button('保存设置'); ?>
            </form>

            <h2>故障排除</h2>
            <form method="post" action="">
                <table class="form-table">
                    <tr>
                        <th scope="row">修复转义字符</th>
                        <td>
                            <p class="description">如果您在默认回复或欢迎消息中看到多余的反斜杠（如 \\\\\\），请点击下面的按钮修复：</p>
                            <?php submit_button('修复转义字符问题', 'secondary', 'fix_strings', false); ?>
                        </td>
                    </tr>
                </table>
            </form>
            
            <h2>使用说明</h2>
            <p><strong>基础设置:</strong></p>
            <p>1. 输入您的 API Token 和服务器地址</p>
            <p>2. 选择聊天按钮的显示位置和边距</p>
            <p>3. 启用聊天功能</p>

            <p><strong>个性化设置:</strong></p>
            <p>4. 自定义助手名称和聊天窗口标题</p>
            <p>5. 设置欢迎消息和输入框提示文字</p>
            <p>6. 配置默认错误回应消息</p>
            <p>7. 访问您的网站前端查看聊天小部件</p>
            
            <h3>当前版本: <?php echo YF_AI_CHAT_VERSION; ?></h3>
        </div>
        <?php
    }

    public function security_page() {
        // 处理解封IP请求
        if (isset($_POST['unban_ip'])) {
            $ip_to_unban = sanitize_text_field($_POST['ip_to_unban']);
            $this->remove_ip_from_blacklist($ip_to_unban);
            echo '<div class="notice notice-success"><p>IP ' . $ip_to_unban . ' 已解封！</p></div>';
        }

        // 处理手动封禁IP请求
        if (isset($_POST['ban_ip'])) {
            $ip_to_ban = sanitize_text_field($_POST['ip_to_ban']);
            $ban_reason = sanitize_text_field($_POST['ban_reason']);
            $this->add_ip_to_blacklist($ip_to_ban, $ban_reason ?: 'Manual ban');
            echo '<div class="notice notice-success"><p>IP ' . $ip_to_ban . ' 已封禁！</p></div>';
        }

        // 处理清除日志请求
        if (isset($_POST['clear_logs'])) {
            update_option('yf_ai_chat_security_logs', array());
            echo '<div class="notice notice-success"><p>安全日志已清除！</p></div>';
        }

        $blacklist = get_option('yf_ai_chat_ip_blacklist', array());
        $security_logs = get_option('yf_ai_chat_security_logs', array());
        $ban_logs = get_option('yf_ai_chat_ban_logs', array());

        // 按时间倒序排列日志
        $security_logs = array_reverse($security_logs);
        $ban_logs = array_reverse($ban_logs);
        ?>
        <div class="wrap">
            <h1>🛡️ YF AI Chat 安全管理</h1>

            <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h2>📊 安全统计</h2>
                <p><strong>当前封禁IP数量：</strong> <?php echo count($blacklist); ?></p>
                <p><strong>安全事件记录：</strong> <?php echo count($security_logs); ?></p>
                <p><strong>封禁记录：</strong> <?php echo count($ban_logs); ?></p>
            </div>

            <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h2>🚫 IP黑名单管理</h2>

                <!-- 手动封禁IP -->
                <h3>手动封禁IP</h3>
                <form method="post" action="" style="margin-bottom: 20px;">
                    <table class="form-table">
                        <tr>
                            <th scope="row">IP地址</th>
                            <td><input type="text" name="ip_to_ban" placeholder="***********" required /></td>
                        </tr>
                        <tr>
                            <th scope="row">封禁原因</th>
                            <td><input type="text" name="ban_reason" placeholder="手动封禁" /></td>
                        </tr>
                    </table>
                    <?php submit_button('封禁IP', 'secondary', 'ban_ip', false); ?>
                </form>

                <!-- 当前黑名单 -->
                <h3>当前黑名单 (<?php echo count($blacklist); ?>)</h3>
                <?php if (!empty($blacklist)): ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th>IP地址</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($blacklist as $ip): ?>
                                <tr>
                                    <td><?php echo esc_html($ip); ?></td>
                                    <td>
                                        <form method="post" action="" style="display: inline;">
                                            <input type="hidden" name="ip_to_unban" value="<?php echo esc_attr($ip); ?>" />
                                            <?php submit_button('解封', 'small', 'unban_ip', false); ?>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p>暂无封禁的IP地址。</p>
                <?php endif; ?>
            </div>

            <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h2>📋 安全日志</h2>
                <form method="post" action="" style="margin-bottom: 20px;">
                    <?php submit_button('清除所有日志', 'secondary', 'clear_logs', false); ?>
                </form>

                <?php if (!empty($security_logs)): ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>IP地址</th>
                                <th>违规类型</th>
                                <th>消息内容</th>
                                <th>用户代理</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($security_logs, 0, 50) as $log): ?>
                                <tr>
                                    <td><?php echo $this->format_timestamp($log['timestamp']); ?></td>
                                    <td><?php echo esc_html($log['ip']); ?></td>
                                    <td>
                                        <?php
                                        $reason_map = array(
                                            'spam_content' => '垃圾内容',
                                            'rate_limit' => '频率超限',
                                            'message_too_long' => '消息过长'
                                        );
                                        echo $reason_map[$log['reason']] ?? $log['reason'];
                                        ?>
                                    </td>
                                    <td><?php echo esc_html(substr($log['message'], 0, 50)) . (strlen($log['message']) > 50 ? '...' : ''); ?></td>
                                    <td><?php echo esc_html(substr($log['user_agent'], 0, 50)) . (strlen($log['user_agent']) > 50 ? '...' : ''); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    <?php if (count($security_logs) > 50): ?>
                        <p><em>只显示最近50条记录</em></p>
                    <?php endif; ?>
                <?php else: ?>
                    <p>暂无安全事件记录。</p>
                <?php endif; ?>
            </div>

            <div style="background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h2>🔒 封禁记录</h2>
                <?php if (!empty($ban_logs)): ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th>封禁时间</th>
                                <th>IP地址</th>
                                <th>封禁原因</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($ban_logs, 0, 20) as $log): ?>
                                <tr>
                                    <td><?php echo $this->format_timestamp($log['timestamp']); ?></td>
                                    <td><?php echo esc_html($log['ip']); ?></td>
                                    <td><?php echo esc_html($log['reason']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    <?php if (count($ban_logs) > 20): ?>
                        <p><em>只显示最近20条记录</em></p>
                    <?php endif; ?>
                <?php else: ?>
                    <p>暂无封禁记录。</p>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }

    private function remove_ip_from_blacklist($ip) {
        $blacklist = get_option('yf_ai_chat_ip_blacklist', array());
        $blacklist = array_diff($blacklist, array($ip));
        update_option('yf_ai_chat_ip_blacklist', array_values($blacklist));
    }
    
    public function enqueue_scripts() {
        if (!get_option('yf_ai_chat_enabled', 1)) {
            return;
        }

        // 确保jQuery已加载
        wp_enqueue_script('jquery');

        wp_enqueue_script('yf-ai-chat-js', YF_AI_CHAT_PLUGIN_URL . 'assets/chat.js', array('jquery'), YF_AI_CHAT_VERSION, true);
        wp_enqueue_style('yf-ai-chat-css', YF_AI_CHAT_PLUGIN_URL . 'assets/chat.css', array(), YF_AI_CHAT_VERSION);
        
        wp_localize_script('yf-ai-chat-js', 'yfAiChat', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('yf_chat_nonce'),
            'apiUrl' => get_option('yf_ai_chat_api_url', 'https://chat.22668.xyz'),
            'apiToken' => get_option('yf_ai_chat_api_token', ''),
            'position' => get_option('yf_ai_chat_position', 'bottom-right'),
            'margin' => get_option('yf_ai_chat_margin', 100),
            'settings' => array(
                'assistantName' => get_option('yf_ai_chat_assistant_name', 'Dylan'),
                'welcomeMessage' => get_option('yf_ai_chat_welcome_message', 'Hello! How can I help you today?'),
                'defaultResponse' => get_option('yf_ai_chat_default_response', 'I apologize, but I am experiencing technical difficulties. Please try again later.'),
                'chatTitle' => get_option('yf_ai_chat_chat_title', 'Customer Support'),
                'placeholderText' => get_option('yf_ai_chat_placeholder_text', 'Type your message...'),
                'sessionTimeout' => get_option('yf_ai_chat_session_timeout', 30) * 60 * 1000, // 转换为毫秒
                'saveHistory' => get_option('yf_ai_chat_save_history', 1)
            ),
            'strings' => array(
                'title' => get_option('yf_ai_chat_chat_title', 'Customer Support'),
                'placeholder' => get_option('yf_ai_chat_placeholder_text', 'Type your message...'),
                'send' => __('Send', 'yf-ai-chat-now'),
                'connecting' => __('Connecting...', 'yf-ai-chat-now'),
                'error' => get_option('yf_ai_chat_default_response', 'Connection error. Please try again.'),
                'welcomeMessage' => get_option('yf_ai_chat_welcome_message', 'Hello! How can I help you today?'),
                'assistantName' => get_option('yf_ai_chat_assistant_name', 'Dylan')
            )
        ));
    }
    
    public function render_chat_widget() {
        if (!get_option('yf_ai_chat_enabled', 1)) {
            return;
        }
        
        include YF_AI_CHAT_PLUGIN_PATH . 'templates/chat-widget.php';
    }

    private function check_message_protection($message) {
        $user_ip = $this->get_user_ip();
        $current_time = $this->get_current_la_time();

        // 1. 检查IP黑名单
        if ($this->is_ip_blacklisted($user_ip)) {
            return array(
                'allowed' => false,
                'message' => 'Your IP has been blocked due to suspicious activity.'
            );
        }

        // 2. 检查频率限制
        if (!$this->check_rate_limit($user_ip)) {
            return array(
                'allowed' => false,
                'message' => 'Too many requests. Please wait a moment before sending another message.'
            );
        }

        // 3. 检查消息内容
        if (!$this->check_message_content($message)) {
            // 记录可疑行为
            $this->log_suspicious_activity($user_ip, $message, 'spam_content');
            return array(
                'allowed' => false,
                'message' => 'Message contains inappropriate content.'
            );
        }

        // 4. 检查消息长度
        if (strlen($message) > get_option('yf_ai_chat_max_message_length', 1000)) {
            return array(
                'allowed' => false,
                'message' => 'Message is too long. Please keep it under 1000 characters.'
            );
        }

        // 记录正常请求
        $this->log_request($user_ip);

        return array('allowed' => true);
    }

    private function get_user_ip() {
        // 获取真实IP地址，考虑代理和CDN
        if (!empty($_SERVER['HTTP_CF_CONNECTING_IP'])) {
            return $_SERVER['HTTP_CF_CONNECTING_IP']; // Cloudflare
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            return trim($ips[0]);
        } elseif (!empty($_SERVER['HTTP_X_REAL_IP'])) {
            return $_SERVER['HTTP_X_REAL_IP'];
        } else {
            return $_SERVER['REMOTE_ADDR'];
        }
    }

    private function is_ip_blacklisted($ip) {
        $blacklist = get_option('yf_ai_chat_ip_blacklist', array());
        return in_array($ip, $blacklist);
    }

    private function check_rate_limit($ip) {
        $rate_limit_enabled = get_option('yf_ai_chat_rate_limit_enabled', 1);
        if (!$rate_limit_enabled) {
            return true;
        }

        $max_requests = get_option('yf_ai_chat_max_requests_per_minute', 10);
        $time_window = 60; // 1分钟
        $current_time = $this->get_current_la_time();

        // 获取IP的请求记录
        $requests = get_transient('yf_chat_requests_' . md5($ip));
        if (!$requests) {
            $requests = array();
        }

        // 清理过期的请求记录
        $requests = array_filter($requests, function($timestamp) use ($current_time, $time_window) {
            return ($current_time - $timestamp) < $time_window;
        });

        // 检查是否超过限制
        if (count($requests) >= $max_requests) {
            return false;
        }

        return true;
    }

    private function check_message_content($message) {
        // 检查垃圾内容
        $spam_patterns = get_option('yf_ai_chat_spam_patterns', array(
            // 重复字符
            '/(.)\1{10,}/',
            // 过多特殊字符
            '/[!@#$%^&*()]{5,}/',
            // 常见垃圾词汇
            '/\b(spam|test{3,}|aaa+|bbb+|ccc+)\b/i',
            // 纯数字或字母重复
            '/^(.)\1+$/',
            // URL垃圾
            '/https?:\/\/[^\s]+\.(tk|ml|ga|cf)/i'
        ));

        foreach ($spam_patterns as $pattern) {
            if (preg_match($pattern, $message)) {
                return false;
            }
        }

        // 检查消息是否为空或只有空格
        if (trim($message) === '') {
            return false;
        }

        // 检查是否包含太多重复单词
        $words = explode(' ', strtolower($message));
        $word_count = array_count_values($words);
        foreach ($word_count as $count) {
            if ($count > 5) { // 同一个词重复超过5次
                return false;
            }
        }

        return true;
    }

    private function log_request($ip) {
        $current_time = $this->get_current_la_time();

        // 记录请求到transient
        $requests = get_transient('yf_chat_requests_' . md5($ip));
        if (!$requests) {
            $requests = array();
        }

        $requests[] = $current_time;
        set_transient('yf_chat_requests_' . md5($ip), $requests, 300); // 5分钟过期
    }

    private function log_suspicious_activity($ip, $message, $reason) {
        $log_entry = array(
            'ip' => $ip,
            'message' => substr($message, 0, 100), // 只记录前100个字符
            'reason' => $reason,
            'timestamp' => $this->get_current_la_time(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        );

        // 获取现有日志
        $logs = get_option('yf_ai_chat_security_logs', array());

        // 添加新日志
        $logs[] = $log_entry;

        // 只保留最近100条记录
        if (count($logs) > 100) {
            $logs = array_slice($logs, -100);
        }

        update_option('yf_ai_chat_security_logs', $logs);

        // 检查是否需要自动封禁
        $this->check_auto_ban($ip);
    }

    private function check_auto_ban($ip) {
        $auto_ban_enabled = get_option('yf_ai_chat_auto_ban_enabled', 1);
        if (!$auto_ban_enabled) {
            return;
        }

        $ban_threshold = get_option('yf_ai_chat_ban_threshold', 5);
        $time_window = get_option('yf_ai_chat_ban_time_window', 300); // 5分钟
        $current_time = $this->get_current_la_time();

        // 获取该IP的可疑活动记录
        $logs = get_option('yf_ai_chat_security_logs', array());
        $recent_violations = array_filter($logs, function($log) use ($ip, $current_time, $time_window) {
            return $log['ip'] === $ip && ($current_time - $log['timestamp']) < $time_window;
        });

        // 如果违规次数超过阈值，自动封禁
        if (count($recent_violations) >= $ban_threshold) {
            $this->add_ip_to_blacklist($ip, 'Auto-banned for suspicious activity');
        }
    }

    private function add_ip_to_blacklist($ip, $reason = '') {
        $blacklist = get_option('yf_ai_chat_ip_blacklist', array());
        if (!in_array($ip, $blacklist)) {
            $blacklist[] = $ip;
            update_option('yf_ai_chat_ip_blacklist', $blacklist);

            // 记录封禁日志
            $ban_log = get_option('yf_ai_chat_ban_logs', array());
            $ban_log[] = array(
                'ip' => $ip,
                'reason' => $reason,
                'timestamp' => $this->get_current_la_time()
            );
            update_option('yf_ai_chat_ban_logs', $ban_log);
        }
    }
    
    public function handle_chat_message() {
        check_ajax_referer('yf_chat_nonce', 'nonce');

        // 🔄 防护检查已移至后端处理，WordPress端不再进行防护检查

        $message = sanitize_text_field($_POST['message']);
        $session_id = sanitize_text_field($_POST['session_id']);
        $api_url = get_option('yf_ai_chat_api_url', 'https://chat.22668.xyz');
        $api_token = get_option('yf_ai_chat_api_token', '');

        if (empty($message) || empty($api_token)) {
            wp_die('Invalid request');
        }
        
        // Send message to AI backend
        $response = wp_remote_post($api_url . '/api/chat', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_token,
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode(array(
                'content' => $message,
                'session_id' => $session_id
            )),
            'timeout' => 60  // 🔧 增加到60秒
        ));
        
        if (is_wp_error($response)) {
            error_log('WP Chat Error: ' . $response->get_error_message());
            wp_send_json_error('Connection failed: ' . $response->get_error_message());
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        error_log('WP Chat Response - Status: ' . $status_code . ', Body: ' . $body);

        $data = json_decode($body, true);

        if (isset($data['response'])) {
            // 检查是否为管理员控制状态或空响应
            if (isset($data['admin_controlled']) && $data['admin_controlled'] === true) {
                // 会话被管理员控制，不返回任何消息，让前端等待人工回复
                wp_send_json_success(array('response' => '', 'admin_controlled' => true));
            } elseif (empty(trim($data['response']))) {
                // 空响应，不显示任何消息
                wp_send_json_success(array('response' => '', 'no_response' => true));
            } else {
                // 正常AI响应
                wp_send_json_success($data);
            }
        } else {
            wp_send_json_error('Invalid response');
        }
    }
    
    public function auto_increment_version($upgrader_object, $options) {
        if ($options['action'] == 'update' && $options['type'] == 'plugin') {
            if (isset($options['plugins'])) {
                foreach ($options['plugins'] as $plugin) {
                    if ($plugin == plugin_basename(__FILE__)) {
                        $this->increment_version();
                        break;
                    }
                }
            }
        }
    }
    
    private function increment_version() {
        $current_version = YF_AI_CHAT_VERSION;
        $version_parts = explode('.', $current_version);
        $version_parts[2] = intval($version_parts[2]) + 1;
        $new_version = implode('.', $version_parts);
        
        // Update version in plugin file
        $plugin_file = __FILE__;
        $content = file_get_contents($plugin_file);
        $content = preg_replace('/Version: ' . preg_quote($current_version, '/') . '/', 'Version: ' . $new_version, $content);
        file_put_contents($plugin_file, $content);
    }
}

// Initialize the plugin
new YF_AI_Chat_Now();
