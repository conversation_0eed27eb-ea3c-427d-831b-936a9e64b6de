#!/usr/bin/env python3
"""
Token Manager 打包脚本
使用 PyInstaller 将 token_manager.py 打包成可执行文件
"""

import os
import sys
import subprocess
from pathlib import Path

def build_token_manager():
    """打包 token_manager.py"""
    
    # 获取当前目录
    current_dir = Path(__file__).parent
    
    # 源文件路径
    source_file = current_dir / "token_manager.py"
    
    # 图标文件路径
    icon_file = current_dir / "resources" / "text_icon.ico"
    
    # 输出目录
    dist_dir = current_dir / "dist"
    build_dir = current_dir / "build"
    
    # 检查源文件是否存在
    if not source_file.exists():
        print(f"❌ 源文件不存在: {source_file}")
        return False
    
    # 检查图标文件是否存在
    if not icon_file.exists():
        print(f"❌ 图标文件不存在: {icon_file}")
        return False
    
    print("🔨 开始打包 Token Manager...")
    print(f"📁 源文件: {source_file}")
    print(f"🎨 图标文件: {icon_file}")
    print()
    
    # PyInstaller 命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--console",                    # 显示控制台窗口
        f"--icon={icon_file}",          # 设置图标
        f"--name=YF_AI_Token_Manager",  # 设置输出文件名
        "--clean",                      # 清理临时文件
        str(source_file)
    ]
    
    try:
        # 执行打包命令
        print("🚀 执行打包命令...")
        print(f"命令: {' '.join(cmd)}")
        print()
        
        result = subprocess.run(cmd, cwd=current_dir, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 打包成功!")
            
            # 查找生成的exe文件
            exe_file = dist_dir / "YF_AI_Token_Manager.exe"
            if exe_file.exists():
                print(f"📦 可执行文件位置: {exe_file}")
                print(f"📏 文件大小: {exe_file.stat().st_size / 1024 / 1024:.2f} MB")
                
                # 创建桌面快捷方式的说明
                print()
                print("📋 使用说明:")
                print("1. 可执行文件已生成，可以直接运行")
                print("2. 生成的token将保存在exe同目录下的 generated_tokens.json 文件中")
                print("3. 每次生成新token都会追加到文件中，包含时间戳")
                print()
                print("🖥️ 创建桌面快捷方式:")
                print(f"   右键点击 {exe_file}")
                print("   选择 '发送到' -> '桌面快捷方式'")
                
                return True
            else:
                print(f"❌ 未找到生成的exe文件: {exe_file}")
                return False
        else:
            print("❌ 打包失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except FileNotFoundError:
        print("❌ PyInstaller 未安装!")
        print("请先安装 PyInstaller:")
        print("pip install pyinstaller")
        return False
    except Exception as e:
        print(f"❌ 打包过程中出现错误: {e}")
        return False

def clean_build_files():
    """清理构建文件"""
    current_dir = Path(__file__).parent
    
    # 要清理的目录和文件
    clean_targets = [
        current_dir / "build",
        current_dir / "dist",
        current_dir / "YF_AI_Token_Manager.spec"
    ]
    
    print("🧹 清理构建文件...")
    
    for target in clean_targets:
        if target.exists():
            if target.is_dir():
                import shutil
                shutil.rmtree(target)
                print(f"🗑️ 已删除目录: {target}")
            else:
                target.unlink()
                print(f"🗑️ 已删除文件: {target}")
    
    print("✅ 清理完成!")

def main():
    """主函数"""
    print("🔐 YF AI Chat Token Manager 打包工具")
    print("=" * 60)
    print()
    print("选择操作:")
    print("1. 打包 Token Manager")
    print("2. 清理构建文件")
    print("3. 退出")
    print()
    
    while True:
        choice = input("请选择 (1-3): ").strip()
        
        if choice == "1":
            print()
            success = build_token_manager()
            if success:
                print()
                input("按回车键退出...")
            break
        elif choice == "2":
            print()
            clean_build_files()
            print()
            input("按回车键退出...")
            break
        elif choice == "3":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请输入 1-3")

if __name__ == "__main__":
    main()
