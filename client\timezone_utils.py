#!/usr/bin/env python3
"""
客户端时区工具模块
提供洛杉矶时间处理功能，独立于后端配置
"""

import os
from datetime import datetime, timezone, timedelta
from dotenv import load_dotenv

# 加载环境变量 - 从exe所在目录
try:
    from config_utils import get_config_path
    env_path = get_config_path('.env')
    load_dotenv(env_path)
except ImportError:
    # 如果config_utils不可用，使用默认方式
    load_dotenv()

# 从环境变量读取时区配置
DEFAULT_TIMEZONE = os.getenv("CLIENT_TIMEZONE", "America/Los_Angeles")
TIMEZONE_NAME = os.getenv("CLIENT_TIMEZONE_NAME", "洛杉矶时间")
FALLBACK_OFFSET = os.getenv("CLIENT_FALLBACK_OFFSET", "-08:00")

# 尝试导入时区库
try:
    # Python 3.9+ 使用 zoneinfo
    from zoneinfo import ZoneInfo
    ZONEINFO_AVAILABLE = True
except ImportError:
    ZONEINFO_AVAILABLE = False

try:
    # 备用方案使用 pytz
    import pytz  # type: ignore
    PYTZ_AVAILABLE = True
except ImportError:
    PYTZ_AVAILABLE = False

def get_la_timezone():
    """获取洛杉矶时区对象"""
    if ZONEINFO_AVAILABLE:
        try:
            return ZoneInfo(DEFAULT_TIMEZONE)
        except Exception:
            pass
    
    if PYTZ_AVAILABLE:
        try:
            return pytz.timezone(DEFAULT_TIMEZONE)
        except Exception:
            pass
    
    # 如果都不可用，使用环境变量配置的偏移量
    try:
        # 解析偏移量格式 +/-HH:MM
        if FALLBACK_OFFSET.startswith(('+', '-')):
            sign = 1 if FALLBACK_OFFSET[0] == '+' else -1
            hours, minutes = map(int, FALLBACK_OFFSET[1:].split(':'))
            offset = timedelta(hours=sign * hours, minutes=sign * minutes)
            print(f"⚠️ 客户端时区库不可用，使用配置的偏移量 {FALLBACK_OFFSET}")
            return timezone(offset)
    except Exception as e:
        print(f"⚠️ 客户端解析偏移量失败: {e}")

    # 最后的备用方案
    print("⚠️ 客户端时区库不可用，使用默认偏移量 UTC-8")
    return timezone(timedelta(hours=-8))

# 创建时区对象
LA_TIMEZONE = get_la_timezone()

def get_current_time():
    """获取当前洛杉矶时间"""
    return datetime.now(LA_TIMEZONE)

def get_current_time_naive():
    """获取当前洛杉矶时间（naive datetime，用于数据库存储）"""
    return datetime.now(LA_TIMEZONE).replace(tzinfo=None)

def format_time_for_display(dt, format_str="%Y-%m-%d %H:%M:%S"):
    """格式化时间用于显示"""
    if dt is None:
        return ""
    
    # 如果是字符串，先解析
    if isinstance(dt, str):
        dt = parse_iso_time(dt)
        if dt is None:
            return ""
    
    # 如果是naive datetime，假设它是洛杉矶时间
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=LA_TIMEZONE)
    else:
        # 转换到洛杉矶时间
        dt = dt.astimezone(LA_TIMEZONE)
    
    return dt.strftime(format_str)

def format_time_for_chat(dt):
    """格式化时间用于聊天显示（只显示时分秒）"""
    if dt is None:
        return ""
    
    # 如果是字符串，先解析
    if isinstance(dt, str):
        dt = parse_iso_time(dt)
        if dt is None:
            return ""
    
    return format_time_for_display(dt, "%H:%M:%S")

def format_time_for_session_list(dt):
    """格式化时间用于会话列表显示"""
    if dt is None:
        return ""
    
    # 如果是字符串，先解析
    if isinstance(dt, str):
        dt = parse_iso_time(dt)
        if dt is None:
            return ""
    
    return format_time_for_display(dt, "%m-%d %H:%M")

def parse_iso_time(iso_string):
    """解析ISO格式时间字符串并转换为洛杉矶时间"""
    if not iso_string:
        return None
    
    try:
        # 处理各种ISO格式
        if iso_string.endswith('Z'):
            # UTC时间
            dt = datetime.fromisoformat(iso_string.replace('Z', '+00:00'))
        elif '+' in iso_string or iso_string.count('-') > 2:
            # 包含时区信息
            dt = datetime.fromisoformat(iso_string)
        else:
            # 没有时区信息，假设是UTC
            dt = datetime.fromisoformat(iso_string).replace(tzinfo=timezone.utc)
        
        # 转换到洛杉矶时间
        return dt.astimezone(LA_TIMEZONE)
    except Exception as e:
        print(f"解析时间失败: {iso_string}, 错误: {e}")
        # 备用解析方法
        try:
            # 简单的ISO格式解析
            if 'T' in iso_string:
                date_part, time_part = iso_string.split('T')
                time_part = time_part.replace('Z', '').split('.')[0]  # 移除时区和毫秒
                dt_str = f"{date_part} {time_part}"
                dt = datetime.strptime(dt_str, "%Y-%m-%d %H:%M:%S")
                # 假设是UTC时间，转换为洛杉矶时间
                dt = dt.replace(tzinfo=timezone.utc).astimezone(LA_TIMEZONE)
                return dt
        except Exception:
            pass
        
        return None

def convert_utc_to_la(utc_dt):
    """将UTC时间转换为洛杉矶时间"""
    if utc_dt is None:
        return None
    
    if utc_dt.tzinfo is None:
        # 假设是UTC时间
        utc_dt = utc_dt.replace(tzinfo=timezone.utc)
    
    return utc_dt.astimezone(LA_TIMEZONE)

def get_timezone_info():
    """获取时区信息"""
    current_time = get_current_time()
    return {
        "timezone": DEFAULT_TIMEZONE,
        "timezone_name": TIMEZONE_NAME,
        "current_time": current_time.isoformat(),
        "current_time_display": format_time_for_display(current_time),
        "utc_offset": current_time.strftime("%z"),
        "is_dst": current_time.dst() != timedelta(0) if hasattr(current_time, 'dst') else False
    }

def setup_environment_timezone():
    """设置环境变量时区"""
    os.environ['TZ'] = DEFAULT_TIMEZONE
    try:
        import time
        time.tzset()
    except AttributeError:
        # Windows 不支持 tzset
        pass

# 在模块导入时自动设置时区
setup_environment_timezone()

if __name__ == "__main__":
    # 测试时区配置
    print("🕐 客户端时区配置测试")
    print("=" * 50)
    
    info = get_timezone_info()
    for key, value in info.items():
        print(f"{key}: {value}")
    
    print("\n🕐 时间格式测试")
    print("=" * 50)
    current = get_current_time()
    print(f"当前时间: {current}")
    print(f"显示格式: {format_time_for_display(current)}")
    print(f"聊天格式: {format_time_for_chat(current)}")
    print(f"会话列表格式: {format_time_for_session_list(current)}")
    
    print("\n🕐 时间转换测试")
    print("=" * 50)
    test_iso = "2024-01-01T16:30:00Z"
    parsed = parse_iso_time(test_iso)
    print(f"ISO时间: {test_iso}")
    print(f"转换后: {parsed}")
    print(f"显示格式: {format_time_for_display(parsed)}")
