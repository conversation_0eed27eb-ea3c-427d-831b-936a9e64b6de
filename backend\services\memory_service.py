"""
基于现有数据库的长期记忆管理系统
直接从chat_messages和chat_sessions表读取数据进行分析
"""

import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import text
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.database import get_db
from models.chat_models import ChatMessage, ChatSession
from utils.file_logger import backend_logger as logger


class MemoryService:
    def __init__(self, db_service=None):
        self.preference_cache = {}  # 缓存用户偏好分析结果
        self.db_service = db_service  # 接受外部传入的数据库服务实例
        
    def get_user_id_from_session(self, session_id: str) -> str:
        """从session_id提取user_id（基于浏览器指纹）"""
        # session_id格式: session_r0zoa5_1750659269177
        # 提取中间的浏览器指纹部分作为user_id
        if "_" in session_id:
            parts = session_id.split("_")
            if len(parts) >= 2:
                return f"user_{parts[1]}"  # user_r0zoa5
        return f"user_{session_id}"
    
    async def get_user_conversation_history(self, session_id: str, days: int = 30) -> List[Dict[str, Any]]:
        """获取用户的历史对话记录（异步版本）"""
        user_id = self.get_user_id_from_session(session_id)

        try:
            # 使用异步数据库连接
            if self.db_service:
                db_service = self.db_service
            else:
                from services.database import get_db_service
                db_service = get_db_service()

            # 查询同一用户（相同浏览器指纹）的所有会话
            cutoff_date = datetime.now() - timedelta(days=days)

            # 提取浏览器指纹用于模糊匹配
            browser_fingerprint = user_id.replace("user_", "")
            user_pattern = f"%{browser_fingerprint}%"

            # 使用异步数据库查询
            async with db_service.async_session() as db:
                from sqlalchemy import text
                query = text("""
                    SELECT cm.session_id, cm.sender, cm.content, cm.created_at
                    FROM chat_messages cm
                    JOIN chat_sessions cs ON cm.session_id = cs.id
                    WHERE cm.session_id LIKE :user_pattern
                    AND cm.created_at >= :cutoff_date
                    ORDER BY cm.created_at DESC
                    LIMIT 100
                """)

                result = await db.execute(query, {
                    "user_pattern": user_pattern,
                    "cutoff_date": cutoff_date
                })

                messages = []
                for row in result:
                    # 处理created_at字段，可能是datetime对象或字符串
                    timestamp = None
                    if row.created_at:
                        if hasattr(row.created_at, 'isoformat'):
                            # 如果是datetime对象
                            timestamp = row.created_at.isoformat()
                        else:
                            # 如果是字符串，直接使用
                            timestamp = str(row.created_at)

                    messages.append({
                        "session_id": row.session_id,
                        "role": row.sender,
                        "content": row.content,
                        "timestamp": timestamp
                    })

                return messages

        except Exception as e:
            logger.error(f"获取用户对话历史失败: {e}")
            return []
    
    async def analyze_user_preferences(self, session_id: str) -> Dict[str, Any]:
        """分析用户偏好（异步版本）"""
        user_id = self.get_user_id_from_session(session_id)

        # 检查缓存
        if user_id in self.preference_cache:
            cache_time = self.preference_cache[user_id].get("analyzed_at")
            if cache_time and datetime.fromisoformat(cache_time) > datetime.now() - timedelta(hours=1):
                return self.preference_cache[user_id]["preferences"]

        # 获取历史对话（异步）
        history = await self.get_user_conversation_history(session_id)

        # 分析偏好
        preferences = self._analyze_preferences_from_history(history)

        # 缓存结果
        self.preference_cache[user_id] = {
            "preferences": preferences,
            "analyzed_at": datetime.now().isoformat()
        }

        return preferences
    
    def _analyze_preferences_from_history(self, history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """从历史对话中分析用户偏好"""
        preferences = {
            "product_interests": {},
            "price_sensitivity": "unknown",
            "quality_focus": "unknown", 
            "convenience_focus": "unknown",
            "health_focus": "unknown",
            "communication_style": "unknown",
            "interaction_patterns": {
                "total_messages": 0,
                "avg_message_length": 0,
                "session_count": 0
            }
        }
        
        if not history:
            return preferences
        
        # 只分析用户消息
        user_messages = [msg for msg in history if msg["role"] == "user"]
        
        if not user_messages:
            return preferences
        
        # 统计基本信息
        preferences["interaction_patterns"]["total_messages"] = len(user_messages)
        preferences["interaction_patterns"]["session_count"] = len(set(msg["session_id"] for msg in history))
        
        # 计算平均消息长度
        total_length = sum(len(msg["content"]) for msg in user_messages)
        preferences["interaction_patterns"]["avg_message_length"] = total_length / len(user_messages)
        
        # 分析各种偏好
        self._analyze_product_interests(user_messages, preferences)
        self._analyze_price_sensitivity(user_messages, preferences)
        self._analyze_quality_focus(user_messages, preferences)
        self._analyze_convenience_focus(user_messages, preferences)
        self._analyze_health_focus(user_messages, preferences)
        self._analyze_communication_style(user_messages, preferences)
        
        return preferences
    
    def _analyze_product_interests(self, messages: List[Dict[str, Any]], preferences: Dict[str, Any]):
        """分析产品兴趣"""
        product_keywords = {
            "air_purifier": ["空气净化器", "净化器", "air purifier", "hepa", "过滤", "pm2.5"],
            "dehumidifier": ["除湿机", "除湿器", "dehumidifier", "湿度", "潮湿", "除湿"],
            "humidifier": ["加湿器", "humidifier", "干燥", "加湿"],
            "air_quality": ["空气质量", "air quality", "甲醛", "异味", "空气"]
        }
        
        interests = {}
        for msg in messages:
            content = msg["content"].lower()
            for product, keywords in product_keywords.items():
                count = sum(1 for keyword in keywords if keyword in content)
                if count > 0:
                    interests[product] = interests.get(product, 0) + count
        
        preferences["product_interests"] = interests
    
    def _analyze_price_sensitivity(self, messages: List[Dict[str, Any]], preferences: Dict[str, Any]):
        """分析价格敏感度"""
        price_keywords = ["便宜", "优惠", "折扣", "价格", "多少钱", "贵", "预算", "cost", "price"]
        quality_keywords = ["质量", "品质", "效果", "性能", "技术", "quality"]
        
        price_mentions = 0
        quality_mentions = 0
        
        for msg in messages:
            content = msg["content"].lower()
            price_mentions += sum(1 for keyword in price_keywords if keyword in content)
            quality_mentions += sum(1 for keyword in quality_keywords if keyword in content)
        
        if price_mentions > quality_mentions * 2:
            preferences["price_sensitivity"] = "high"
        elif quality_mentions > price_mentions * 2:
            preferences["price_sensitivity"] = "low"
        elif price_mentions > 0 or quality_mentions > 0:
            preferences["price_sensitivity"] = "medium"
    
    def _analyze_quality_focus(self, messages: List[Dict[str, Any]], preferences: Dict[str, Any]):
        """分析质量关注度"""
        quality_keywords = ["品质", "质量", "技术", "参数", "认证", "品牌", "专业", "性能"]
        
        quality_mentions = sum(
            1 for msg in messages 
            for keyword in quality_keywords 
            if keyword in msg["content"].lower()
        )
        
        if quality_mentions >= 3:
            preferences["quality_focus"] = "high"
        elif quality_mentions >= 1:
            preferences["quality_focus"] = "medium"
    
    def _analyze_convenience_focus(self, messages: List[Dict[str, Any]], preferences: Dict[str, Any]):
        """分析便利性关注度"""
        convenience_keywords = ["方便", "简单", "易用", "自动", "智能", "维护", "操作"]
        
        convenience_mentions = sum(
            1 for msg in messages 
            for keyword in convenience_keywords 
            if keyword in msg["content"].lower()
        )
        
        if convenience_mentions >= 2:
            preferences["convenience_focus"] = "high"
        elif convenience_mentions >= 1:
            preferences["convenience_focus"] = "medium"
    
    def _analyze_health_focus(self, messages: List[Dict[str, Any]], preferences: Dict[str, Any]):
        """分析健康关注度"""
        health_keywords = ["健康", "过敏", "呼吸", "孩子", "老人", "安全", "医疗", "病"]
        
        health_mentions = sum(
            1 for msg in messages 
            for keyword in health_keywords 
            if keyword in msg["content"].lower()
        )
        
        if health_mentions >= 2:
            preferences["health_focus"] = "high"
        elif health_mentions >= 1:
            preferences["health_focus"] = "medium"
    
    def _analyze_communication_style(self, messages: List[Dict[str, Any]], preferences: Dict[str, Any]):
        """分析沟通风格"""
        if not messages:
            return
            
        avg_length = preferences["interaction_patterns"]["avg_message_length"]
        
        if avg_length > 50:
            preferences["communication_style"] = "detailed"
        elif avg_length < 15:
            preferences["communication_style"] = "concise"
        else:
            preferences["communication_style"] = "balanced"
    
    async def get_personalization_context(self, session_id: str) -> str:
        """生成个性化上下文（异步版本）"""
        preferences = await self.analyze_user_preferences(session_id)
        context_parts = []
        
        # 产品兴趣
        if preferences["product_interests"]:
            top_interest = max(preferences["product_interests"].items(), key=lambda x: x[1])
            if top_interest[1] >= 2:  # 至少提到2次
                context_parts.append(f"用户对{top_interest[0]}特别感兴趣")
        
        # 价格敏感度
        if preferences["price_sensitivity"] == "high":
            context_parts.append("用户对价格比较敏感")
        elif preferences["price_sensitivity"] == "low":
            context_parts.append("用户更关注产品质量而非价格")
        
        # 质量关注度
        if preferences["quality_focus"] == "high":
            context_parts.append("用户特别关注产品质量和技术参数")
        
        # 健康关注度
        if preferences["health_focus"] == "high":
            context_parts.append("用户非常关心健康相关的产品功能")
        
        # 便利性关注度
        if preferences["convenience_focus"] == "high":
            context_parts.append("用户重视产品的易用性和便利性")
        
        # 沟通风格
        if preferences["communication_style"] == "detailed":
            context_parts.append("用户喜欢详细的解释和具体的技术参数")
        elif preferences["communication_style"] == "concise":
            context_parts.append("用户偏好简洁明了的回答")
        
        if context_parts:
            return "个性化提示：" + "。".join(context_parts) + "。"
        else:
            return ""
    
    async def get_recent_conversation_context(self, session_id: str, turns: int = 10) -> List[Dict[str, Any]]:
        """获取最近的对话上下文（异步版本）"""
        try:
            # 使用异步数据库连接
            if self.db_service:
                db_service = self.db_service
            else:
                from services.database import get_db_service
                db_service = get_db_service()

            # 使用异步数据库查询
            async with db_service.async_session() as db:
                from sqlalchemy import select
                from models.chat_models import ChatMessage

                # 构建查询
                query = select(ChatMessage).filter(
                    ChatMessage.session_id == session_id
                ).order_by(ChatMessage.created_at.desc()).limit(turns)

                result = await db.execute(query)
                messages = result.scalars().all()

                result_messages = []
                for msg in reversed(messages):
                    # 处理created_at字段，可能是datetime对象或字符串
                    timestamp = None
                    if msg.created_at:
                        if hasattr(msg.created_at, 'isoformat'):
                            # 如果是datetime对象
                            timestamp = msg.created_at.isoformat()
                        else:
                            # 如果是字符串，直接使用
                            timestamp = str(msg.created_at)

                    result_messages.append({
                        "role": msg.sender,
                        "content": msg.content,
                        "timestamp": timestamp
                    })

                return result_messages
        except Exception as e:
            logger.error(f"获取对话上下文失败: {e}")
            return []
    
    def update_user_interaction(self, session_id: str, message: str, response: str):
        """更新用户交互（触发偏好重新分析）"""
        user_id = self.get_user_id_from_session(session_id)
        
        # 清除缓存，下次查询时重新分析
        if user_id in self.preference_cache:
            del self.preference_cache[user_id]
        
        logger.debug(f"用户交互已更新，偏好缓存已清除: {user_id}")


# 全局实例 - 延迟初始化
memory_service = None


def get_memory_service(db_service=None) -> MemoryService:
    """获取记忆服务实例"""
    global memory_service
    if memory_service is None:
        memory_service = MemoryService(db_service)
    return memory_service
