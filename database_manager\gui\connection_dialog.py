#!/usr/bin/env python3
"""
数据库连接对话框
用于选择和连接数据库文件
"""

import os
from pathlib import Path
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QComboBox, QGroupBox,
                            QFileDialog, QMessageBox, QCheckBox, QTextEdit)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

from core.config import config_manager

class ConnectionDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_path = ""
        self.init_ui()
        self.load_history()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("连接数据库")
        self.setModal(True)
        self.setMinimumSize(600, 400)
        
        layout = QVBoxLayout(self)
        
        # 数据库路径选择组
        path_group = QGroupBox("数据库路径")
        path_layout = QVBoxLayout(path_group)
        
        # 路径输入行
        path_input_layout = QHBoxLayout()
        
        self.path_input = QLineEdit()
        self.path_input.setPlaceholderText("选择或输入数据库文件路径...")
        path_input_layout.addWidget(self.path_input)
        
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_database_file)
        path_input_layout.addWidget(browse_btn)
        
        path_layout.addLayout(path_input_layout)
        
        # 历史路径下拉框
        history_layout = QHBoxLayout()
        history_layout.addWidget(QLabel("历史路径:"))
        
        self.history_combo = QComboBox()
        self.history_combo.currentTextChanged.connect(self.on_history_selected)
        history_layout.addWidget(self.history_combo)
        
        clear_history_btn = QPushButton("清除历史")
        clear_history_btn.clicked.connect(self.clear_history)
        history_layout.addWidget(clear_history_btn)
        
        path_layout.addLayout(history_layout)
        
        layout.addWidget(path_group)
        
        # 快速连接组
        quick_group = QGroupBox("快速连接")
        quick_layout = QVBoxLayout(quick_group)
        
        # 默认数据库路径按钮
        default_paths_layout = QHBoxLayout()
        
        # 后端数据库路径
        backend_btn = QPushButton("后端数据库 (backend/data/chat_data.db)")
        backend_btn.clicked.connect(self.select_backend_database)
        default_paths_layout.addWidget(backend_btn)
        
        # 当前目录数据库
        current_btn = QPushButton("当前目录搜索")
        current_btn.clicked.connect(self.search_current_directory)
        default_paths_layout.addWidget(current_btn)
        
        quick_layout.addLayout(default_paths_layout)
        
        layout.addWidget(quick_group)
        
        # 连接选项组
        options_group = QGroupBox("连接选项")
        options_layout = QVBoxLayout(options_group)
        
        self.readonly_checkbox = QCheckBox("只读模式（推荐用于生产数据库）")
        options_layout.addWidget(self.readonly_checkbox)
        
        self.backup_checkbox = QCheckBox("连接前创建备份")
        options_layout.addWidget(self.backup_checkbox)
        
        layout.addWidget(options_group)
        
        # 数据库信息预览
        info_group = QGroupBox("数据库信息预览")
        info_layout = QVBoxLayout(info_group)
        
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(100)
        self.info_text.setReadOnly(True)
        self.info_text.setPlaceholderText("选择数据库文件后将显示基本信息...")
        info_layout.addWidget(self.info_text)
        
        layout.addWidget(info_group)
        
        # 按钮组
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.connect_btn = QPushButton("连接")
        self.connect_btn.clicked.connect(self.accept_connection)
        self.connect_btn.setEnabled(False)
        self.connect_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        button_layout.addWidget(self.connect_btn)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
        
        # 连接路径输入变化事件
        self.path_input.textChanged.connect(self.on_path_changed)
    
    def load_history(self):
        """加载历史路径"""
        history = config_manager.get_database_paths()
        self.history_combo.clear()
        self.history_combo.addItem("选择历史路径...")
        
        for path in history:
            if os.path.exists(path):
                self.history_combo.addItem(path)
        
        # 设置上次使用的路径
        last_path = config_manager.get_last_database_path()
        if last_path and os.path.exists(last_path):
            self.path_input.setText(last_path)
    
    def browse_database_file(self):
        """浏览数据库文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择数据库文件", "", 
            "SQLite数据库 (*.db *.sqlite *.sqlite3);;所有文件 (*.*)"
        )
        
        if file_path:
            self.path_input.setText(file_path)
    
    def on_history_selected(self, path):
        """历史路径被选中"""
        if path and path != "选择历史路径...":
            self.path_input.setText(path)
    
    def clear_history(self):
        """清除历史记录"""
        reply = QMessageBox.question(self, "确认", "确定要清除所有历史记录吗？")
        if reply == QMessageBox.StandardButton.Yes:
            config_manager.config["database_paths"] = []
            config_manager.save_config()
            self.load_history()
    
    def select_backend_database(self):
        """选择后端数据库"""
        # 尝试找到后端数据库文件
        possible_paths = [
            "backend/data/chat_data.db",
            "../backend/data/chat_data.db",
            "../../backend/data/chat_data.db",
            "backend/chat_data.db",
            "../backend/chat_data.db"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                self.path_input.setText(os.path.abspath(path))
                return
        
        QMessageBox.information(self, "提示", 
                               "未找到后端数据库文件，请手动选择。\n"
                               "通常位于: backend/data/chat_data.db")
    
    def search_current_directory(self):
        """搜索当前目录下的数据库文件"""
        current_dir = Path.cwd()
        db_files = []
        
        # 搜索常见的数据库文件扩展名
        for pattern in ["*.db", "*.sqlite", "*.sqlite3"]:
            db_files.extend(current_dir.rglob(pattern))
        
        if not db_files:
            QMessageBox.information(self, "搜索结果", "当前目录下未找到数据库文件")
            return
        
        # 如果只有一个文件，直接选择
        if len(db_files) == 1:
            self.path_input.setText(str(db_files[0]))
            return
        
        # 多个文件，让用户选择
        from PyQt6.QtWidgets import QInputDialog
        
        file_names = [str(f) for f in db_files]
        selected_file, ok = QInputDialog.getItem(
            self, "选择数据库文件", "找到多个数据库文件，请选择:", 
            file_names, 0, False
        )
        
        if ok and selected_file:
            self.path_input.setText(selected_file)
    
    def on_path_changed(self, path):
        """路径输入改变"""
        self.selected_path = path.strip()
        
        # 更新连接按钮状态
        self.connect_btn.setEnabled(bool(self.selected_path))
        
        # 更新数据库信息预览
        self.update_database_info()
    
    def update_database_info(self):
        """更新数据库信息预览"""
        if not self.selected_path or not os.path.exists(self.selected_path):
            self.info_text.clear()
            if self.selected_path:
                self.info_text.setText("文件不存在")
            return
        
        try:
            import sqlite3
            
            # 获取文件信息
            file_path = Path(self.selected_path)
            file_size = file_path.stat().st_size
            
            # 连接数据库获取基本信息
            conn = sqlite3.connect(self.selected_path)
            cursor = conn.cursor()
            
            # 获取表数量
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]
            
            # 获取SQLite版本
            cursor.execute("SELECT sqlite_version()")
            sqlite_version = cursor.fetchone()[0]
            
            conn.close()
            
            # 显示信息
            info_text = f"文件大小: {file_size / 1024:.2f} KB\n"
            info_text += f"表数量: {table_count}\n"
            info_text += f"SQLite版本: {sqlite_version}"
            
            self.info_text.setText(info_text)
            
        except Exception as e:
            self.info_text.setText(f"无法读取数据库信息: {e}")
    
    def accept_connection(self):
        """接受连接"""
        if not self.selected_path:
            QMessageBox.warning(self, "警告", "请选择数据库文件")
            return
        
        if not os.path.exists(self.selected_path):
            QMessageBox.warning(self, "警告", "数据库文件不存在")
            return
        
        # 如果选择了备份选项
        if self.backup_checkbox.isChecked():
            self.create_backup()
        
        self.accept()
    
    def create_backup(self):
        """创建数据库备份"""
        try:
            import shutil
            from datetime import datetime
            
            source_path = Path(self.selected_path)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{source_path.stem}_backup_{timestamp}{source_path.suffix}"
            backup_path = source_path.parent / backup_name
            
            shutil.copy2(source_path, backup_path)
            QMessageBox.information(self, "备份完成", f"备份已创建: {backup_path}")
            
        except Exception as e:
            QMessageBox.warning(self, "备份失败", f"创建备份失败: {e}")
    
    def get_selected_path(self):
        """获取选择的路径"""
        return self.selected_path
    
    def is_readonly_mode(self):
        """是否为只读模式"""
        return self.readonly_checkbox.isChecked()
