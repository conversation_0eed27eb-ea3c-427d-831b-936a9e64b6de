#!/usr/bin/env python3
"""
用户管理GUI界面
"""

import sys
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTableWidget, QTableWidgetItem, QPushButton, QDialog, QFormLayout,
    QLineEdit, QComboBox, QDialogButtonBox, QMessageBox, QLabel,
    QHeaderView
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from user_manager import UserManager

class AddUserDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加用户")
        self.setFixedSize(300, 250)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # Title
        title = QLabel("添加新用户")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # Form
        form_layout = QFormLayout()
        
        self.username_input = QLineEdit()
        form_layout.addRow("用户名:", self.username_input)
        
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        form_layout.addRow("密码:", self.password_input)
        
        self.name_input = QLineEdit()
        form_layout.addRow("显示名称:", self.name_input)
        
        self.role_combo = QComboBox()
        self.role_combo.addItems(["operator", "admin"])
        form_layout.addRow("角色:", self.role_combo)
        
        layout.addLayout(form_layout)
        
        # Buttons
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.setLayout(layout)
    
    def get_user_data(self):
        return {
            "username": self.username_input.text().strip(),
            "password": self.password_input.text().strip(),
            "name": self.name_input.text().strip(),
            "role": self.role_combo.currentText()
        }

class ChangePasswordDialog(QDialog):
    def __init__(self, username, parent=None):
        super().__init__(parent)
        self.username = username
        self.setWindowTitle(f"修改密码 - {username}")
        self.setFixedSize(300, 150)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # Title
        title = QLabel(f"修改用户 {self.username} 的密码")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # Form
        form_layout = QFormLayout()
        
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        form_layout.addRow("新密码:", self.password_input)
        
        layout.addLayout(form_layout)
        
        # Buttons
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
        self.setLayout(layout)
    
    def get_password(self):
        return self.password_input.text().strip()

class UserManagementWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.user_manager = UserManager()
        self.setup_ui()
        self.refresh_users()
    
    def setup_ui(self):
        self.setWindowTitle("YF AI Chat - 用户管理")
        self.setGeometry(100, 100, 800, 600)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        layout = QVBoxLayout()
        
        # Title
        title = QLabel("用户管理")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.add_user_btn = QPushButton("添加用户")
        self.add_user_btn.clicked.connect(self.add_user)
        button_layout.addWidget(self.add_user_btn)
        
        self.change_password_btn = QPushButton("修改密码")
        self.change_password_btn.clicked.connect(self.change_password)
        button_layout.addWidget(self.change_password_btn)
        
        self.deactivate_user_btn = QPushButton("停用用户")
        self.deactivate_user_btn.clicked.connect(self.deactivate_user)
        button_layout.addWidget(self.deactivate_user_btn)
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_users)
        button_layout.addWidget(self.refresh_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # Users table
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(5)
        self.users_table.setHorizontalHeaderLabels(["用户名", "显示名称", "角色", "状态", "创建时间"])
        
        # Set column widths
        header = self.users_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        
        layout.addWidget(self.users_table)
        
        central_widget.setLayout(layout)
    
    def refresh_users(self):
        """刷新用户列表"""
        users = self.user_manager.list_users()
        
        self.users_table.setRowCount(len(users))
        
        for row, user in enumerate(users):
            self.users_table.setItem(row, 0, QTableWidgetItem(user["username"]))
            self.users_table.setItem(row, 1, QTableWidgetItem(user["name"]))
            self.users_table.setItem(row, 2, QTableWidgetItem(user["role"]))
            
            status = "活跃" if user["active"] else "停用"
            self.users_table.setItem(row, 3, QTableWidgetItem(status))
            
            created_at = user["created_at"][:10] if user["created_at"] else ""
            self.users_table.setItem(row, 4, QTableWidgetItem(created_at))
    
    def get_selected_username(self):
        """获取选中的用户名"""
        current_row = self.users_table.currentRow()
        if current_row >= 0:
            return self.users_table.item(current_row, 0).text()
        return None
    
    def add_user(self):
        """添加用户"""
        dialog = AddUserDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            user_data = dialog.get_user_data()
            
            if not user_data["username"] or not user_data["password"]:
                QMessageBox.warning(self, "错误", "用户名和密码不能为空")
                return
            
            if self.user_manager.add_user(
                user_data["username"],
                user_data["password"],
                user_data["name"],
                user_data["role"]
            ):
                QMessageBox.information(self, "成功", f"用户 {user_data['username']} 添加成功")
                self.refresh_users()
            else:
                QMessageBox.warning(self, "错误", f"用户 {user_data['username']} 已存在")
    
    def change_password(self):
        """修改密码"""
        username = self.get_selected_username()
        if not username:
            QMessageBox.warning(self, "错误", "请先选择一个用户")
            return
        
        dialog = ChangePasswordDialog(username, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            new_password = dialog.get_password()
            
            if not new_password:
                QMessageBox.warning(self, "错误", "密码不能为空")
                return
            
            if self.user_manager.update_password(username, new_password):
                QMessageBox.information(self, "成功", f"用户 {username} 密码修改成功")
            else:
                QMessageBox.warning(self, "错误", f"用户 {username} 不存在")
    
    def deactivate_user(self):
        """停用用户"""
        username = self.get_selected_username()
        if not username:
            QMessageBox.warning(self, "错误", "请先选择一个用户")
            return
        
        if username == "admin":
            QMessageBox.warning(self, "错误", "不能停用admin用户")
            return
        
        reply = QMessageBox.question(
            self, "确认", f"确定要停用用户 {username} 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            if self.user_manager.deactivate_user(username):
                QMessageBox.information(self, "成功", f"用户 {username} 已停用")
                self.refresh_users()
            else:
                QMessageBox.warning(self, "错误", f"用户 {username} 不存在")

def main():
    app = QApplication(sys.argv)
    window = UserManagementWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
