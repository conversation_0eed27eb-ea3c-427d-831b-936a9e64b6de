#!/usr/bin/env python3
"""
高级设置配置组件
负责变量管理、对话规则等高级配置
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QTextEdit, QComboBox, QGroupBox,
                            QFormLayout, QScrollArea, QListWidget, QListWidgetItem,
                            QPushButton, QInputDialog, QMessageBox, QCheckBox,
                            QTreeWidget, QTreeWidgetItem, QSplitter, QTabWidget,
                            QSpinBox, QTableWidget, QTableWidgetItem, QHeaderView,
                            QRadioButton, QButtonGroup)
from PyQt6.QtCore import pyqtSignal, Qt
from PyQt6.QtGui import QFont


class AdvancedSettingsWidget(QWidget):
    """高级设置配置组件"""

    # 信号定义
    config_changed = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.config_data = {}
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 主容器
        main_widget = QWidget()
        layout = QVBoxLayout(main_widget)

        # 创建标签页
        tab_widget = QTabWidget()

        # 对话规则标签页
        conversation_tab = self.create_conversation_tab()
        tab_widget.addTab(conversation_tab, "💬 对话规则")

        # 个性化设置标签页
        personalization_tab = self.create_personalization_tab()
        tab_widget.addTab(personalization_tab, "🎭 个性化设置")

        # 升级规则标签页
        escalation_tab = self.create_escalation_tab()
        tab_widget.addTab(escalation_tab, "⬆️ 升级规则")

        # 系统设置标签页
        system_tab = self.create_system_tab()
        tab_widget.addTab(system_tab, "⚙️ 系统设置")

        layout.addWidget(tab_widget)

        # 设置滚动区域
        scroll.setWidget(main_widget)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(scroll)

    # 移除变量管理标签页 - 配置文件中没有variables配置

    def create_conversation_tab(self):
        """创建对话规则标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 说明信息
        help_label = QLabel("""
💡 对话规则配置说明:
• 设置AI对话的基本规则和限制
• 控制对话流程、话题范围、会话管理等
• 确保AI按照预期的方式与客户互动
        """)
        help_label.setStyleSheet("color: #666; font-size: 11px; background: #f5f5f5; padding: 10px; border-radius: 5px;")
        layout.addWidget(help_label)

        # 基本对话规则 - 使用紧凑布局
        basic_rules_group = QGroupBox("📋 基本对话规则")
        basic_rules_group.setMaximumHeight(400)  # 增加最大高度，容纳新增的提示消息字段
        basic_rules_layout = QFormLayout(basic_rules_group)
        basic_rules_layout.setVerticalSpacing(8)  # 减少垂直间距
        basic_rules_layout.setContentsMargins(8, 8, 8, 8)  # 紧凑边距

        # 保持话题相关性
        self.stay_on_topic_checkbox = QCheckBox("保持话题相关性")
        self.stay_on_topic_checkbox.setChecked(True)
        self.stay_on_topic_checkbox.stateChanged.connect(self.on_config_changed)
        basic_rules_layout.addRow("话题控制:", self.stay_on_topic_checkbox)

        # 最大对话轮数
        self.max_turns_spin = QSpinBox()
        self.max_turns_spin.setRange(10, 100)
        self.max_turns_spin.setValue(50)
        self.max_turns_spin.valueChanged.connect(self.on_config_changed)
        basic_rules_layout.addRow("最大对话轮数:", self.max_turns_spin)

        # 会话超时时间
        self.session_timeout_spin = QSpinBox()
        self.session_timeout_spin.setRange(5, 120)
        self.session_timeout_spin.setValue(30)
        self.session_timeout_spin.setSuffix(" 分钟")
        self.session_timeout_spin.valueChanged.connect(self.on_config_changed)
        basic_rules_layout.addRow("会话超时:", self.session_timeout_spin)

        # 偏题重定向话术 - 使用QGroupBox，但高度紧凑
        off_topic_group = QGroupBox("🔄 偏题重定向")
        off_topic_group.setMinimumHeight(80)  # 紧凑高度
        off_topic_layout = QVBoxLayout(off_topic_group)
        off_topic_layout.setContentsMargins(6, 4, 6, 4)  # 更紧凑的边距
        off_topic_layout.setSpacing(2)

        self.off_topic_redirect_edit = QTextEdit()
        self.off_topic_redirect_edit.setMaximumHeight(50)  # 减少高度
        self.off_topic_redirect_edit.setPlaceholderText("I focus on providing air quality solution consultations. Let's get back to your air purification needs...")
        self.off_topic_redirect_edit.textChanged.connect(self.on_config_changed)
        off_topic_layout.addWidget(self.off_topic_redirect_edit)

        basic_rules_layout.addRow(off_topic_group)

        # 轮数限制提示消息 - 使用QGroupBox
        max_turns_msg_group = QGroupBox("📊 轮数限制提示消息")
        max_turns_msg_group.setMinimumHeight(80)  # 紧凑高度
        max_turns_msg_layout = QVBoxLayout(max_turns_msg_group)
        max_turns_msg_layout.setContentsMargins(6, 4, 6, 4)  # 更紧凑的边距
        max_turns_msg_layout.setSpacing(2)

        self.max_turns_message_edit = QTextEdit()
        self.max_turns_message_edit.setMaximumHeight(50)  # 减少高度
        self.max_turns_message_edit.setPlaceholderText("This conversation has reached the maximum limit of {max_turns} turns. Please refresh the page...")
        self.max_turns_message_edit.textChanged.connect(self.on_config_changed)
        max_turns_msg_layout.addWidget(self.max_turns_message_edit)

        basic_rules_layout.addRow(max_turns_msg_group)

        # 超时提示消息 - 使用QGroupBox
        timeout_msg_group = QGroupBox("⏰ 超时提示消息")
        timeout_msg_group.setMinimumHeight(80)  # 紧凑高度
        timeout_msg_layout = QVBoxLayout(timeout_msg_group)
        timeout_msg_layout.setContentsMargins(6, 4, 6, 4)  # 更紧凑的边距
        timeout_msg_layout.setSpacing(2)

        self.timeout_message_edit = QTextEdit()
        self.timeout_message_edit.setMaximumHeight(50)  # 减少高度
        self.timeout_message_edit.setPlaceholderText("This conversation session has timed out after {timeout_minutes} minutes. Please refresh the page...")
        self.timeout_message_edit.textChanged.connect(self.on_config_changed)
        timeout_msg_layout.addWidget(self.timeout_message_edit)

        basic_rules_layout.addRow(timeout_msg_group)

        layout.addWidget(basic_rules_group)

        # 话题关键词管理 - 调整区域大小
        keywords_group = QGroupBox("🎯 话题关键词管理")
        keywords_group.setMinimumHeight(300)  # 调整最小高度，为基本对话规则让出空间
        keywords_layout = QVBoxLayout(keywords_group)
        keywords_layout.setContentsMargins(8, 8, 8, 8)
        keywords_layout.setSpacing(5)

        # 创建标签页
        keywords_tab = QTabWidget()

        # 允许的话题 - 使用优化的布局
        allowed_widget = QWidget()
        allowed_layout = QVBoxLayout(allowed_widget)
        allowed_layout.setContentsMargins(8, 8, 8, 8)
        allowed_layout.setSpacing(5)

        # 添加帮助信息
        allowed_help = QLabel("💡 配置AI可以讨论的话题关键词")
        allowed_help.setStyleSheet("color: #666; font-size: 11px;")
        allowed_layout.addWidget(allowed_help)

        # 列表自动填充空间
        self.allowed_topics_list = QListWidget()
        allowed_layout.addWidget(self.allowed_topics_list, 1)  # stretch factor = 1

        # 按钮布局 - 右对齐，紧凑排列
        allowed_btn_layout = QHBoxLayout()
        allowed_btn_layout.setContentsMargins(0, 5, 0, 0)
        allowed_btn_layout.addStretch()  # 左侧弹性空间，让按钮右对齐

        self.add_allowed_btn = QPushButton("➕ 添加")
        self.add_allowed_btn.setMaximumWidth(80)  # 限制按钮宽度
        self.add_allowed_btn.clicked.connect(lambda: self.add_topic_keyword("allowed"))
        allowed_btn_layout.addWidget(self.add_allowed_btn)

        self.remove_allowed_btn = QPushButton("➖ 删除")
        self.remove_allowed_btn.setMaximumWidth(80)  # 限制按钮宽度
        self.remove_allowed_btn.clicked.connect(lambda: self.remove_topic_keyword("allowed"))
        allowed_btn_layout.addWidget(self.remove_allowed_btn)

        allowed_layout.addLayout(allowed_btn_layout)

        keywords_tab.addTab(allowed_widget, "✅ 允许话题")

        # 禁止的话题 - 使用优化的布局
        forbidden_widget = QWidget()
        forbidden_layout = QVBoxLayout(forbidden_widget)
        forbidden_layout.setContentsMargins(8, 8, 8, 8)
        forbidden_layout.setSpacing(5)

        # 添加帮助信息
        forbidden_help = QLabel("💡 配置AI应该避免的话题关键词")
        forbidden_help.setStyleSheet("color: #666; font-size: 11px;")
        forbidden_layout.addWidget(forbidden_help)

        # 列表自动填充空间
        self.forbidden_topics_list = QListWidget()
        forbidden_layout.addWidget(self.forbidden_topics_list, 1)  # stretch factor = 1

        # 按钮布局 - 右对齐，紧凑排列
        forbidden_btn_layout = QHBoxLayout()
        forbidden_btn_layout.setContentsMargins(0, 5, 0, 0)
        forbidden_btn_layout.addStretch()  # 左侧弹性空间，让按钮右对齐

        self.add_forbidden_btn = QPushButton("➕ 添加")
        self.add_forbidden_btn.setMaximumWidth(80)  # 限制按钮宽度
        self.add_forbidden_btn.clicked.connect(lambda: self.add_topic_keyword("forbidden"))
        forbidden_btn_layout.addWidget(self.add_forbidden_btn)

        self.remove_forbidden_btn = QPushButton("➖ 删除")
        self.remove_forbidden_btn.setMaximumWidth(80)  # 限制按钮宽度
        self.remove_forbidden_btn.clicked.connect(lambda: self.remove_topic_keyword("forbidden"))
        forbidden_btn_layout.addWidget(self.remove_forbidden_btn)

        forbidden_layout.addLayout(forbidden_btn_layout)

        keywords_tab.addTab(forbidden_widget, "❌ 禁止话题")

        keywords_layout.addWidget(keywords_tab)
        layout.addWidget(keywords_group)

        # 话题关键词将在load_config时从配置文件加载
        # 移除硬编码加载

        return widget

    def create_personalization_tab(self):
        """创建个性化设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 说明信息
        help_label = QLabel("""
💡 个性化设置说明:
• 配置AI如何根据不同客户类型调整对话策略
• 设置客户细分规则和相应的销售方法
• 提高转化率和客户满意度
        """)
        help_label.setStyleSheet("color: #666; font-size: 11px; background: #f5f5f5; padding: 10px; border-radius: 5px;")
        layout.addWidget(help_label)

        # 客户细分配置
        segmentation_group = QGroupBox("👥 客户细分配置")
        segmentation_layout = QVBoxLayout(segmentation_group)

        # 创建分割器
        seg_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：客户类型列表
        types_group = QGroupBox("📋 客户类型")
        types_layout = QVBoxLayout(types_group)

        self.customer_types_list = QListWidget()
        self.customer_types_list.currentItemChanged.connect(self.on_customer_type_selected)
        types_layout.addWidget(self.customer_types_list)

        types_btn_layout = QHBoxLayout()
        self.add_type_btn = QPushButton("➕ 添加类型")
        self.add_type_btn.clicked.connect(self.add_customer_type)
        types_btn_layout.addWidget(self.add_type_btn)

        self.remove_type_btn = QPushButton("➖ 删除类型")
        self.remove_type_btn.clicked.connect(self.remove_customer_type)
        types_btn_layout.addWidget(self.remove_type_btn)

        types_btn_layout.addStretch()
        types_layout.addLayout(types_btn_layout)

        seg_splitter.addWidget(types_group)

        # 右侧：类型详细配置
        type_detail_group = QGroupBox("⚙️ 类型详细配置")
        type_detail_layout = QFormLayout(type_detail_group)

        # 类型名称 - 使用QGroupBox
        type_name_group = QGroupBox("🏷️ 类型名称")
        type_name_group.setMinimumHeight(80)  # 设置最小高度
        type_name_layout = QVBoxLayout(type_name_group)
        type_name_layout.setContentsMargins(8, 8, 8, 8)
        type_name_layout.setSpacing(5)

        # 类型名称编辑器填满容器
        self.type_name_edit = QLineEdit()
        self.type_name_edit.setPlaceholderText("输入客户类型名称，如：价格敏感型、品质追求型等")
        self.type_name_edit.textChanged.connect(self.on_customer_type_data_changed)  # 🔧 修改为专门的客户类型数据变更信号
        type_name_layout.addWidget(self.type_name_edit, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        type_detail_layout.addRow(type_name_group)

        # 特征描述 - 使用QGroupBox
        characteristics_group = QGroupBox("📝 特征描述")
        characteristics_group.setMinimumHeight(120)  # 设置最小高度
        characteristics_layout = QVBoxLayout(characteristics_group)
        characteristics_layout.setContentsMargins(8, 8, 8, 8)
        characteristics_layout.setSpacing(5)

        # 特征描述编辑器填满容器
        self.type_characteristics_edit = QTextEdit()
        self.type_characteristics_edit.setPlaceholderText("描述该客户类型的主要特征和行为模式")
        # 移除最大高度限制，让编辑器自动填充可用空间
        self.type_characteristics_edit.textChanged.connect(self.on_customer_type_data_changed)  # 🔧 修改为专门的客户类型数据变更信号
        characteristics_layout.addWidget(self.type_characteristics_edit, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        type_detail_layout.addRow(characteristics_group)

        # 识别关键词 - 使用QGroupBox，编辑框填满容器，按钮在右下角
        keywords_group = QGroupBox("🔍 识别关键词")
        keywords_group.setMinimumHeight(160)  # 设置最小高度
        keywords_main_layout = QVBoxLayout(keywords_group)
        keywords_main_layout.setContentsMargins(8, 8, 8, 8)
        keywords_main_layout.setSpacing(5)

        # 使用相对布局让列表填满大部分空间
        self.type_keywords_list = QListWidget()
        # 移除最大高度限制，让列表自动填充可用空间
        keywords_main_layout.addWidget(self.type_keywords_list, 1)  # stretch factor = 1

        # 按钮布局 - 右对齐，紧凑排列
        type_keywords_btn_layout = QHBoxLayout()
        type_keywords_btn_layout.setContentsMargins(0, 5, 0, 0)  # 顶部留一点间距
        type_keywords_btn_layout.addStretch()  # 左侧弹性空间，让按钮右对齐

        self.add_type_keyword_btn = QPushButton("➕ 添加")
        self.add_type_keyword_btn.setMaximumWidth(80)  # 限制按钮宽度，更紧凑
        self.add_type_keyword_btn.clicked.connect(self.add_type_keyword)
        type_keywords_btn_layout.addWidget(self.add_type_keyword_btn)

        self.remove_type_keyword_btn = QPushButton("➖ 删除")
        self.remove_type_keyword_btn.setMaximumWidth(80)  # 限制按钮宽度，更紧凑
        self.remove_type_keyword_btn.clicked.connect(self.remove_type_keyword)
        type_keywords_btn_layout.addWidget(self.remove_type_keyword_btn)

        keywords_main_layout.addLayout(type_keywords_btn_layout)

        # 直接添加QGroupBox，不需要标签
        type_detail_layout.addRow(keywords_group)

        # 销售策略 - 使用QGroupBox
        sales_approach_group = QGroupBox("🎯 销售策略")
        sales_approach_group.setMinimumHeight(100)  # 设置最小高度
        sales_approach_layout = QVBoxLayout(sales_approach_group)
        sales_approach_layout.setContentsMargins(8, 8, 8, 8)
        sales_approach_layout.setSpacing(5)

        # 销售策略编辑器填满容器
        self.sales_approach_edit = QTextEdit()
        self.sales_approach_edit.setPlaceholderText("描述针对该客户类型的销售策略和方法")
        # 使用QTextEdit替代QLineEdit，提供更大的编辑空间
        self.sales_approach_edit.textChanged.connect(self.on_customer_type_data_changed)  # 🔧 修改为专门的客户类型数据变更信号
        sales_approach_layout.addWidget(self.sales_approach_edit, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        type_detail_layout.addRow(sales_approach_group)

        # 推荐产品 - 使用QGroupBox
        products_group = QGroupBox("🛍️ 推荐产品")
        products_layout = QVBoxLayout(products_group)
        products_layout.setContentsMargins(8, 8, 8, 8)
        products_layout.setSpacing(5)

        self.recommended_products_list = QListWidget()
        self.recommended_products_list.setMaximumHeight(100)  # 增加高度
        self.recommended_products_list.itemChanged.connect(self.on_customer_type_data_changed)  # 🔧 添加信号连接
        products_layout.addWidget(self.recommended_products_list)

        products_btn_layout = QHBoxLayout()
        products_btn_layout.setContentsMargins(0, 0, 0, 0)
        self.add_product_btn = QPushButton("➕ 添加产品")
        self.add_product_btn.clicked.connect(self.add_recommended_product)
        products_btn_layout.addWidget(self.add_product_btn)

        self.remove_product_btn = QPushButton("➖ 删除产品")
        self.remove_product_btn.clicked.connect(self.remove_recommended_product)
        products_btn_layout.addWidget(self.remove_product_btn)

        products_btn_layout.addStretch()
        products_layout.addLayout(products_btn_layout)

        type_detail_layout.addRow(products_group)

        # 说服要点 - 使用QGroupBox
        persuasion_group = QGroupBox("💡 说服要点")
        persuasion_layout = QVBoxLayout(persuasion_group)
        persuasion_layout.setContentsMargins(8, 8, 8, 8)
        persuasion_layout.setSpacing(5)

        self.persuasion_points_list = QListWidget()
        self.persuasion_points_list.setMaximumHeight(100)  # 增加高度
        self.persuasion_points_list.itemChanged.connect(self.on_customer_type_data_changed)  # 🔧 添加信号连接
        persuasion_layout.addWidget(self.persuasion_points_list)

        persuasion_btn_layout = QHBoxLayout()
        persuasion_btn_layout.setContentsMargins(0, 0, 0, 0)
        self.add_persuasion_btn = QPushButton("➕ 添加要点")
        self.add_persuasion_btn.clicked.connect(self.add_persuasion_point)
        persuasion_btn_layout.addWidget(self.add_persuasion_btn)

        self.remove_persuasion_btn = QPushButton("➖ 删除要点")
        self.remove_persuasion_btn.clicked.connect(self.remove_persuasion_point)
        persuasion_btn_layout.addWidget(self.remove_persuasion_btn)

        persuasion_btn_layout.addStretch()
        persuasion_layout.addLayout(persuasion_btn_layout)

        type_detail_layout.addRow(persuasion_group)

        seg_splitter.addWidget(type_detail_group)
        seg_splitter.setSizes([300, 700])

        segmentation_layout.addWidget(seg_splitter)
        layout.addWidget(segmentation_group)

        # 加载默认客户类型
        self.load_default_customer_types()

        return widget

    def create_system_tab(self):
        """创建系统设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 说明信息
        help_label = QLabel("""
💡 系统设置说明:
• 配置系统级别的参数和行为
• 包括日志记录、性能优化、调试选项等
• 这些设置影响整个AI系统的运行
        """)
        help_label.setStyleSheet("color: #666; font-size: 11px; background: #f5f5f5; padding: 10px; border-radius: 5px;")
        layout.addWidget(help_label)

        # 系统行为设置
        behavior_group = QGroupBox("⚙️ 系统行为设置")
        behavior_layout = QFormLayout(behavior_group)

        # 移除配置文件中不存在的系统设置开关

        # 自动保存间隔
        self.auto_save_interval_spin = QSpinBox()
        self.auto_save_interval_spin.setRange(30, 600)
        self.auto_save_interval_spin.setValue(300)
        self.auto_save_interval_spin.setSuffix(" 秒")
        self.auto_save_interval_spin.valueChanged.connect(self.on_config_changed)
        behavior_layout.addRow("自动保存间隔:", self.auto_save_interval_spin)

        layout.addWidget(behavior_group)

        # 缓存设置
        cache_group = QGroupBox("💾 缓存设置")
        cache_layout = QFormLayout(cache_group)

        # 移除缓存设置开关 - 配置文件中不存在

        # 缓存大小
        self.cache_size_spin = QSpinBox()
        self.cache_size_spin.setRange(100, 10000)
        self.cache_size_spin.setValue(1000)
        self.cache_size_spin.setSuffix(" 条")
        self.cache_size_spin.valueChanged.connect(self.on_config_changed)
        cache_layout.addRow("缓存大小:", self.cache_size_spin)

        # 缓存过期时间
        self.cache_expiry_spin = QSpinBox()
        self.cache_expiry_spin.setRange(1, 24)
        self.cache_expiry_spin.setValue(6)
        self.cache_expiry_spin.setSuffix(" 小时")
        self.cache_expiry_spin.valueChanged.connect(self.on_config_changed)
        cache_layout.addRow("缓存过期:", self.cache_expiry_spin)

        layout.addWidget(cache_group)

        layout.addStretch()

        return widget

    # 移除变量管理相关方法

    def load_default_topics(self):
        """从配置文件加载话题关键词"""
        # 移除硬编码，话题关键词现在完全从配置文件加载
        # 在load_config方法中处理
        pass

    def load_default_customer_types(self):
        """从配置文件加载客户类型"""
        # 移除硬编码，客户类型现在完全从配置文件加载
        # 在load_config方法中处理
        # 客户类型数据现在从配置文件加载，不使用硬编码
        pass

    # 移除变量分类处理方法

    def on_customer_type_selected(self, current, previous):
        """选择客户类型时的处理"""
        try:
            print(f"🖱️ 用户切换客户类型")

            # 🔧 统一为Agent模式：只有在配置已加载完成后才保存之前的数据
            if previous and hasattr(self.parent(), 'current_config_loaded') and self.parent().current_config_loaded:
                print(f"  保存之前选中的客户类型数据: {previous.text()}")
                # 临时设置当前项为previous，以便update_current_customer_type能正确工作
                self.customer_types_list.setCurrentItem(previous)
                self.update_current_customer_type()
                # 恢复当前项
                self.customer_types_list.setCurrentItem(current)

            if current:
                print(f"🔄 当前选中项: {current.text()}")
                # 🔧 修复：从UserRole+1中读取完整数据
                segment_key = current.data(Qt.ItemDataRole.UserRole)
                type_data = current.data(Qt.ItemDataRole.UserRole + 1)
                print(f"  键值: {segment_key}")
                print(f"  数据: {type_data}")

                if type_data:
                    print(f"  加载客户类型数据: {type_data.get('name', 'Unknown')}")
                    self.type_name_edit.setText(type_data.get('name', ''))
                    characteristics = type_data.get('characteristics', '')
                    if isinstance(characteristics, list):
                        characteristics = '\n'.join(characteristics)
                    self.type_characteristics_edit.setPlainText(characteristics)
                    sales_approach = type_data.get('sales_approach', '')
                    if isinstance(sales_approach, list):
                        sales_approach = '\n'.join(sales_approach)
                    self.sales_approach_edit.setPlainText(sales_approach)

                    # 加载关键词
                    print(f"  加载关键词: {type_data.get('keywords', [])}")
                    self.type_keywords_list.clear()
                    for keyword in type_data.get('keywords', []):
                        self.type_keywords_list.addItem(keyword)

                    # 加载推荐产品
                    print(f"  加载推荐产品: {type_data.get('recommended_products', [])}")
                    self.recommended_products_list.clear()
                    for product in type_data.get('recommended_products', []):
                        self.recommended_products_list.addItem(product)

                    # 加载说服要点
                    print(f"  加载说服要点: {type_data.get('persuasion_points', [])}")
                    self.persuasion_points_list.clear()
                    for point in type_data.get('persuasion_points', []):
                        self.persuasion_points_list.addItem(point)

                    print(f"  ✅ 客户类型数据加载完成")
                else:
                    print(f"  ❌ 无法获取客户类型数据")
            else:
                print(f"  ❌ 没有选中的客户类型")
        except Exception as e:
            print(f"❌ 客户类型选择处理失败: {e}")
            import traceback
            traceback.print_exc()

    # 移除模板预览方法

    # 移除所有变量管理相关方法

    def add_topic_keyword(self, topic_type):
        """添加话题关键词"""
        text, ok = QInputDialog.getText(self, f"添加{topic_type}话题", "请输入关键词:")
        if ok and text:
            if topic_type == "allowed":
                self.allowed_topics_list.addItem(text)
            else:
                self.forbidden_topics_list.addItem(text)
            self.on_config_changed()

    def remove_topic_keyword(self, topic_type):
        """删除话题关键词"""
        if topic_type == "allowed":
            current_row = self.allowed_topics_list.currentRow()
            if current_row >= 0:
                self.allowed_topics_list.takeItem(current_row)
        else:
            current_row = self.forbidden_topics_list.currentRow()
            if current_row >= 0:
                self.forbidden_topics_list.takeItem(current_row)
        self.on_config_changed()

    def add_customer_type(self):
        """添加客户类型"""
        text, ok = QInputDialog.getText(self, "添加客户类型", "请输入客户类型名称:")
        if ok and text:
            type_data = {
                "name": text,
                "characteristics": "",
                "keywords": [],
                "sales_approach": "",
                "recommended_products": [],
                "persuasion_points": []
            }

            item = QListWidgetItem(text)
            # 🔧 修复：UserRole存储键值，UserRole+1存储完整数据
            type_key = text.lower().replace(' ', '_')
            item.setData(Qt.ItemDataRole.UserRole, type_key)  # 存储键值
            item.setData(Qt.ItemDataRole.UserRole + 1, type_data)  # 存储完整数据
            self.customer_types_list.addItem(item)
            self.customer_types_list.setCurrentItem(item)
            self.on_config_changed()

    def remove_customer_type(self):
        """删除客户类型"""
        current_row = self.customer_types_list.currentRow()
        if current_row >= 0:
            self.customer_types_list.takeItem(current_row)
            self.on_config_changed()

    def add_type_keyword(self):
        """添加类型关键词"""
        text, ok = QInputDialog.getText(self, "添加识别关键词", "请输入关键词:")
        if ok and text:
            self.type_keywords_list.addItem(text)
            self.update_current_customer_type()
            self.on_config_changed()

    def remove_type_keyword(self):
        """删除类型关键词"""
        current_row = self.type_keywords_list.currentRow()
        if current_row >= 0:
            self.type_keywords_list.takeItem(current_row)
            self.update_current_customer_type()
            self.on_config_changed()

    def add_recommended_product(self):
        """添加推荐产品"""
        text, ok = QInputDialog.getText(self, "添加推荐产品", "请输入产品类型:")
        if ok and text:
            self.recommended_products_list.addItem(text)
            self.update_current_customer_type()
            self.on_config_changed()

    def remove_recommended_product(self):
        """删除推荐产品"""
        current_row = self.recommended_products_list.currentRow()
        if current_row >= 0:
            self.recommended_products_list.takeItem(current_row)
            self.update_current_customer_type()
            self.on_config_changed()

    def add_persuasion_point(self):
        """添加说服要点"""
        text, ok = QInputDialog.getText(self, "添加说服要点", "请输入说服要点:")
        if ok and text:
            self.persuasion_points_list.addItem(text)
            self.update_current_customer_type()
            self.on_config_changed()

    def remove_persuasion_point(self):
        """删除说服要点"""
        current_row = self.persuasion_points_list.currentRow()
        if current_row >= 0:
            self.persuasion_points_list.takeItem(current_row)
            self.update_current_customer_type()
            self.on_config_changed()

    def update_current_customer_type(self):
        """更新当前选中的客户类型数据"""
        current_item = self.customer_types_list.currentItem()
        if current_item:
            print(f"🔧 更新客户类型数据")

            # 🔧 修复：直接从UI控件读取数据
            type_name = self.type_name_edit.text()
            type_characteristics = self.type_characteristics_edit.toPlainText()
            sales_approach = self.sales_approach_edit.toPlainText()

            # 收集关键词
            keywords = []
            for i in range(self.type_keywords_list.count()):
                keywords.append(self.type_keywords_list.item(i).text())

            # 收集推荐产品
            products = []
            for i in range(self.recommended_products_list.count()):
                products.append(self.recommended_products_list.item(i).text())

            # 收集说服要点
            points = []
            for i in range(self.persuasion_points_list.count()):
                points.append(self.persuasion_points_list.item(i).text())

            print(f"  客户类型信息: name='{type_name}', keywords={len(keywords)}, products={len(products)}, points={len(points)}")

            # 构建完整的客户类型数据
            updated_type_data = {
                'name': type_name,
                'characteristics': type_characteristics,
                'sales_approach': sales_approach,
                'keywords': keywords,
                'recommended_products': products,
                'persuasion_points': points
            }

            # 🔧 修复：同时更新UserRole数据和config_data
            # UserRole存储键值，UserRole+1存储完整数据
            current_item.setData(Qt.ItemDataRole.UserRole + 1, updated_type_data)

            # 更新config_data中的对应数据
            if not hasattr(self, 'config_data'):
                self.config_data = {}
            if 'personalization' not in self.config_data:
                self.config_data['personalization'] = {}
            if 'customer_segmentation' not in self.config_data['personalization']:
                self.config_data['personalization']['customer_segmentation'] = {}

            # 使用类型名称作为键
            type_key = type_name.lower().replace(' ', '_') if type_name else 'unknown_type'
            self.config_data['personalization']['customer_segmentation'][type_key] = updated_type_data.copy()

            print(f"  ✅ 客户类型数据已同步到UserRole和config_data")

            # 更新显示文本
            current_item.setText(type_name)

            # 🔧 移除信号发射，避免循环调用
            # 信号发射由调用者负责

    def update_current_customer_type_silent(self):
        """更新当前选中的客户类型数据（不触发信号）"""
        current_item = self.customer_types_list.currentItem()
        if current_item:
            # 直接从UI控件读取数据
            type_name = self.type_name_edit.text()
            type_characteristics = self.type_characteristics_edit.toPlainText()
            sales_approach = self.sales_approach_edit.toPlainText()

            # 收集关键词
            keywords = []
            for i in range(self.type_keywords_list.count()):
                keywords.append(self.type_keywords_list.item(i).text())

            # 收集推荐产品
            products = []
            for i in range(self.recommended_products_list.count()):
                products.append(self.recommended_products_list.item(i).text())

            # 收集说服要点
            points = []
            for i in range(self.persuasion_points_list.count()):
                points.append(self.persuasion_points_list.item(i).text())

            # 构建完整的客户类型数据
            updated_type_data = {
                'name': type_name,
                'characteristics': type_characteristics,
                'sales_approach': sales_approach,
                'keywords': keywords,
                'recommended_products': products,
                'persuasion_points': points
            }

            # 更新UserRole数据和config_data
            current_item.setData(Qt.ItemDataRole.UserRole + 1, updated_type_data)

            # 更新config_data中的对应数据
            if not hasattr(self, 'config_data'):
                self.config_data = {}
            if 'personalization' not in self.config_data:
                self.config_data['personalization'] = {}
            if 'customer_segmentation' not in self.config_data['personalization']:
                self.config_data['personalization']['customer_segmentation'] = {}

            # 使用类型名称作为键
            type_key = type_name.lower().replace(' ', '_') if type_name else 'unknown_type'
            self.config_data['personalization']['customer_segmentation'][type_key] = updated_type_data.copy()

            # 更新显示文本
            current_item.setText(type_name)

    def load_config(self, config_data):
        """加载配置数据"""
        self.config_data = config_data
        print(f"🔄 开始加载高级设置配置...")

        # 🔧 统一为Agent模式：直接加载，依赖current_config_loaded保护
        # 移除变量管理相关的加载

        # 加载升级规则配置
        escalation_rules = config_data.get('escalation_rules', {})
        advanced_support = config_data.get('advanced_support', {})

        if escalation_rules:
            # 人工接管设置已移除 - AI默认按照规则自动判断

            # 加载升级触发条件 - 合并escalation_rules和advanced_support
            escalation_triggers = escalation_rules.get('escalation_triggers', {})

            # 合并advanced_support中的categories作为升级触发条件
            if advanced_support and 'categories' in advanced_support:
                for category_key, category_info in advanced_support['categories'].items():
                    if isinstance(category_info, dict):
                        # 转换advanced_support格式到escalation_triggers格式
                        trigger_data = {
                            'enabled': True,  # advanced_support中的都默认启用
                            'keywords': category_info.get('keywords', []),
                            'action': 'form_submission',  # advanced_support都是表单提交
                            'trigger_message': category_info.get('trigger_message', ''),
                            'source': 'advanced_support'  # 标记来源
                        }
                        escalation_triggers[category_key] = trigger_data

            if escalation_triggers and hasattr(self, 'escalation_triggers_list'):
                self.escalation_triggers_list.clear()

                for trigger_key, trigger_info in escalation_triggers.items():
                    if isinstance(trigger_info, dict):
                        # 🔧 修复：优先使用保存的name字段，如果没有则使用key的友好显示名称
                        saved_name = trigger_info.get('name', '')
                        if saved_name:
                            display_name = saved_name
                        else:
                            display_name = trigger_key.replace('_', ' ').title()

                        item = QListWidgetItem(display_name)
                        item.setData(Qt.ItemDataRole.UserRole, trigger_key)
                        item.setData(Qt.ItemDataRole.UserRole + 1, trigger_info)
                        self.escalation_triggers_list.addItem(item)

                print(f"✅ 已加载 {len(escalation_triggers)} 个升级触发条件（包含高级售后支持）")

            print("✅ 已加载升级规则配置")

        # 加载对话规则配置
        conversation_rules = config_data.get('conversation_rules', {})
        if conversation_rules:
            # 基本设置
            stay_on_topic = conversation_rules.get('stay_on_topic', True)
            if hasattr(self, 'stay_on_topic_checkbox'):
                self.stay_on_topic_checkbox.setChecked(stay_on_topic)

            max_turns = conversation_rules.get('max_conversation_turns', 50)
            if hasattr(self, 'max_turns_spin'):
                self.max_turns_spin.setValue(max_turns)

            timeout_minutes = conversation_rules.get('session_timeout_minutes', 30)
            if hasattr(self, 'timeout_spin'):
                self.timeout_spin.setValue(timeout_minutes)

            off_topic_redirect = conversation_rules.get('off_topic_redirect', '')
            if hasattr(self, 'off_topic_redirect_edit'):
                self.off_topic_redirect_edit.setPlainText(off_topic_redirect)

            # 加载轮数限制提示消息
            max_turns_message = conversation_rules.get('max_turns_message',
                'This conversation has reached the maximum limit of {max_turns} turns. Please refresh the page to start a new conversation if you need further assistance.')
            if hasattr(self, 'max_turns_message_edit'):
                self.max_turns_message_edit.setPlainText(max_turns_message)

            # 加载超时提示消息
            timeout_message = conversation_rules.get('timeout_message',
                'This conversation session has timed out after {timeout_minutes} minutes. Please refresh the page to start a new conversation.')
            if hasattr(self, 'timeout_message_edit'):
                self.timeout_message_edit.setPlainText(timeout_message)

            # 加载话题关键词
            allowed_topics = conversation_rules.get('allowed_topics', [])
            if hasattr(self, 'allowed_topics_list'):
                self.allowed_topics_list.clear()
                for topic in allowed_topics:
                    self.allowed_topics_list.addItem(topic)

            forbidden_topics = conversation_rules.get('forbidden_topics', [])
            if hasattr(self, 'forbidden_topics_list'):
                self.forbidden_topics_list.clear()
                for topic in forbidden_topics:
                    self.forbidden_topics_list.addItem(topic)

            print("✅ 已加载对话规则配置")

        # 加载个性化配置
        print(f"🔄 开始加载个性化配置...")
        personalization = config_data.get('personalization', {})
        if personalization:
            print(f"📋 个性化配置数据: {personalization}")
            # 客户细分
            customer_segmentation = personalization.get('customer_segmentation', {})
            print(f"📋 客户细分数据: {customer_segmentation}")
            if hasattr(self, 'customer_types_list'):
                print(f"🔄 清空客户类型列表...")
                self.customer_types_list.clear()

                for segment_key, segment_info in customer_segmentation.items():
                    try:
                        print(f"🔄 处理客户类型: {segment_key} -> {segment_info}")
                        if isinstance(segment_info, dict):
                            # 🔧 修复：正确处理数据格式，避免重新加载时闪退
                            display_name = segment_info.get('name', segment_key.replace('_', ' ').title())
                            print(f"  显示名称: {display_name}")

                            # 确保characteristics是字符串格式
                            characteristics = segment_info.get('characteristics', '')
                            if isinstance(characteristics, list):
                                characteristics = ', '.join(characteristics)
                            print(f"  特征描述: {characteristics}")

                            # 构建GUI格式数据
                            gui_format = {
                                'name': display_name,
                                'characteristics': characteristics,
                                'keywords': segment_info.get('keywords', []) if isinstance(segment_info.get('keywords'), list) else [],
                                'sales_approach': segment_info.get('sales_approach', ''),
                                'recommended_products': segment_info.get('recommended_products', []) if isinstance(segment_info.get('recommended_products'), list) else [],
                                'persuasion_points': segment_info.get('persuasion_points', []) if isinstance(segment_info.get('persuasion_points'), list) else []
                            }
                            print(f"  GUI格式数据: {gui_format}")

                            print(f"🔄 创建列表项...")
                            item = QListWidgetItem(display_name)
                            # 🔧 修复：UserRole存储键值，UserRole+1存储完整数据
                            item.setData(Qt.ItemDataRole.UserRole, segment_key)  # 存储键值
                            item.setData(Qt.ItemDataRole.UserRole + 1, gui_format)  # 存储完整数据
                            self.customer_types_list.addItem(item)
                            print(f"  ✅ 客户类型 {segment_key} 加载成功")

                    except Exception as e:
                        print(f"❌ 加载客户类型 {segment_key} 失败: {e}")
                        import traceback
                        traceback.print_exc()
                        continue

                print(f"✅ 已加载 {len(customer_segmentation)} 个客户细分类型")

        # 注意：购买渠道配置已移至产品管理模块

        print("✅ 高级设置配置加载完成")

    # 移除变量更新方法

    def get_config(self):
        """获取当前配置"""
        # 强制让所有控件失去焦点，确保内容被提交
        self.setFocus()

        # 保存当前编辑的数据
        self.update_current_trigger_data()
        self.update_current_customer_type()  # 🔧 修复：保存客户类型数据

        config = {}

        # 收集升级规则配置
        escalation_rules = {}
        # 人工接管设置已移除 - AI默认按照规则自动判断

        # 收集升级触发条件
        escalation_triggers = {}
        advanced_support_categories = {}

        if hasattr(self, 'escalation_triggers_list'):
            for i in range(self.escalation_triggers_list.count()):
                item = self.escalation_triggers_list.item(i)
                trigger_key = item.data(Qt.ItemDataRole.UserRole)
                trigger_data = item.data(Qt.ItemDataRole.UserRole + 1)

                if trigger_data and trigger_key:
                    # 🔧 修复：直接使用UI中的最新数据，不再进行数据对比
                    # 因为update_current_trigger_data()已经确保了数据同步

                    # 根据来源分别保存
                    if trigger_data.get('source') == 'advanced_support':
                        # 保存到advanced_support格式
                        advanced_support_categories[trigger_key] = {
                            'keywords': trigger_data.get('keywords', []),
                            'trigger_message': trigger_data.get('trigger_message', '')
                        }
                    else:
                        # 🔧 修复：保存到escalation_rules格式，包含所有字段
                        escalation_triggers[trigger_key] = {
                            'keywords': trigger_data.get('keywords', []),
                            'action': trigger_data.get('action', 'notify_supervisor'),
                            'form_prompt_message': trigger_data.get('form_prompt_message', '')
                        }

        if escalation_triggers:
            escalation_rules['escalation_triggers'] = escalation_triggers

        if escalation_rules:
            config['escalation_rules'] = escalation_rules

        # 保存advanced_support配置
        if advanced_support_categories:
            config['advanced_support'] = {
                'enabled': True,
                'require_form': True,
                'categories': advanced_support_categories
            }

        # 收集对话规则配置
        conversation_rules = {}
        if hasattr(self, 'stay_on_topic_checkbox'):
            conversation_rules['stay_on_topic'] = self.stay_on_topic_checkbox.isChecked()
        if hasattr(self, 'max_turns_spin'):
            conversation_rules['max_conversation_turns'] = self.max_turns_spin.value()
        if hasattr(self, 'timeout_spin'):
            conversation_rules['session_timeout_minutes'] = self.timeout_spin.value()
        if hasattr(self, 'off_topic_redirect_edit'):
            conversation_rules['off_topic_redirect'] = self.off_topic_redirect_edit.toPlainText()

        # 保存轮数限制提示消息
        if hasattr(self, 'max_turns_message_edit'):
            conversation_rules['max_turns_message'] = self.max_turns_message_edit.toPlainText()

        # 保存超时提示消息
        if hasattr(self, 'timeout_message_edit'):
            conversation_rules['timeout_message'] = self.timeout_message_edit.toPlainText()

        # 收集话题关键词
        if hasattr(self, 'allowed_topics_list'):
            allowed_topics = []
            for i in range(self.allowed_topics_list.count()):
                allowed_topics.append(self.allowed_topics_list.item(i).text())
            conversation_rules['allowed_topics'] = allowed_topics

        if hasattr(self, 'forbidden_topics_list'):
            forbidden_topics = []
            for i in range(self.forbidden_topics_list.count()):
                forbidden_topics.append(self.forbidden_topics_list.item(i).text())
            conversation_rules['forbidden_topics'] = forbidden_topics

        if conversation_rules:
            config['conversation_rules'] = conversation_rules

        # 收集个性化配置
        print(f"🔄 开始收集个性化配置...")
        personalization = {}
        customer_segmentation = {}

        if hasattr(self, 'customer_types_list'):
            print(f"📋 客户类型列表项数: {self.customer_types_list.count()}")
            for i in range(self.customer_types_list.count()):
                item = self.customer_types_list.item(i)
                # 🔧 修复：从UserRole读取键值，从UserRole+1读取完整数据
                segment_key = item.data(Qt.ItemDataRole.UserRole)  # 键值
                segment_data = item.data(Qt.ItemDataRole.UserRole + 1)  # 完整数据
                print(f"🔄 处理客户类型 {i}: 键值={segment_key}, 数据={segment_data}")

                if segment_data and segment_key:
                    # 转换GUI格式回配置文件格式
                    config_format = {
                        'name': segment_data.get('name', ''),  # 添加名称字段
                        'characteristics': segment_data.get('characteristics', ''),  # 🔧 修复：特征描述应该是字符串，不是列表
                        'keywords': segment_data.get('keywords', []) if isinstance(segment_data.get('keywords'), list) else [],  # 🔧 修复：添加关键词字段
                        'sales_approach': segment_data.get('sales_approach', ''),
                        'recommended_products': segment_data.get('recommended_products', []) if isinstance(segment_data.get('recommended_products'), list) else [],
                        'persuasion_points': segment_data.get('persuasion_points', []) if isinstance(segment_data.get('persuasion_points'), list) else []
                    }
                    print(f"  转换后的配置格式: {config_format}")

                    customer_segmentation[segment_key] = config_format
                    print(f"  ✅ 客户类型 {segment_key} 配置收集完成")
                else:
                    print(f"  ❌ 客户类型 {i} 数据不完整: 键值={segment_key}, 数据={segment_data}")

        if customer_segmentation:
            personalization['customer_segmentation'] = customer_segmentation
            print(f"📋 最终客户细分配置: {customer_segmentation}")

        if personalization:
            config['personalization'] = personalization
            print(f"📋 最终个性化配置: {personalization}")

        # 注意：购买渠道配置已移至产品管理模块
        print(f"📋 最终高级设置配置: {config}")

        return config

    def create_escalation_tab(self):
        """创建升级规则标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 说明信息
        help_label = QLabel("""
💡 升级规则配置说明:
• AI默认按照配置的规则自动判断是否需要升级到高级售后支持
• 配置具体的升级触发条件：关键词、动作类型等
• 客服人员可以随时手动接管AI对话，无需额外配置
        """)
        help_label.setStyleSheet("color: #666; font-size: 11px; background: #f5f5f5; padding: 5px; border-radius: 5px;")
        help_label.setMaximumHeight(80)  # 进一步减少高度，使其更紧凑
        layout.addWidget(help_label)

        # 售后问题升级规则
        triggers_group = QGroupBox("🎯 售后问题升级规则")
        triggers_layout = QVBoxLayout(triggers_group)

        # 说明
        triggers_help = QLabel("配置什么情况下从基础售后升级到高级售后（表单提交）")
        triggers_help.setStyleSheet("color: #666; font-size: 10px; margin-bottom: 5px;")
        triggers_help.setMaximumHeight(25)  # 限制最大高度，使其更紧凑
        triggers_layout.addWidget(triggers_help)

        # 创建分割器
        triggers_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：升级规则类型列表
        triggers_list_group = QGroupBox("升级规则类型")
        triggers_list_layout = QVBoxLayout(triggers_list_group)

        self.escalation_triggers_list = QListWidget()
        self.escalation_triggers_list.currentItemChanged.connect(self.on_escalation_trigger_selected)
        triggers_list_layout.addWidget(self.escalation_triggers_list)

        # 升级规则操作按钮
        triggers_btn_layout = QHBoxLayout()
        self.add_trigger_btn = QPushButton("➕ 添加规则")
        self.add_trigger_btn.clicked.connect(self.add_escalation_trigger)
        triggers_btn_layout.addWidget(self.add_trigger_btn)

        self.remove_trigger_btn = QPushButton("➖ 删除规则")
        self.remove_trigger_btn.clicked.connect(self.remove_escalation_trigger)
        triggers_btn_layout.addWidget(self.remove_trigger_btn)

        triggers_btn_layout.addStretch()
        triggers_list_layout.addLayout(triggers_btn_layout)

        triggers_splitter.addWidget(triggers_list_group)

        # 右侧：升级规则详细配置
        trigger_detail_group = QGroupBox("升级规则详细配置")
        trigger_detail_layout = QFormLayout(trigger_detail_group)
        trigger_detail_layout.setLabelAlignment(Qt.AlignmentFlag.AlignTop)  # 标签顶部对齐
        trigger_detail_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        trigger_detail_layout.setVerticalSpacing(10)  # 设置更紧凑的垂直间距

        # 规则名称 - 使用QGroupBox，超紧凑布局
        trigger_name_group = QGroupBox("🏷️ 规则名称")
        trigger_name_group.setMinimumHeight(60)  # 进一步减少高度
        trigger_name_group.setMaximumHeight(60)  # 限制最大高度
        trigger_name_layout = QVBoxLayout(trigger_name_group)
        trigger_name_layout.setContentsMargins(6, 4, 6, 4)  # 超紧凑的边距
        trigger_name_layout.setSpacing(2)  # 最小间距

        # 规则名称编辑器填满容器
        self.trigger_name_edit = QLineEdit()
        self.trigger_name_edit.setPlaceholderText("输入升级规则名称，如：技术问题升级、复杂咨询升级等")
        self.trigger_name_edit.textChanged.connect(self.on_trigger_data_changed)
        trigger_name_layout.addWidget(self.trigger_name_edit, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        trigger_detail_layout.addRow(trigger_name_group)

        # 升级动作 - 使用QGroupBox，超紧凑布局
        trigger_action_group = QGroupBox("⚡ 升级动作")
        trigger_action_group.setMinimumHeight(60)  # 进一步减少高度
        trigger_action_group.setMaximumHeight(60)  # 限制最大高度
        trigger_action_layout = QVBoxLayout(trigger_action_group)
        trigger_action_layout.setContentsMargins(6, 4, 6, 4)  # 超紧凑的边距
        trigger_action_layout.setSpacing(2)  # 最小间距

        # 升级动作选择器填满容器
        self.trigger_action_combo = QComboBox()
        self.trigger_action_combo.addItems(["form_submission", "immediate_escalation", "notify_customer_service"])
        self.trigger_action_combo.currentTextChanged.connect(self.on_trigger_data_changed)
        trigger_action_layout.addWidget(self.trigger_action_combo, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        trigger_detail_layout.addRow(trigger_action_group)

        # 表单提示消息 - 使用QGroupBox
        form_prompt_group = QGroupBox("💬 表单提示消息")
        form_prompt_group.setMinimumHeight(100)  # 设置最小高度
        form_prompt_layout = QVBoxLayout(form_prompt_group)
        form_prompt_layout.setContentsMargins(8, 8, 8, 8)
        form_prompt_layout.setSpacing(5)

        # 表单提示消息编辑器填满容器
        self.form_prompt_message_edit = QTextEdit()
        self.form_prompt_message_edit.setPlaceholderText("It looks like you need advanced support. Please fill out this form so our technical team can assist you better.")
        # 移除最大高度限制，让编辑器自动填充可用空间
        self.form_prompt_message_edit.textChanged.connect(self.on_trigger_data_changed)
        form_prompt_layout.addWidget(self.form_prompt_message_edit, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        trigger_detail_layout.addRow(form_prompt_group)

        # 触发关键词 - 使用QGroupBox，编辑框填满容器，按钮在右下角
        keywords_group = QGroupBox("🔑 触发关键词")
        keywords_group.setMinimumHeight(180)  # 设置最小高度
        keywords_main_layout = QVBoxLayout(keywords_group)
        keywords_main_layout.setContentsMargins(8, 8, 8, 8)
        keywords_main_layout.setSpacing(5)

        # 使用相对布局让列表填满大部分空间
        self.trigger_keywords_list = QListWidget()
        # 移除最大高度限制，让列表自动填充可用空间
        keywords_main_layout.addWidget(self.trigger_keywords_list, 1)  # stretch factor = 1

        # 按钮布局 - 右对齐，紧凑排列
        keywords_btn_layout = QHBoxLayout()
        keywords_btn_layout.setContentsMargins(0, 5, 0, 0)  # 顶部留一点间距
        keywords_btn_layout.addStretch()  # 左侧弹性空间，让按钮右对齐

        self.add_keyword_btn = QPushButton("➕ 添加")
        self.add_keyword_btn.setMaximumWidth(80)  # 限制按钮宽度，更紧凑
        self.add_keyword_btn.clicked.connect(self.add_trigger_keyword)
        keywords_btn_layout.addWidget(self.add_keyword_btn)

        self.remove_keyword_btn = QPushButton("➖ 删除")
        self.remove_keyword_btn.setMaximumWidth(80)  # 限制按钮宽度，更紧凑
        self.remove_keyword_btn.clicked.connect(self.remove_trigger_keyword)
        keywords_btn_layout.addWidget(self.remove_keyword_btn)

        keywords_main_layout.addLayout(keywords_btn_layout)

        # 直接添加QGroupBox，不需要标签
        trigger_detail_layout.addRow(keywords_group)

        triggers_splitter.addWidget(trigger_detail_group)
        triggers_splitter.setSizes([250, 450])  # 调整比例，给右侧更多空间

        triggers_layout.addWidget(triggers_splitter)
        layout.addWidget(triggers_group)

        # 升级触发条件将在load_config时从配置文件加载
        # 移除硬编码的默认数据

        return widget
    def load_default_escalation_triggers(self):
        """从配置文件加载升级触发条件（移除硬编码）"""
        # 升级触发条件现在完全从配置文件加载
        # 在load_config方法中处理，不使用硬编码的默认数据
        pass

    def on_escalation_trigger_selected(self, current, previous):
        """选择升级触发条件时的处理"""
        try:
            print(f"🖱️ 用户切换升级触发条件")

            # 🔧 统一为Agent模式：只有在配置已加载完成后才保存之前的数据
            if previous and hasattr(self.parent(), 'current_config_loaded') and self.parent().current_config_loaded:
                print(f"  保存之前选中的触发条件数据: {previous.text()}")
                # 临时设置当前项为previous，以便update_current_trigger_data能正确工作
                self.escalation_triggers_list.setCurrentItem(previous)
                self.update_current_trigger_data()
                # 恢复当前项
                self.escalation_triggers_list.setCurrentItem(current)

            if current:
                print(f"🔄 当前选中项: {current.text()}")
                trigger_key = current.data(Qt.ItemDataRole.UserRole)
                trigger_data = current.data(Qt.ItemDataRole.UserRole + 1)
                print(f"  键值: {trigger_key}")
                print(f"  数据: {trigger_data}")

                if trigger_data:
                    # 🔧 修复：优先使用保存的name字段，如果没有则使用key的友好显示名称
                    saved_name = trigger_data.get('name', '')
                    if saved_name:
                        display_name = saved_name
                    else:
                        display_name = trigger_key.replace('_', ' ').title()
                    self.trigger_name_edit.setText(display_name)
                    print(f"  加载触发条件名称: {display_name}")

                    # 设置升级动作
                    action = trigger_data.get('action', 'form_submission')
                    index = self.trigger_action_combo.findText(action)
                    if index >= 0:
                        self.trigger_action_combo.setCurrentIndex(index)

                    # 设置表单提示消息
                    form_prompt = trigger_data.get('form_prompt_message', '')
                    if hasattr(self, 'form_prompt_message_edit'):
                        self.form_prompt_message_edit.setPlainText(form_prompt)

                    # 加载关键词
                    self.trigger_keywords_list.clear()
                    keywords = trigger_data.get('keywords', [])
                    for keyword in keywords:
                        self.trigger_keywords_list.addItem(keyword)

                    print(f"  ✅ 触发条件数据加载完成")
                else:
                    print(f"  ❌ 无法获取触发条件数据")
                    # 清空显示
                    self.trigger_name_edit.clear()
                    self.trigger_action_combo.setCurrentIndex(0)
                    if hasattr(self, 'form_prompt_message_edit'):
                        self.form_prompt_message_edit.clear()
                    self.trigger_keywords_list.clear()
            else:
                print(f"  ❌ 没有选中的触发条件")
        except Exception as e:
            print(f"❌ 触发条件选择处理失败: {e}")
            import traceback
            traceback.print_exc()

    def add_escalation_trigger(self):
        """添加升级触发条件"""
        text, ok = QInputDialog.getText(self, "添加触发条件", "请输入条件名称:")
        if ok and text:
            trigger_key = text.lower().replace(' ', '_')
            trigger_data = {
                "keywords": [],
                "action": "notify_supervisor"
            }

            item = QListWidgetItem(text)
            item.setData(Qt.ItemDataRole.UserRole, trigger_key)
            item.setData(Qt.ItemDataRole.UserRole + 1, trigger_data)
            self.escalation_triggers_list.addItem(item)
            self.escalation_triggers_list.setCurrentItem(item)
            self.on_config_changed()

    def remove_escalation_trigger(self):
        """删除升级触发条件"""
        current = self.escalation_triggers_list.currentItem()
        if current:
            current_row = self.escalation_triggers_list.row(current)
            self.escalation_triggers_list.takeItem(current_row)
            self.on_config_changed()

    def add_trigger_keyword(self):
        """添加触发关键词"""
        text, ok = QInputDialog.getText(self, "添加关键词", "请输入触发关键词:")
        if ok and text:
            self.trigger_keywords_list.addItem(text)
            self.update_current_trigger_data()
            self.config_changed.emit()

    def remove_trigger_keyword(self):
        """删除触发关键词"""
        current_row = self.trigger_keywords_list.currentRow()
        if current_row >= 0:
            self.trigger_keywords_list.takeItem(current_row)
            self.update_current_trigger_data()
            self.config_changed.emit()

    def update_current_trigger_data(self):
        """更新当前选中触发条件的数据"""
        current = self.escalation_triggers_list.currentItem()
        if current:
            trigger_key = current.data(Qt.ItemDataRole.UserRole)
            if trigger_key:
                print(f"🔧 更新触发条件数据: {trigger_key}")

                # 🔧 修复：直接从UI控件读取数据
                trigger_name = self.trigger_name_edit.text()
                trigger_action = self.trigger_action_combo.currentText()
                form_prompt_message = self.form_prompt_message_edit.toPlainText()

                # 收集关键词
                keywords = []
                for i in range(self.trigger_keywords_list.count()):
                    keywords.append(self.trigger_keywords_list.item(i).text())

                print(f"  触发条件信息: name='{trigger_name}', action='{trigger_action}', keywords={len(keywords)}")

                # 🔧 修复：构建完整的触发条件数据，包括名称
                updated_trigger_data = {
                    'name': trigger_name,
                    'action': trigger_action,
                    'keywords': keywords,
                    'form_prompt_message': form_prompt_message
                }

                # 🔧 修复：同时更新UserRole数据和config_data
                current.setData(Qt.ItemDataRole.UserRole + 1, updated_trigger_data)

                # 更新config_data中的对应数据
                if not hasattr(self, 'config_data'):
                    self.config_data = {}

                # 根据来源分别保存到不同的配置节点
                source = updated_trigger_data.get('source', 'escalation_rules')
                if source == 'advanced_support':
                    if 'advanced_support' not in self.config_data:
                        self.config_data['advanced_support'] = {}
                    if 'categories' not in self.config_data['advanced_support']:
                        self.config_data['advanced_support']['categories'] = {}
                    self.config_data['advanced_support']['categories'][trigger_key] = updated_trigger_data.copy()
                else:
                    if 'escalation_rules' not in self.config_data:
                        self.config_data['escalation_rules'] = {}
                    if 'escalation_triggers' not in self.config_data['escalation_rules']:
                        self.config_data['escalation_rules']['escalation_triggers'] = {}
                    self.config_data['escalation_rules']['escalation_triggers'][trigger_key] = updated_trigger_data.copy()

                print(f"  ✅ 触发条件数据已同步到UserRole和config_data")

                # 更新显示文本和键值
                if trigger_name:
                    current.setText(trigger_name)
                    # 更新trigger_key
                    new_key = trigger_name.lower().replace(' ', '_')
                    current.setData(Qt.ItemDataRole.UserRole, new_key)

                # 🔧 移除信号发射，避免循环调用
                # 信号发射由调用者负责

    def update_current_trigger_data_silent(self):
        """更新当前选中触发条件的数据（不触发信号）"""
        current = self.escalation_triggers_list.currentItem()
        if current:
            trigger_key = current.data(Qt.ItemDataRole.UserRole)
            if trigger_key:
                # 直接从UI控件读取数据
                trigger_name = self.trigger_name_edit.text()
                trigger_action = self.trigger_action_combo.currentText()
                form_prompt_message = self.form_prompt_message_edit.toPlainText()

                # 收集关键词
                keywords = []
                for i in range(self.trigger_keywords_list.count()):
                    keywords.append(self.trigger_keywords_list.item(i).text())

                # 🔧 修复：构建完整的触发条件数据，包括名称
                updated_trigger_data = {
                    'name': trigger_name,
                    'action': trigger_action,
                    'keywords': keywords,
                    'form_prompt_message': form_prompt_message
                }

                # 更新UserRole数据和config_data
                current.setData(Qt.ItemDataRole.UserRole + 1, updated_trigger_data)

                # 更新config_data中的对应数据
                if not hasattr(self, 'config_data'):
                    self.config_data = {}

                # 根据来源分别保存到不同的配置节点
                source = updated_trigger_data.get('source', 'escalation_rules')
                if source == 'advanced_support':
                    if 'advanced_support' not in self.config_data:
                        self.config_data['advanced_support'] = {}
                    if 'categories' not in self.config_data['advanced_support']:
                        self.config_data['advanced_support']['categories'] = {}
                    self.config_data['advanced_support']['categories'][trigger_key] = updated_trigger_data.copy()
                else:
                    if 'escalation_rules' not in self.config_data:
                        self.config_data['escalation_rules'] = {}
                    if 'escalation_triggers' not in self.config_data['escalation_rules']:
                        self.config_data['escalation_rules']['escalation_triggers'] = {}
                    self.config_data['escalation_rules']['escalation_triggers'][trigger_key] = updated_trigger_data.copy()

                # 更新显示文本和键值
                if trigger_name:
                    current.setText(trigger_name)
                    # 更新trigger_key
                    new_key = trigger_name.lower().replace(' ', '_')
                    current.setData(Qt.ItemDataRole.UserRole, new_key)




    def on_trigger_data_changed(self):
        """升级规则数据改变时的实时处理"""
        # 🔧 统一为Agent模式：依赖main_window的current_config_loaded保护
        # 只有在配置已加载完成后才处理数据变更
        if hasattr(self.parent(), 'current_config_loaded') and self.parent().current_config_loaded:
            # 立即更新当前选中触发条件的数据
            self.update_current_trigger_data()
        # 直接发射信号，让main_window处理
        self.config_changed.emit()

    def on_customer_type_data_changed(self):
        """客户类型数据改变时的实时处理"""
        # 🔧 统一为Agent模式：依赖main_window的current_config_loaded保护
        # 只有在配置已加载完成后才处理数据变更
        if hasattr(self.parent(), 'current_config_loaded') and self.parent().current_config_loaded:
            # 立即更新当前选中客户类型的数据
            self.update_current_customer_type()
        # 直接发射信号，让main_window处理
        self.config_changed.emit()

    def on_config_changed(self):
        """配置改变时的处理"""
        print(f"📝 高级设置模块配置发生变化，触发保存")
        # 🔧 修复：更新所有当前数据（不触发额外的信号）
        # 只有在配置已加载完成后才处理数据变更
        if hasattr(self.parent(), 'current_config_loaded') and self.parent().current_config_loaded:
            self.update_current_customer_type_silent()
            self.update_current_trigger_data_silent()
        self.config_changed.emit()
        print(f"✅ 高级设置模块配置变化处理完成")


