"""
Customer Service - 客服系统集成
与SalesGPT配合，处理人工客服接管和释放
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime
import asyncio

logger = logging.getLogger(__name__)

class CustomerService:
    """客服系统类，处理人工接管逻辑"""
    
    def __init__(self):
        self.active_handovers = {}  # 存储活跃的接管请求
        
    async def initiate_handover(self, session_id: str, user_message: str, user_ip: str = None, context: str = None) -> Dict[str, Any]:
        """启动人工客服接管"""
        try:
            handover_id = f"handover_{session_id}_{int(datetime.now().timestamp())}"
            
            handover_data = {
                "handover_id": handover_id,
                "session_id": session_id,
                "user_message": user_message,
                "user_ip": user_ip,
                "context": context,
                "timestamp": datetime.now().isoformat(),
                "status": "pending"
            }
            
            self.active_handovers[handover_id] = handover_data
            
            logger.info(f"人工客服接管启动: {handover_id} - 会话: {session_id}")
            
            return {
                "success": True,
                "handover_id": handover_id,
                "message": "Handover initiated successfully"
            }
            
        except Exception as e:
            logger.error(f"启动人工客服接管失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def complete_handover(self, handover_id: str, admin_id: str) -> Dict[str, Any]:
        """完成人工客服接管"""
        try:
            if handover_id in self.active_handovers:
                handover_data = self.active_handovers[handover_id]
                handover_data["status"] = "completed"
                handover_data["admin_id"] = admin_id
                handover_data["completed_at"] = datetime.now().isoformat()
                
                logger.info(f"人工客服接管完成: {handover_id} - 管理员: {admin_id}")
                
                return {
                    "success": True,
                    "handover_data": handover_data
                }
            else:
                return {
                    "success": False,
                    "error": "Handover not found"
                }
                
        except Exception as e:
            logger.error(f"完成人工客服接管失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def release_handover(self, session_id: str, admin_id: str) -> Dict[str, Any]:
        """释放人工客服接管，回到AI模式"""
        try:
            # 查找该会话的活跃接管
            handover_id = None
            for hid, hdata in self.active_handovers.items():
                if hdata["session_id"] == session_id and hdata["status"] == "completed":
                    handover_id = hid
                    break
            
            if handover_id:
                handover_data = self.active_handovers[handover_id]
                handover_data["status"] = "released"
                handover_data["released_at"] = datetime.now().isoformat()
                handover_data["released_by"] = admin_id
                
                logger.info(f"人工客服接管释放: {handover_id} - 管理员: {admin_id}")
                
                return {
                    "success": True,
                    "handover_id": handover_id,
                    "message": "Handover released successfully"
                }
            else:
                return {
                    "success": True,
                    "message": "No active handover found for this session"
                }
                
        except Exception as e:
            logger.error(f"释放人工客服接管失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_handover_status(self, session_id: str) -> Dict[str, Any]:
        """获取会话的接管状态"""
        try:
            for handover_id, handover_data in self.active_handovers.items():
                if handover_data["session_id"] == session_id:
                    return {
                        "has_handover": True,
                        "handover_id": handover_id,
                        "status": handover_data["status"],
                        "data": handover_data
                    }
            
            return {
                "has_handover": False,
                "status": "no_handover"
            }
            
        except Exception as e:
            logger.error(f"获取接管状态失败: {e}")
            return {
                "has_handover": False,
                "error": str(e)
            }
    
    def cleanup_old_handovers(self, max_age_hours: int = 24):
        """清理旧的接管记录"""
        try:
            current_time = datetime.now()
            to_remove = []
            
            for handover_id, handover_data in self.active_handovers.items():
                handover_time = datetime.fromisoformat(handover_data["timestamp"])
                age_hours = (current_time - handover_time).total_seconds() / 3600
                
                if age_hours > max_age_hours:
                    to_remove.append(handover_id)
            
            for handover_id in to_remove:
                del self.active_handovers[handover_id]
                logger.debug(f"清理旧接管记录: {handover_id}")
            
            if to_remove:
                logger.info(f"清理了 {len(to_remove)} 个旧接管记录")
                
        except Exception as e:
            logger.error(f"清理旧接管记录失败: {e}")

# 全局实例
customer_service = CustomerService()
