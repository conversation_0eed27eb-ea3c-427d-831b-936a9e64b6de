# -*- mode: python ; coding: utf-8 -*-
"""
YF AI Chat Backend v3 打包配置
高性能版本，包含完整的电源管理和性能优化功能
"""

import os
import sys
from pathlib import Path

# 获取项目根目录
project_root = Path(SPECPATH)

# 数据文件收集
datas = [
    # 配置文件
    (str(project_root / 'config' / 'protection_rules.json'), 'config'),
    (str(project_root / 'salesgpt_config' / 'advanced_sales_config.json'), 'salesgpt_config'),
    
    # 资源文件
    (str(project_root / 'resources' / 'text_icon.ico'), 'resources'),
    (str(project_root / 'resources' / 'text_icon.png'), 'resources'),
    
    # 黑名单缓存文件
    (str(project_root / 'blacklist_cache' / '*.txt'), 'blacklist_cache'),
    
    # SSL目录（如果存在）
    (str(project_root / 'ssl'), 'ssl') if (project_root / 'ssl').exists() else None,
    
    # 日志目录结构
    (str(project_root / 'logs' / 'README.md'), 'logs'),
    
    # 历史目录结构
    (str(project_root / 'history' / 'README.md'), 'history'),
]

# 过滤掉None值
datas = [item for item in datas if item is not None]

# 隐藏导入
hiddenimports = [
    # FastAPI相关
    'fastapi',
    'uvicorn',
    'uvicorn.lifespan',
    'uvicorn.lifespan.on',
    'uvicorn.protocols',
    'uvicorn.protocols.http',
    'uvicorn.protocols.websockets',
    'uvicorn.server',
    'uvicorn.config',
    'uvicorn.main',
    
    # Pydantic相关
    'pydantic',
    'pydantic.fields',
    'pydantic.main',
    'pydantic.types',
    'pydantic.validators',
    'pydantic.json',
    'pydantic.parse',
    'pydantic.schema',
    'pydantic.utils',
    'pydantic.error_wrappers',
    'pydantic.env_settings',
    'pydantic_settings',
    
    # 数据库相关
    'sqlite3',
    'aiosqlite',
    'sqlalchemy',
    'sqlalchemy.ext',
    'sqlalchemy.ext.asyncio',
    'sqlalchemy.orm',
    'sqlalchemy.pool',
    'sqlalchemy.engine',
    
    # HTTP客户端
    'httpx',
    'httpx._client',
    'httpx._config',
    'httpx._models',
    'httpx._types',
    'httpx._utils',
    'aiohttp',
    'aiohttp.client',
    'aiohttp.connector',
    'aiohttp.helpers',
    
    # 日志相关
    'logging',
    'logging.handlers',
    'logging.config',
    
    # 时间和时区
    'datetime',
    'zoneinfo',
    'pytz',
    
    # 系统相关
    'ctypes',
    'ctypes.wintypes',
    'psutil',
    'subprocess',
    'threading',
    'multiprocessing',
    
    # 加密相关
    'cryptography',
    'cryptography.fernet',
    'cryptography.hazmat',
    'cryptography.hazmat.primitives',
    'cryptography.hazmat.backends',
    
    # JSON和配置
    'json',
    'configparser',
    'dotenv',
    
    # 网络相关
    'socket',
    'ssl',
    'websockets',
    'websockets.server',
    'websockets.client',
    
    # 异步相关
    'asyncio',
    'asyncio.events',
    'asyncio.protocols',
    'asyncio.transports',
    'concurrent',
    'concurrent.futures',
    
    # 文件处理
    'pathlib',
    'tempfile',
    'shutil',
    
    # 正则表达式
    're',
    
    # 数学和随机
    'random',
    'uuid',
    'hashlib',
    
    # 类型提示
    'typing',
    'typing_extensions',
    
    # 项目模块
    'models',
    'models.chat_models',
    'services',
    'services.database',
    'services.customer_service',
    'services.salesgpt_service',
    'services.memory_service',
    'services.escalation_service',
    'services.form_service',
    'services.protection_service',
    'services.ip_blacklist_service',
    'services.conversation_rules_service',
    'services.customer_segmentation_service',
    'utils',
    'utils.logger',
    'utils.file_logger',
    'utils.path_utils',
    'utils.timezone_config',
    'logging_config',
    'power_management',
    'exe_performance_wrapper',
]

# 排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',
    'pandas',
    'scipy',
    'PIL',
    'cv2',
    'tensorflow',
    'torch',
    'jupyter',
    'notebook',
    'IPython',
    'pytest',
    'unittest',
    'doctest',
    'pdb',
    'cProfile',
    'profile',
    'pstats',
    'timeit',
]

# 主程序分析
a = Analysis(
    ['start_high_performance.py'],  # 使用高性能启动脚本作为入口点
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[str(project_root)],  # 包含自定义hook
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 处理重复文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='YF_AI_Chat_Backend_v3',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 启用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 保持控制台窗口以显示日志
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(project_root / 'resources' / 'text_icon.ico'),  # 设置图标
    version_file=None,
    uac_admin=False,  # 不强制要求管理员权限
    uac_uiaccess=False,
)
