# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['start_high_performance.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['aiosqlite', 'aiosqlite.core', 'sqlalchemy.dialects.sqlite.aiosqlite'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='YF_AI_Chat_Backend_v3',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['resources\\text_icon.ico'],
)
