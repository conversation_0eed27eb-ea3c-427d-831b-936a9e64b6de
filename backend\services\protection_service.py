import json
import re
import time
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from collections import defaultdict
import asyncio
from utils.path_utils import get_config_directory

class ProtectionService:
    def __init__(self):
        # 支持环境变量配置路径，默认使用path_utils
        config_path = os.getenv("PROTECTION_RULES_PATH")
        if config_path:
            self.config_file = Path(config_path)
        else:
            # 使用path_utils获取正确的配置目录
            config_dir = get_config_directory()
            self.config_file = config_dir / "protection_rules.json"

            # 如果都找不到，使用默认路径
            if not self.config_file:
                self.config_file = Path(__file__).parent.parent / "config" / "protection_rules.json"
        self.config = {}
        self.last_config_mtime = 0
        self.request_history = defaultdict(list)  # IP -> [timestamps]
        self.session_requests = defaultdict(int)  # session_id -> request_count
        self.blocked_ips = set()
        self.violation_history = defaultdict(list)  # IP -> [violation_timestamps]

        # 延迟导入IP黑名单服务，避免循环导入
        self.ip_blacklist_service = None
        self._init_ip_blacklist_service()

        self.load_config()

    def _init_ip_blacklist_service(self):
        """初始化IP黑名单服务"""
        try:
            from .ip_blacklist_service import ip_blacklist_service
            self.ip_blacklist_service = ip_blacklist_service
        except ImportError as e:
            print(f"Warning: IP blacklist service not available: {e}")
            self.ip_blacklist_service = None

    def load_config(self):
        """加载配置文件，支持热重载"""
        try:
            if not self.config_file.exists():
                print(f"Protection config file not found: {self.config_file}")
                return
            
            current_mtime = self.config_file.stat().st_mtime
            if current_mtime != self.last_config_mtime:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                self.last_config_mtime = current_mtime
                print(f"Protection rules loaded/reloaded at {datetime.now()}")
        except Exception as e:
            print(f"Error loading protection config: {e}")
    
    def is_enabled(self) -> bool:
        """检查防护是否启用"""
        self.load_config()  # 热重载
        return self.config.get("enabled", True)
    
    def check_rate_limit(self, ip: str) -> Tuple[bool, str]:
        """检查IP的请求频率限制"""
        if not self.is_enabled():
            return True, ""
        
        rate_config = self.config.get("rate_limiting", {})
        if not rate_config.get("enabled", True):
            return True, ""
        
        current_time = time.time()
        max_per_minute = rate_config.get("max_requests_per_minute", 10)
        max_per_hour = rate_config.get("max_requests_per_hour", 100)
        
        # 清理过期的请求记录
        self.request_history[ip] = [
            timestamp for timestamp in self.request_history[ip]
            if current_time - timestamp < 3600  # 保留1小时内的记录
        ]
        
        # 检查每分钟限制
        minute_requests = [
            timestamp for timestamp in self.request_history[ip]
            if current_time - timestamp < 60
        ]
        
        if len(minute_requests) >= max_per_minute:
            return False, self.config.get("response_messages", {}).get(
                "rate_limit_exceeded", "Too many requests"
            )
        
        # 检查每小时限制
        if len(self.request_history[ip]) >= max_per_hour:
            return False, self.config.get("response_messages", {}).get(
                "rate_limit_exceeded", "Too many requests"
            )
        
        # 记录请求
        self.request_history[ip].append(current_time)
        return True, ""
    
    async def check_ip_blacklist(self, ip: str) -> Tuple[bool, str]:
        """检查IP是否在黑名单中（包括外部黑名单源）"""
        if not self.is_enabled():
            return True, ""

        blacklist_config = self.config.get("ip_blacklist", {})
        if not blacklist_config.get("enabled", True):
            return True, ""

        # 1. 检查静态黑名单（配置文件中的）
        blocked_ips = blacklist_config.get("ips", [])
        if ip in blocked_ips or ip in self.blocked_ips:
            return False, self.config.get("response_messages", {}).get(
                "ip_blocked", "IP blocked (static blacklist)"
            )

        # 2. 检查外部黑名单源（GitHub等）
        if self.ip_blacklist_service:
            try:
                is_blacklisted, match_type = await self.ip_blacklist_service.is_ip_blacklisted(ip)
                if is_blacklisted:
                    return False, self.config.get("response_messages", {}).get(
                        "ip_blocked", f"IP blocked (external blacklist: {match_type})"
                    )
            except Exception as e:
                print(f"Error checking external IP blacklist: {e}")
                # 继续执行，不因为外部黑名单错误而阻止正常功能

        return True, ""
    
    def check_content_filtering(self, message: str) -> Tuple[bool, str]:
        """检查消息内容过滤"""
        if not self.is_enabled():
            return True, ""
        
        content_config = self.config.get("content_filtering", {})
        if not content_config.get("enabled", True):
            return True, ""
        
        # 检查消息长度
        max_length = content_config.get("max_message_length", 1000)
        min_length = content_config.get("min_message_length", 1)
        
        if len(message) > max_length:
            return False, self.config.get("response_messages", {}).get(
                "message_too_long", "Message too long"
            ).replace("{max_length}", str(max_length))
        
        if len(message.strip()) < min_length:
            return False, self.config.get("response_messages", {}).get(
                "message_too_short", "Message too short"
            )
        
        # 检查空消息
        empty_config = content_config.get("empty_message", {})
        if empty_config.get("enabled", True) and not message.strip():
            return False, self.config.get("response_messages", {}).get(
                "content_blocked", "Empty message"
            )
        
        # 检查垃圾模式
        spam_patterns = content_config.get("spam_patterns", [])
        for pattern_config in spam_patterns:
            pattern = pattern_config.get("pattern", "")
            flags = pattern_config.get("flags", "")
            action = pattern_config.get("action", "block")
            description = pattern_config.get("description", "")
            
            try:
                regex_flags = 0
                if 'i' in flags:
                    regex_flags |= re.IGNORECASE
                if 'm' in flags:
                    regex_flags |= re.MULTILINE
                
                if re.search(pattern, message, regex_flags):
                    if action == "block":
                        return False, self.config.get("response_messages", {}).get(
                            "spam_detected", f"Content blocked: {description}"
                        )
                    elif action == "warn":
                        print(f"Content warning for message: {description}")
            except re.error as e:
                print(f"Invalid regex pattern: {pattern}, error: {e}")
        
        # 检查单词重复
        word_config = content_config.get("word_repetition", {})
        if word_config.get("enabled", True):
            max_count = word_config.get("max_same_word_count", 5)
            words = message.lower().split()
            word_counts = {}
            for word in words:
                word_counts[word] = word_counts.get(word, 0) + 1
                if word_counts[word] > max_count:
                    return False, self.config.get("response_messages", {}).get(
                        "spam_detected", f"Too many repeated words: {word}"
                    )
        
        return True, ""
    
    def check_llm_abuse(self, message: str, session_id: str) -> Tuple[bool, str]:
        """检查LLM滥用防护"""
        if not self.is_enabled():
            return True, ""
        
        llm_config = self.config.get("llm_abuse_protection", {})
        if not llm_config.get("enabled", True):
            return True, ""
        
        # 检查会话请求次数
        max_requests = llm_config.get("max_requests_per_session", 50)
        self.session_requests[session_id] += 1
        
        if self.session_requests[session_id] > max_requests:
            return False, self.config.get("response_messages", {}).get(
                "llm_abuse_detected", "Too many requests in this session"
            )
        
        # 检查可疑模式
        suspicious_patterns = llm_config.get("suspicious_patterns", [])
        for pattern_config in suspicious_patterns:
            pattern = pattern_config.get("pattern", "")
            flags = pattern_config.get("flags", "")
            action = pattern_config.get("action", "block")
            description = pattern_config.get("description", "")
            
            try:
                regex_flags = 0
                if 'i' in flags:
                    regex_flags |= re.IGNORECASE
                if 'm' in flags:
                    regex_flags |= re.MULTILINE
                
                if re.search(pattern, message, regex_flags):
                    if action == "block":
                        return False, self.config.get("response_messages", {}).get(
                            "llm_abuse_detected", f"Suspicious activity: {description}"
                        )
            except re.error as e:
                print(f"Invalid regex pattern: {pattern}, error: {e}")
        
        return True, ""
    
    def log_violation(self, ip: str, violation_type: str, message: str = ""):
        """记录违规行为"""
        if not self.config.get("logging", {}).get("enabled", True):
            return
        
        current_time = time.time()
        self.violation_history[ip].append({
            "timestamp": current_time,
            "type": violation_type,
            "message": message[:100]  # 只记录前100个字符
        })
        
        # 检查自动封禁
        self.check_auto_ban(ip)
        
        print(f"Violation logged: {ip} - {violation_type} - {message[:50]}")
    
    def check_auto_ban(self, ip: str):
        """检查是否需要自动封禁IP"""
        auto_ban_config = self.config.get("ip_blacklist", {}).get("auto_ban", {})
        if not auto_ban_config.get("enabled", True):
            return
        
        threshold = auto_ban_config.get("violation_threshold", 5)
        time_window = auto_ban_config.get("time_window_minutes", 5) * 60
        current_time = time.time()
        
        # 清理过期的违规记录
        self.violation_history[ip] = [
            violation for violation in self.violation_history[ip]
            if current_time - violation["timestamp"] < time_window
        ]
        
        # 检查是否达到封禁阈值
        if len(self.violation_history[ip]) >= threshold:
            self.blocked_ips.add(ip)
            print(f"Auto-banned IP: {ip} (violations: {len(self.violation_history[ip])})")
    
    async def check_message_protection(self, message: str, ip: str, session_id: str) -> Tuple[bool, str]:
        """综合检查消息防护"""
        # 1. 检查IP黑名单（异步）
        allowed, error_msg = await self.check_ip_blacklist(ip)
        if not allowed:
            self.log_violation(ip, "ip_blocked")
            return False, error_msg
        
        # 2. 检查请求频率
        allowed, error_msg = self.check_rate_limit(ip)
        if not allowed:
            self.log_violation(ip, "rate_limit", message)
            return False, error_msg
        
        # 3. 检查内容过滤
        allowed, error_msg = self.check_content_filtering(message)
        if not allowed:
            self.log_violation(ip, "content_blocked", message)
            return False, error_msg
        
        # 4. 检查LLM滥用
        allowed, error_msg = self.check_llm_abuse(message, session_id)
        if not allowed:
            self.log_violation(ip, "llm_abuse", message)
            return False, error_msg
        
        return True, ""
    
    def get_stats(self) -> Dict:
        """获取防护统计信息"""
        stats = {
            "total_blocked_ips": len(self.blocked_ips),
            "active_sessions": len(self.session_requests),
            "total_violations": sum(len(violations) for violations in self.violation_history.values()),
            "config_last_updated": self.last_config_mtime
        }

        # 添加IP黑名单统计
        if self.ip_blacklist_service:
            try:
                blacklist_stats = self.ip_blacklist_service.get_blacklist_summary()
                stats["external_blacklist"] = blacklist_stats
            except Exception as e:
                stats["external_blacklist"] = {"error": str(e)}
        else:
            stats["external_blacklist"] = {"status": "not_available"}

        return stats

# 全局实例
protection_service = ProtectionService()
