# YF AI Chat - Git忽略文件

# 虚拟环境
.venv/
venv/
env/
ENV/

# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 环境变量文件
.env
*.env
!.env.example

# 数据库文件
*.db
*.sqlite
*.sqlite3
chat_data.db

# 日志文件
*.log
logs/

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 构建文件
build/
dist/
*.egg-info/

# 测试覆盖率
.coverage
htmlcov/
.pytest_cache/

# WordPress插件临时文件
wordpress-plugin/*.zip

# 客服端编译文件
client/*.exe
client/build/
client/dist/

# 备份文件
*.bak
*.backup
*~
backups/
backup_*/

# 配置文件备份
*.conf.bak
*.ini.bak

# 测试文件
test_*.py
*_test.py

# 用户数据文件
users.json
mail.json
archived_sessions.json

# 缓存目录
cache/
.cache/

# 黑名单缓存（保留结构，忽略内容）
blacklist_cache/*.txt
!blacklist_cache/README.md

# 打包文件
*.zip
*.tar.gz
*.rar
*.7z

# 临时修复文件
FIXED_*.liquid
FIXED_*.md
*_FIXED.*

# 文档目录（使用根目录readme.md）
docs/
