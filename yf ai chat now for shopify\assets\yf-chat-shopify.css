/* YF AI Chat Widget for Shopify - Styles */
/* 基于WordPress版本改造，适配Shopify主题环境 */

.yf-chat-widget {
    position: fixed !important;
    z-index: 999999 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 强制移除所有按钮的默认样式和边框 */
.yf-chat-widget button,
.yf-chat-widget button:hover,
.yf-chat-widget button:focus,
.yf-chat-widget button:active,
.yf-chat-widget button:visited {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-focus-ring-color: transparent !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
}

.yf-chat-widget.top-left {
    top: var(--yf-margin, 100px);
    left: var(--yf-margin, 100px);
}

.yf-chat-widget.top-right {
    top: var(--yf-margin, 100px);
    right: var(--yf-margin, 100px);
}

.yf-chat-widget.bottom-left {
    bottom: var(--yf-margin, 100px);
    left: var(--yf-margin, 100px);
}

.yf-chat-widget.bottom-right {
    bottom: var(--yf-margin, 100px) !important;
    right: var(--yf-margin, 100px) !important;
}

.yf-chat-widget .yf-chat-button {
    width: 56px !important;
    height: 56px !important;
    border-radius: 50% !important;
    background: #ffffff !important;
    border: none !important;
    cursor: pointer !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: #007AFF !important;
    font-size: 24px !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    outline: none !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    -webkit-focus-ring-color: transparent !important;
    position: relative !important;
    z-index: 999999 !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.yf-chat-widget .yf-chat-button:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    background: #f8f9fa;
    outline: none !important;
    border: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

.yf-chat-widget .yf-chat-button:active {
    transform: scale(0.95);
    transition: all 0.1s ease;
    outline: none !important;
    border: none !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

.yf-chat-widget .yf-chat-button:focus {
    outline: none !important;
    border: none !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

.yf-chat-window {
    position: absolute;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.yf-chat-widget.bottom-right .yf-chat-window,
.yf-chat-widget.bottom-left .yf-chat-window {
    bottom: 70px;
}

.yf-chat-widget.top-right .yf-chat-window,
.yf-chat-widget.top-left .yf-chat-window {
    top: 70px;
}

.yf-chat-widget.bottom-left .yf-chat-window,
.yf-chat-widget.top-left .yf-chat-window {
    left: 0;
}

.yf-chat-widget.bottom-right .yf-chat-window,
.yf-chat-widget.top-right .yf-chat-window {
    right: 0;
}

.yf-chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.yf-chat-header-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.yf-chat-title {
    font-weight: 600;
    font-size: 16px;
}

.yf-chat-widget .yf-chat-close,
.yf-chat-widget .yf-chat-clear {
    background: none;
    border: none !important;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    outline: none !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    -webkit-focus-ring-color: transparent !important;
}

.yf-chat-widget .yf-chat-close:hover,
.yf-chat-widget .yf-chat-clear:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
    outline: none !important;
    border: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

.yf-chat-close {
    font-size: 20px;
}

.yf-chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
}

.yf-chat-message {
    margin-bottom: 16px;
    display: flex;
    align-items: flex-start;
}

.yf-chat-message.user {
    justify-content: flex-end;
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
}

.yf-chat-message.ai {
    justify-content: flex-start;
}

.yf-chat-message-content {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.4;
}

.yf-chat-message.user .yf-chat-message-content {
    background: #667eea !important;
    color: white !important;
    border-bottom-right-radius: 4px;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    width: auto !important;
}

.yf-chat-message.ai .yf-chat-message-content {
    background: white;
    color: #333;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 4px;
}

.yf-chat-message.system {
    justify-content: center;
}

.yf-chat-message.system .yf-chat-message-content {
    background: #e3f2fd;
    color: #1976d2;
    font-size: 12px;
    font-style: italic;
    border-radius: 12px;
    padding: 6px 12px;
    margin: 5px 20px;
    text-align: center;
    border: 1px solid #bbdefb;
}

/* 通知动画 */
.yf-chat-button.yf-chat-notification {
    animation: yf-notification-pulse 1s ease-in-out infinite;
}

@keyframes yf-notification-pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 12px 40px rgba(255, 152, 0, 0.3), 0 4px 12px rgba(255, 152, 0, 0.2);
    }
}

.yf-chat-input-area {
    padding: 16px 20px;
    background: white;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 12px;
}

.yf-chat-input {
    flex: 1;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 10px 16px;
    font-size: 14px;
    outline: none;
    resize: none;
    max-height: 100px;
    -webkit-tap-highlight-color: transparent;
}

.yf-chat-input:focus {
    border-color: #667eea;
}

.yf-chat-widget .yf-chat-send {
    background: #667eea;
    color: white;
    border: none !important;
    border-radius: 8px;
    width: 40px;
    height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
    outline: none !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    -webkit-focus-ring-color: transparent !important;
}

.yf-chat-widget .yf-chat-send:hover {
    background: #5a6fd8 !important;
    outline: none !important;
    border: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

.yf-chat-widget .yf-chat-send:disabled {
    background: #ccc !important;
    cursor: not-allowed;
    outline: none !important;
    border: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

.yf-chat-typing {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 14px;
    font-style: italic;
}

.yf-chat-typing-dots {
    display: flex;
    gap: 2px;
}

.yf-chat-typing-dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #666;
    animation: yf-typing 1.4s infinite ease-in-out;
}

.yf-chat-typing-dot:nth-child(1) { animation-delay: -0.32s; }
.yf-chat-typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes yf-typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

/* Mobile responsive */
@media (max-width: 480px) {
    .yf-chat-window {
        width: 300px;
        height: 450px;
    }
    
    .yf-chat-widget {
        --yf-margin: 20px !important;
    }
}

/* Animations */
.yf-chat-window.show {
    display: flex;
    animation: yf-slide-up 0.3s ease-out;
}

@keyframes yf-slide-up {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
