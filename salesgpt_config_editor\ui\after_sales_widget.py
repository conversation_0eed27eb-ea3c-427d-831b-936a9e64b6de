#!/usr/bin/env python3
"""
售后支持配置组件
负责售后服务和支持的配置管理
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QTextEdit, QComboBox, QGroupBox,
                            QFormLayout, QScrollArea, QListWidget, QListWidgetItem,
                            QPushButton, QInputDialog, QMessageBox, QCheckBox,
                            QTreeWidget, QTreeWidgetItem, QSplitter, QTabWidget)
from PyQt6.QtCore import pyqtSignal, Qt
# 移除不必要的导入，简化代码


class AfterSalesWidget(QWidget):
    """售后支持配置组件"""

    # 信号定义
    config_changed = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.config_data = {}
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 主容器
        main_widget = QWidget()
        layout = QVBoxLayout(main_widget)

        # 创建标签页
        tab_widget = QTabWidget()

        # 基础支持标签页
        basic_tab = self.create_basic_support_tab()
        tab_widget.addTab(basic_tab, "🔧 基础支持")

        # 高级支持与表单配置标签页（合并）
        advanced_tab = self.create_advanced_support_tab()
        tab_widget.addTab(advanced_tab, "🎯 高级支持与表单配置")

        layout.addWidget(tab_widget)

        # 设置滚动区域
        scroll.setWidget(main_widget)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(scroll)

    def create_basic_support_tab(self):
        """创建基础支持标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 说明信息
        help_label = QLabel("""
💡 基础支持配置说明:
• AI agent默认处理所有基础售后问题，无需启用开关
• 配置不同类别的售后问题：安装指导、操作说明、维护建议等
• AI会根据关键词自动识别问题类型并提供相应的解决方案
        """)
        help_label.setStyleSheet("color: #666; font-size: 11px; background: #f5f5f5; padding: 5px; border-radius: 5px;")
        help_label.setMaximumHeight(90)  # 限制最大高度，使其更紧凑
        layout.addWidget(help_label)

        # 移除基础支持设置组 - AI agent默认处理基础售后问题，无需开关控制

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：支持类别列表
        categories_group = QGroupBox("📋 支持类别")
        categories_layout = QVBoxLayout(categories_group)

        self.categories_tree = QTreeWidget()
        self.categories_tree.setHeaderLabels(["类别", "关键词数量"])
        self.categories_tree.currentItemChanged.connect(self.on_category_selected)
        categories_layout.addWidget(self.categories_tree)

        # 类别操作按钮
        category_btn_layout = QHBoxLayout()
        self.add_category_btn = QPushButton("➕ 添加类别")
        self.add_category_btn.clicked.connect(self.add_category)
        category_btn_layout.addWidget(self.add_category_btn)

        self.remove_category_btn = QPushButton("➖ 删除类别")
        self.remove_category_btn.clicked.connect(self.remove_category)
        category_btn_layout.addWidget(self.remove_category_btn)

        category_btn_layout.addStretch()
        categories_layout.addLayout(category_btn_layout)

        splitter.addWidget(categories_group)

        # 右侧：类别详细配置
        detail_group = QGroupBox("⚙️ 类别详细配置")
        detail_layout = QFormLayout(detail_group)
        # 移除顶部对齐，使用默认的左对齐以实现更好的水平对齐
        detail_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)

        # 类别名称 - 使用QGroupBox，超紧凑布局
        category_name_group = QGroupBox("🏷️ 类别名称")
        category_name_group.setMinimumHeight(60)  # 进一步减少高度
        category_name_group.setMaximumHeight(60)  # 限制最大高度
        category_name_layout = QVBoxLayout(category_name_group)
        category_name_layout.setContentsMargins(6, 4, 6, 4)  # 超紧凑的边距
        category_name_layout.setSpacing(2)  # 最小间距

        # 类别名称编辑器填满容器
        self.category_name_edit = QLineEdit()
        self.category_name_edit.setPlaceholderText("输入支持类别名称，如：产品使用、故障排除等")
        self.category_name_edit.textChanged.connect(self.on_config_changed)
        category_name_layout.addWidget(self.category_name_edit, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        detail_layout.addRow(category_name_group)

        # 触发关键词 - 使用QGroupBox，编辑框填满容器，按钮在右下角
        keywords_group = QGroupBox("🔑 触发关键词")
        keywords_group.setMinimumHeight(180)  # 设置最小高度
        keywords_main_layout = QVBoxLayout(keywords_group)
        keywords_main_layout.setContentsMargins(8, 8, 8, 8)
        keywords_main_layout.setSpacing(5)

        # 使用相对布局让列表填满大部分空间
        self.keywords_list = QListWidget()
        # 移除最大高度限制，让列表自动填充可用空间
        keywords_main_layout.addWidget(self.keywords_list, 1)  # stretch factor = 1

        # 按钮布局 - 右对齐，紧凑排列
        keywords_btn_layout = QHBoxLayout()
        keywords_btn_layout.setContentsMargins(0, 5, 0, 0)  # 顶部留一点间距
        keywords_btn_layout.addStretch()  # 左侧弹性空间，让按钮右对齐

        self.add_keyword_btn = QPushButton("➕ 添加")
        self.add_keyword_btn.setMaximumWidth(80)  # 限制按钮宽度，更紧凑
        self.add_keyword_btn.clicked.connect(self.add_keyword)
        keywords_btn_layout.addWidget(self.add_keyword_btn)

        self.remove_keyword_btn = QPushButton("➖ 删除")
        self.remove_keyword_btn.setMaximumWidth(80)  # 限制按钮宽度，更紧凑
        self.remove_keyword_btn.clicked.connect(self.remove_keyword)
        keywords_btn_layout.addWidget(self.remove_keyword_btn)

        keywords_main_layout.addLayout(keywords_btn_layout)

        # 直接添加QGroupBox，不需要标签
        detail_layout.addRow(keywords_group)

        # 解决方案内容 - 使用QGroupBox，编辑框填满容器
        solution_group = QGroupBox("💡 解决方案")
        solution_group.setMinimumHeight(200)  # 设置最小高度，比关键词区域稍大
        solution_layout = QVBoxLayout(solution_group)
        solution_layout.setContentsMargins(8, 8, 8, 8)
        solution_layout.setSpacing(5)

        # 解决方案编辑器填满容器
        self.solution_content_edit = QTextEdit()
        self.solution_content_edit.setPlaceholderText("输入该类别问题的详细解决方案")
        # 移除最大高度限制，让编辑器自动填充可用空间
        self.solution_content_edit.textChanged.connect(self.on_config_changed)
        solution_layout.addWidget(self.solution_content_edit, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        detail_layout.addRow(solution_group)

        # 移除启用开关 - 配置文件中基础支持类别没有enabled字段

        splitter.addWidget(detail_group)
        splitter.setSizes([400, 600])

        layout.addWidget(splitter)

        # 加载默认类别
        self.load_default_categories()

        return widget

    def create_advanced_support_tab(self):
        """创建高级支持与表单配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 说明信息
        help_label = QLabel("""
💡 高级支持与表单配置说明:
• AI agent默认处理高级售后问题，通过类别自动判断
• 配置不同类别的复杂售后问题：技术故障、退换货等
• 每个类别可设置专门的触发消息，并统一使用表单收集信息
        """)
        help_label.setStyleSheet("color: #666; font-size: 11px; background: #f5f5f5; padding: 5px; border-radius: 5px;")
        layout.addWidget(help_label)

        # 创建垂直分割器（上下分割）
        main_splitter = QSplitter(Qt.Orientation.Vertical)

        # 上半部分：高级支持类别配置
        upper_widget = self.create_advanced_categories_section()
        main_splitter.addWidget(upper_widget)

        # 下半部分：表单字段配置
        lower_widget = self.create_form_fields_section()
        main_splitter.addWidget(lower_widget)

        # 设置上下比例 (60% : 40%)
        main_splitter.setSizes([600, 400])

        layout.addWidget(main_splitter)

        return widget

    def create_advanced_categories_section(self):
        """创建高级支持类别配置区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 区域标题
        title_label = QLabel("🎯 高级支持类别配置")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #333; margin: 5px 0;")
        layout.addWidget(title_label)

        # 创建水平分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：类别列表
        categories_group = QGroupBox("📋 类别列表")
        categories_layout = QVBoxLayout(categories_group)

        self.advanced_categories_list = QListWidget()
        self.advanced_categories_list.currentItemChanged.connect(self.on_advanced_category_selected)
        categories_layout.addWidget(self.advanced_categories_list)

        # 类别操作按钮
        advanced_btn_layout = QHBoxLayout()
        self.add_advanced_category_btn = QPushButton("➕ 添加类别")
        self.add_advanced_category_btn.clicked.connect(self.add_advanced_category)
        advanced_btn_layout.addWidget(self.add_advanced_category_btn)

        self.remove_advanced_category_btn = QPushButton("➖ 删除类别")
        self.remove_advanced_category_btn.clicked.connect(self.remove_advanced_category)
        advanced_btn_layout.addWidget(self.remove_advanced_category_btn)

        advanced_btn_layout.addStretch()
        categories_layout.addLayout(advanced_btn_layout)

        # 右侧：类别详细配置
        category_detail_group = QGroupBox("🔧 类别详细配置")
        category_detail_layout = QFormLayout(category_detail_group)
        category_detail_layout.setLabelAlignment(Qt.AlignmentFlag.AlignTop)  # 标签顶部对齐
        category_detail_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)

        # 类别名称 - 使用QGroupBox
        advanced_category_name_group = QGroupBox("🏷️ 类别名称")
        advanced_category_name_group.setMinimumHeight(80)  # 设置最小高度
        advanced_category_name_layout = QVBoxLayout(advanced_category_name_group)
        advanced_category_name_layout.setContentsMargins(8, 8, 8, 8)
        advanced_category_name_layout.setSpacing(5)

        # 类别名称编辑器填满容器
        self.advanced_category_name_edit = QLineEdit()
        self.advanced_category_name_edit.setPlaceholderText("输入高级支持类别名称，如：技术咨询、定制服务等")
        advanced_category_name_layout.addWidget(self.advanced_category_name_edit, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        category_detail_layout.addRow(advanced_category_name_group)

        # 触发消息 - 使用QGroupBox
        trigger_message_group = QGroupBox("💬 触发消息")
        trigger_message_group.setMinimumHeight(120)  # 设置最小高度
        trigger_message_layout = QVBoxLayout(trigger_message_group)
        trigger_message_layout.setContentsMargins(8, 8, 8, 8)
        trigger_message_layout.setSpacing(5)

        # 触发消息编辑器填满容器
        self.advanced_trigger_message_edit = QTextEdit()
        self.advanced_trigger_message_edit.setPlaceholderText("当检测到此类问题时向用户显示的消息...")
        # 移除最大高度限制，让编辑器自动填充可用空间
        trigger_message_layout.addWidget(self.advanced_trigger_message_edit, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        category_detail_layout.addRow(trigger_message_group)

        # 触发关键词 - 使用QGroupBox，编辑框填满容器，按钮在右下角
        advanced_keywords_group = QGroupBox("🔑 触发关键词")
        advanced_keywords_group.setMinimumHeight(160)  # 设置最小高度
        advanced_keywords_main_layout = QVBoxLayout(advanced_keywords_group)
        advanced_keywords_main_layout.setContentsMargins(8, 8, 8, 8)
        advanced_keywords_main_layout.setSpacing(5)

        # 使用相对布局让列表填满大部分空间
        self.advanced_keywords_list = QListWidget()
        # 移除最大高度限制，让列表自动填充可用空间
        advanced_keywords_main_layout.addWidget(self.advanced_keywords_list, 1)  # stretch factor = 1

        # 按钮布局 - 右对齐，紧凑排列
        keywords_btn_layout = QHBoxLayout()
        keywords_btn_layout.setContentsMargins(0, 5, 0, 0)  # 顶部留一点间距
        keywords_btn_layout.addStretch()  # 左侧弹性空间，让按钮右对齐

        self.add_advanced_keyword_btn = QPushButton("➕ 添加")
        self.add_advanced_keyword_btn.setMaximumWidth(80)  # 限制按钮宽度，更紧凑
        self.add_advanced_keyword_btn.clicked.connect(self.add_advanced_keyword)
        keywords_btn_layout.addWidget(self.add_advanced_keyword_btn)

        self.remove_advanced_keyword_btn = QPushButton("➖ 删除")
        self.remove_advanced_keyword_btn.setMaximumWidth(80)  # 限制按钮宽度，更紧凑
        self.remove_advanced_keyword_btn.clicked.connect(self.remove_advanced_keyword)
        keywords_btn_layout.addWidget(self.remove_advanced_keyword_btn)

        advanced_keywords_main_layout.addLayout(keywords_btn_layout)

        # 直接添加QGroupBox，不需要标签
        category_detail_layout.addRow(advanced_keywords_group)

        # 添加到分割器
        splitter.addWidget(categories_group)
        splitter.addWidget(category_detail_group)
        splitter.setSizes([300, 500])

        layout.addWidget(splitter)

        return widget

    def create_form_fields_section(self):
        """创建表单字段配置区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 区域标题
        title_label = QLabel("📋 表单字段配置")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #333; margin: 5px 0;")
        layout.addWidget(title_label)

        # 创建水平分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：表单字段列表
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        fields_group = QGroupBox("📋 字段列表")
        fields_layout = QVBoxLayout(fields_group)

        self.form_fields_list = QListWidget()
        self.form_fields_list.currentItemChanged.connect(self.on_form_field_selected)
        fields_layout.addWidget(self.form_fields_list)

        # 字段操作按钮
        field_btn_layout = QHBoxLayout()
        self.add_field_btn = QPushButton("➕ 添加字段")
        self.add_field_btn.clicked.connect(self.add_form_field)
        field_btn_layout.addWidget(self.add_field_btn)

        self.remove_field_btn = QPushButton("➖ 删除字段")
        self.remove_field_btn.clicked.connect(self.remove_form_field)
        field_btn_layout.addWidget(self.remove_field_btn)

        field_btn_layout.addStretch()
        fields_layout.addLayout(field_btn_layout)

        left_layout.addWidget(fields_group)

        # 右侧：字段详细配置
        field_detail_group = QGroupBox("⚙️ 字段详细配置")
        field_detail_layout = QFormLayout(field_detail_group)
        field_detail_layout.setLabelAlignment(Qt.AlignmentFlag.AlignTop)  # 标签顶部对齐
        field_detail_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)

        # 字段名称
        self.field_name_edit = QLineEdit()
        self.field_name_edit.textChanged.connect(self.on_config_changed)
        field_detail_layout.addRow("字段名称:", self.field_name_edit)

        # 显示标签
        self.field_label_edit = QLineEdit()
        self.field_label_edit.textChanged.connect(self.on_config_changed)
        field_detail_layout.addRow("显示标签:", self.field_label_edit)

        # 字段类型
        self.field_type_combo = QComboBox()
        self.field_type_combo.addItems(["text", "email", "tel", "select", "textarea", "number", "date"])
        self.field_type_combo.currentTextChanged.connect(self.on_config_changed)
        field_detail_layout.addRow("字段类型:", self.field_type_combo)

        # 占位符
        self.field_placeholder_edit = QLineEdit()
        self.field_placeholder_edit.textChanged.connect(self.on_config_changed)
        field_detail_layout.addRow("占位符:", self.field_placeholder_edit)

        # 验证规则
        self.field_validation_edit = QLineEdit()
        self.field_validation_edit.setPlaceholderText("正则表达式验证规则")
        self.field_validation_edit.textChanged.connect(self.on_config_changed)
        field_detail_layout.addRow("验证规则:", self.field_validation_edit)

        # 错误消息
        self.field_error_edit = QLineEdit()
        self.field_error_edit.textChanged.connect(self.on_config_changed)
        field_detail_layout.addRow("错误消息:", self.field_error_edit)

        # 选项列表 - 使用QGroupBox
        options_group = QGroupBox("📝 选项列表 (仅选择类型字段)")
        options_layout = QVBoxLayout(options_group)
        options_layout.setContentsMargins(8, 8, 8, 8)
        options_layout.setSpacing(5)

        self.field_options_list = QListWidget()
        self.field_options_list.setMaximumHeight(100)  # 增加高度
        options_layout.addWidget(self.field_options_list)

        options_btn_layout = QHBoxLayout()
        options_btn_layout.setContentsMargins(0, 0, 0, 0)
        self.add_option_btn = QPushButton("➕ 添加选项")
        self.add_option_btn.clicked.connect(self.add_field_option)
        options_btn_layout.addWidget(self.add_option_btn)

        self.remove_option_btn = QPushButton("➖ 删除选项")
        self.remove_option_btn.clicked.connect(self.remove_field_option)
        options_btn_layout.addWidget(self.remove_option_btn)

        options_btn_layout.addStretch()
        options_layout.addLayout(options_btn_layout)

        field_detail_layout.addRow(options_group)

        # 添加到分割器
        splitter.addWidget(left_widget)
        splitter.addWidget(field_detail_group)
        splitter.setSizes([300, 500])

        layout.addWidget(splitter)

        return widget

    # 表单配置已合并到高级支持标签页中，删除独立的表单配置标签页方法



    def load_default_categories(self):
        """从配置文件加载基础支持类别"""
        # 移除硬编码，从配置文件加载
        # 如果配置文件中没有数据，则不显示任何默认项
        # 基础支持类别数据现在从配置文件加载，不使用硬编码
        pass

    def load_default_advanced_categories(self):
        """从配置文件加载高级支持类别（移除硬编码）"""
        # 高级支持类别数据将在load_config时从配置文件加载
        # 这里不加载任何硬编码的默认数据，避免重复
        pass

    def load_default_form_fields(self):
        """从配置文件加载表单字段（移除硬编码）"""
        # 表单字段数据将在load_config时从配置文件加载
        # 这里不加载任何硬编码的默认数据，避免重复
        pass

    def on_category_selected(self, current, previous):
        """选择基础支持类别时的处理"""
        try:
            print(f"🖱️ 用户切换基础支持类别")

            # 🔧 统一为Agent模式：只有在配置已加载完成后才保存之前的数据
            if previous and hasattr(self.parent(), 'current_config_loaded') and self.parent().current_config_loaded:
                print(f"  保存之前选中的类别数据: {previous.text(0)}")
                # 临时设置当前项为previous，以便update_current_category_data能正确工作
                self.categories_tree.setCurrentItem(previous)
                self.update_current_category_data()
                # 恢复当前项
                self.categories_tree.setCurrentItem(current)

            if current:
                category_key = current.data(0, Qt.ItemDataRole.UserRole)
                category_data = current.data(1, Qt.ItemDataRole.UserRole)
                print(f"🔄 当前选中项: {current.text(0)}")
                print(f"  键值: {category_key}")
                print(f"  数据: {category_data is not None}")

                if category_data:
                    # 🔧 修复：优先使用保存的name字段，如果没有则使用key的友好显示名称
                    saved_name = category_data.get('name', '')
                    if saved_name:
                        display_name = saved_name
                    else:
                        display_name = category_key.replace('_', ' ').title()
                    self.category_name_edit.setText(display_name)
                    print(f"  加载类别名称: {display_name}")

                    # 响应模板已移除 - AI agent使用内置的回复逻辑

                    # 设置解决方案内容（从knowledge_base获取）
                    solution_content = ""
                    knowledge_base = category_data.get('knowledge_base', {})
                    if knowledge_base:
                        for kb_key, kb_value in knowledge_base.items():
                            if isinstance(kb_value, list):
                                if solution_content:
                                    solution_content += f"\n\n{kb_key.replace('_', ' ').title()}:\n"
                                else:
                                    solution_content += f"{kb_key.replace('_', ' ').title()}:\n"
                                solution_content += "\n".join(kb_value)

                    self.solution_content_edit.setPlainText(solution_content)

                    # 加载关键词
                    self.keywords_list.clear()
                    for keyword in category_data.get('keywords', []):
                        self.keywords_list.addItem(keyword)
                    print(f"  ✅ 类别数据加载完成")
                else:
                    print(f"  ❌ 无法获取类别数据")
                    # 清空显示
                    self.category_name_edit.clear()
                    self.solution_content_edit.setPlainText("")
                    self.keywords_list.clear()
            else:
                print(f"  ❌ 没有选中的类别")
        except Exception as e:
            print(f"❌ 类别选择处理失败: {e}")
            import traceback
            traceback.print_exc()

    def on_advanced_category_selected(self, current, previous):
        """选择高级支持类别时的处理"""
        try:
            print(f"🖱️ 用户切换高级支持类别")

            # 🔧 统一为Agent模式：只有在配置已加载完成后才保存之前的数据
            if previous and hasattr(self.parent(), 'current_config_loaded') and self.parent().current_config_loaded:
                print(f"  保存之前选中的类别数据: {previous.text()}")
                # 临时设置当前项为previous，以便update_current_advanced_category_data能正确工作
                self.advanced_categories_list.setCurrentItem(previous)
                self.update_current_advanced_category_data()
                # 恢复当前项
                self.advanced_categories_list.setCurrentItem(current)

            if current:
                category_key = current.data(Qt.ItemDataRole.UserRole)
                category_data = current.data(Qt.ItemDataRole.UserRole + 1)
                print(f"🔄 当前选中项: {current.text()}")
                print(f"  键值: {category_key}")
                print(f"  数据: {category_data is not None}")

                if category_data:
                    # 🔧 修复：优先使用保存的name字段，如果没有则使用key的友好显示名称
                    saved_name = category_data.get('name', '')
                    if saved_name:
                        display_name = saved_name
                    else:
                        display_name = category_key.replace('_', ' ').title()
                    self.advanced_category_name_edit.setText(display_name)
                    print(f"  加载类别名称: {display_name}")

                    # 设置触发消息
                    trigger_message = category_data.get('trigger_message', '')
                    self.advanced_trigger_message_edit.setPlainText(trigger_message)

                    # 加载关键词
                    self.advanced_keywords_list.clear()
                    keywords = category_data.get('keywords', [])
                    for keyword in keywords:
                        self.advanced_keywords_list.addItem(keyword)
                    print(f"  ✅ 高级类别数据加载完成")
                else:
                    print(f"  ❌ 无法获取类别数据")
                    # 清空显示
                    self.advanced_category_name_edit.clear()
                    self.advanced_trigger_message_edit.setPlainText("")
                    self.advanced_keywords_list.clear()
            else:
                print(f"  ❌ 没有选中的类别")
        except Exception as e:
            print(f"❌ 高级类别选择处理失败: {e}")
            import traceback
            traceback.print_exc()


    def add_category(self):
        """添加基础支持类别"""
        text, ok = QInputDialog.getText(self, "添加支持类别", "请输入类别名称:")
        if ok and text:
            category_data = {
                "name": text,
                "keywords": [],
                "solution": ""
            }

            item = QTreeWidgetItem([text, "0"])
            item.setData(0, Qt.ItemDataRole.UserRole, text.lower().replace(' ', '_'))
            item.setData(1, Qt.ItemDataRole.UserRole, category_data)
            self.categories_tree.addTopLevelItem(item)
            self.on_config_changed()

    def remove_category(self):
        """删除基础支持类别"""
        current = self.categories_tree.currentItem()
        if current:
            index = self.categories_tree.indexOfTopLevelItem(current)
            self.categories_tree.takeTopLevelItem(index)
            self.on_config_changed()

    def add_keyword(self):
        """添加关键词"""
        text, ok = QInputDialog.getText(self, "添加关键词", "请输入关键词:")
        if ok and text:
            self.keywords_list.addItem(text)
            self.update_current_category_data()
            self.on_config_changed()

    def remove_keyword(self):
        """删除关键词"""
        current_row = self.keywords_list.currentRow()
        if current_row >= 0:
            self.keywords_list.takeItem(current_row)
            self.update_current_category_data()
            self.on_config_changed()

    def add_advanced_keyword(self):
        """添加高级支持关键词"""
        text, ok = QInputDialog.getText(self, "添加关键词", "请输入高级支持关键词:")
        if ok and text.strip():
            self.advanced_keywords_list.addItem(text.strip())
            self.update_current_advanced_category_data()
            self.on_config_changed()

    def remove_advanced_keyword(self):
        """删除高级支持关键词"""
        current_row = self.advanced_keywords_list.currentRow()
        if current_row >= 0:
            self.advanced_keywords_list.takeItem(current_row)
            self.update_current_advanced_category_data()
            self.on_config_changed()

    def add_advanced_category(self):
        """添加高级支持类别"""
        text, ok = QInputDialog.getText(self, "添加高级支持类别", "请输入类别名称:")
        if ok and text.strip():
            category_key = text.strip().lower().replace(' ', '_')
            display_name = text.strip()

            # 创建新的类别数据
            category_data = {
                'keywords': [],
                'trigger_message': f"I understand you're experiencing {display_name.lower()}. Let me collect some information to help you better."
            }

            # 添加到列表
            item = QListWidgetItem(display_name)
            item.setData(Qt.ItemDataRole.UserRole, category_key)
            item.setData(Qt.ItemDataRole.UserRole + 1, category_data)
            self.advanced_categories_list.addItem(item)

            # 选中新添加的项
            self.advanced_categories_list.setCurrentItem(item)
            self.on_config_changed()

    def remove_advanced_category(self):
        """删除高级支持类别"""
        current_row = self.advanced_categories_list.currentRow()
        if current_row >= 0:
            self.advanced_categories_list.takeItem(current_row)
            self.on_config_changed()

    def update_current_advanced_category_data(self):
        """更新当前选中的高级支持类别数据"""
        current_item = self.advanced_categories_list.currentItem()
        if current_item:
            category_key = current_item.data(Qt.ItemDataRole.UserRole)
            if category_key:
                print(f"🔧 更新高级支持类别数据: {category_key}")

                # 🔧 修复：直接从UI控件读取数据，包括类别名称
                category_name = self.advanced_category_name_edit.text()
                print(f"  类别名称: '{category_name}' (控件: {type(self.advanced_category_name_edit).__name__})")

                # 收集关键词
                keywords = []
                for i in range(self.advanced_keywords_list.count()):
                    keywords.append(self.advanced_keywords_list.item(i).text())
                print(f"  关键词: {len(keywords)} 项")

                # 获取触发消息
                trigger_message = self.advanced_trigger_message_edit.toPlainText()
                print(f"  触发消息: {len(trigger_message)} 字符")

                # 🔧 修复：构建完整的类别数据
                updated_category_data = {
                    'name': category_name,
                    'keywords': keywords,
                    'trigger_message': trigger_message
                }

                # 🔧 修复：同时更新UserRole数据和config_data
                current_item.setData(Qt.ItemDataRole.UserRole + 1, updated_category_data)

                # 更新config_data中的对应数据
                if not hasattr(self, 'config_data'):
                    self.config_data = {}
                if 'advanced_support' not in self.config_data:
                    self.config_data['advanced_support'] = {}
                if 'categories' not in self.config_data['advanced_support']:
                    self.config_data['advanced_support']['categories'] = {}

                self.config_data['advanced_support']['categories'][category_key] = updated_category_data.copy()

                print(f"  ✅ 高级类别数据已同步到UserRole和config_data")
                print(f"  📝 最终数据: name='{category_name}', keywords={len(keywords)}, trigger_message={len(trigger_message)} 字符")

                # 🔧 修复：更新列表控件的显示文本
                current_item.setText(category_name)

                # 🔧 移除信号发射，避免循环调用
                # 信号发射由调用者负责





    def update_current_advanced_category_data_silent(self):
        """更新当前选中的高级支持类别数据（不触发信号）"""
        current_item = self.advanced_categories_list.currentItem()
        if current_item:
            category_key = current_item.data(Qt.ItemDataRole.UserRole)
            if category_key:
                # 直接从UI控件读取数据，包括类别名称
                category_name = self.advanced_category_name_edit.text()

                # 收集关键词
                keywords = []
                for i in range(self.advanced_keywords_list.count()):
                    keywords.append(self.advanced_keywords_list.item(i).text())

                # 获取触发消息
                trigger_message = self.advanced_trigger_message_edit.toPlainText()

                # 构建完整的类别数据
                updated_category_data = {
                    'name': category_name,
                    'keywords': keywords,
                    'trigger_message': trigger_message
                }

                # 同时更新UserRole数据和config_data
                current_item.setData(Qt.ItemDataRole.UserRole + 1, updated_category_data)

                # 更新config_data中的对应数据
                if not hasattr(self, 'config_data'):
                    self.config_data = {}
                if 'advanced_support' not in self.config_data:
                    self.config_data['advanced_support'] = {}
                if 'categories' not in self.config_data['advanced_support']:
                    self.config_data['advanced_support']['categories'] = {}

                self.config_data['advanced_support']['categories'][category_key] = updated_category_data.copy()

                # 更新列表控件的显示文本
                current_item.setText(category_name)
    def update_current_category_data(self):
        """更新当前选中类别的数据"""
        current = self.categories_tree.currentItem()
        if current:
            category_key = current.data(0, Qt.ItemDataRole.UserRole)
            if category_key:
                print(f"🔧 更新基础支持类别数据: {category_key}")

                # 🔧 修复：直接从UI控件读取数据，包括类别名称
                category_name = self.category_name_edit.text()
                print(f"  类别名称: '{category_name}' (控件: {type(self.category_name_edit).__name__})")

                keywords = []
                for i in range(self.keywords_list.count()):
                    keywords.append(self.keywords_list.item(i).text())
                print(f"  关键词: {len(keywords)} 项")

                # 收集解决方案内容
                solution_content = self.solution_content_edit.toPlainText()
                print(f"  解决方案内容: {len(solution_content)} 字符")

                # 🔧 修复：构建完整的类别数据，包括名称
                updated_category_data = {
                    'name': category_name,  # 添加名称字段
                    'keywords': keywords
                }

                if solution_content:
                    # 将解决方案内容保存到knowledge_base字段
                    updated_category_data['knowledge_base'] = {
                        'solution': solution_content.split('\n')
                    }

                # 🔧 修复：同时更新UserRole数据和config_data
                current.setData(1, Qt.ItemDataRole.UserRole, updated_category_data)

                # 更新config_data中的对应数据
                if not hasattr(self, 'config_data'):
                    self.config_data = {}
                if 'basic_support' not in self.config_data:
                    self.config_data['basic_support'] = {}
                if 'categories' not in self.config_data['basic_support']:
                    self.config_data['basic_support']['categories'] = {}

                self.config_data['basic_support']['categories'][category_key] = updated_category_data.copy()

                print(f"  ✅ 类别数据已同步到UserRole和config_data")
                print(f"  📝 最终数据: name='{category_name}', keywords={len(keywords)}, solution={len(solution_content)} 字符")

                # 🔧 修复：更新树形控件的显示文本
                current.setText(0, category_name)  # 更新显示的类别名称
                current.setText(1, str(len(keywords)))

                # 🔧 移除信号发射，避免循环调用
                # 信号发射由调用者负责

    def load_config(self, config_data):
        """加载配置数据"""
        self.config_data = config_data

        print(f"🔍 售后支持模块收到的配置数据键: {list(config_data.keys()) if config_data else '无数据'}")

        # 🔧 统一为Agent模式：直接加载，依赖current_config_loaded保护
        # 加载基础支持配置
        basic_support = config_data.get('basic_support', {})
        print(f"🔍 基础支持配置: {bool(basic_support)}, 包含键: {list(basic_support.keys()) if basic_support else '无数据'}")
        if basic_support:
            # AI agent默认处理基础支持，无需设置开关
            # 直接加载支持类别到基础支持标签页的categories_tree
            categories = basic_support.get('categories', {})
            if hasattr(self, 'categories_tree'):
                self.categories_tree.clear()

                for category_key, category_info in categories.items():
                    if isinstance(category_info, dict):
                        keywords = category_info.get('keywords', [])

                        # 🔧 修复：优先使用保存的name字段，如果没有则使用key的友好显示名称
                        saved_name = category_info.get('name', '')
                        if saved_name:
                            display_name = saved_name
                        else:
                            display_name = category_key.replace('_', ' ').title()

                        item = QTreeWidgetItem([
                            display_name,
                            str(len(keywords)),
                            "启用"
                        ])
                        item.setData(0, Qt.ItemDataRole.UserRole, category_key)
                        item.setData(1, Qt.ItemDataRole.UserRole, category_info)
                        self.categories_tree.addTopLevelItem(item)

                print(f"✅ 已加载 {len(categories)} 个基础支持类别")

        # 加载高级支持配置
        advanced_support = config_data.get('advanced_support', {})
        print(f"🔍 高级支持配置: {bool(advanced_support)}, 包含键: {list(advanced_support.keys()) if advanced_support else '无数据'}")
        if advanced_support:
            # AI agent默认处理高级支持，无需设置开关
            # 直接加载高级支持类别到advanced_categories_list
            categories = advanced_support.get('categories', {})
            if hasattr(self, 'advanced_categories_list'):
                self.advanced_categories_list.clear()

                for category_key, category_info in categories.items():
                    if isinstance(category_info, dict):
                        # 🔧 修复：优先使用保存的name字段，如果没有则使用key的友好显示名称
                        saved_name = category_info.get('name', '')
                        if saved_name:
                            display_name = saved_name
                        else:
                            display_name = category_key.replace('_', ' ').title()

                        item = QListWidgetItem(display_name)
                        item.setData(Qt.ItemDataRole.UserRole, category_key)
                        item.setData(Qt.ItemDataRole.UserRole + 1, category_info)
                        self.advanced_categories_list.addItem(item)

                print(f"✅ 已加载 {len(categories)} 个高级支持类别")

            # 加载表单字段配置到form_fields_list
            form_fields = advanced_support.get('form_fields', {})
            if hasattr(self, 'form_fields_list'):
                self.form_fields_list.clear()

                for field_key, field_info in form_fields.items():
                    if isinstance(field_info, dict):
                        # 🔧 修复：优先使用保存的name字段，如果没有则使用field_key
                        saved_name = field_info.get('name', '')
                        if saved_name:
                            field_name = saved_name
                        else:
                            field_name = field_key

                        label = field_info.get('label', '')
                        field_type = field_info.get('type', '')
                        required = field_info.get('required', False)

                        # 🔧 修复：显示文本使用字段名称而不是标签
                        display_text = f"{field_name} ({field_type})" + (" *" if required else "")
                        item = QListWidgetItem(display_text)

                        # 添加field_key到field_info中，方便后续使用
                        field_info_with_key = field_info.copy()
                        field_info_with_key['name'] = field_name  # 使用正确的字段名称

                        item.setData(Qt.ItemDataRole.UserRole, field_key)
                        item.setData(Qt.ItemDataRole.UserRole + 1, field_info_with_key)
                        self.form_fields_list.addItem(item)

                print(f"✅ 已加载 {len(form_fields)} 个表单字段")

        # 注意：升级规则配置在高级设置面板中处理，不在此处加载

        print("✅ 售后支持配置加载完成")

    def get_config(self):
        """获取当前配置"""
        # 强制让所有控件失去焦点，确保内容被提交
        self.setFocus()

        # 保存当前编辑的数据
        self.update_current_category_data()
        self.update_current_field_data()
        self.update_current_advanced_category_data()

        config = {}

        # 收集基础支持配置
        basic_support = {}
        # AI agent默认处理基础支持，无需设置开关
        # 只收集支持类别配置

        # 🔧 重大修复：改为直接从UI控件读取数据，避免UserRole数据同步问题
        # 收集基础支持类别 - 直接从当前选中的UI控件读取
        basic_categories = {}
        if hasattr(self, 'categories_tree'):
            # 先保存当前编辑的数据
            self.update_current_category_data()

            for i in range(self.categories_tree.topLevelItemCount()):
                item = self.categories_tree.topLevelItem(i)
                category_key = item.data(0, Qt.ItemDataRole.UserRole)
                category_data = item.data(1, Qt.ItemDataRole.UserRole)
                if category_key and category_data:
                    basic_categories[category_key] = category_data

        if basic_categories:
            basic_support['categories'] = basic_categories

        config['basic_support'] = basic_support

        # 收集高级支持配置
        advanced_support = {}
        # AI agent默认处理高级支持，无需设置开关
        # 只收集支持类别配置

        # 收集高级支持类别
        advanced_categories = {}
        if hasattr(self, 'advanced_categories_list'):
            for i in range(self.advanced_categories_list.count()):
                item = self.advanced_categories_list.item(i)
                category_key = item.data(Qt.ItemDataRole.UserRole)
                category_data = item.data(Qt.ItemDataRole.UserRole + 1)
                if category_key and category_data:
                    # 🔧 修复：直接使用UI中的最新数据，不再进行数据对比
                    # 因为update_current_advanced_category_data()已经确保了数据同步
                    advanced_categories[category_key] = category_data

        if advanced_categories:
            advanced_support['categories'] = advanced_categories

        # 收集表单字段
        form_fields = {}
        if hasattr(self, 'form_fields_list'):
            for i in range(self.form_fields_list.count()):
                item = self.form_fields_list.item(i)
                field_key = item.data(Qt.ItemDataRole.UserRole)
                field_data = item.data(Qt.ItemDataRole.UserRole + 1)
                if field_key and field_data:
                    form_fields[field_key] = field_data

        if form_fields:
            advanced_support['form_fields'] = form_fields

        config['advanced_support'] = advanced_support

        return config

    def on_form_field_selected(self, current, previous):
        """选择表单字段时的处理"""
        try:
            print(f"🖱️ 用户切换表单字段")

            # 🔧 统一为Agent模式：只有在配置已加载完成后才保存之前的数据
            if previous and hasattr(self.parent(), 'current_config_loaded') and self.parent().current_config_loaded:
                print(f"  保存之前选中的字段数据: {previous.text()}")
                # 临时设置当前项为previous，以便update_current_field_data能正确工作
                self.form_fields_list.setCurrentItem(previous)
                self.update_current_field_data()
                # 恢复当前项
                self.form_fields_list.setCurrentItem(current)

            if current:
                field_key = current.data(Qt.ItemDataRole.UserRole)
                field_data = current.data(Qt.ItemDataRole.UserRole + 1)
                print(f"🔄 当前选中项: {current.text()}")
                print(f"  键值: {field_key}")
                print(f"  数据: {field_data is not None}")

                if field_data:
                    # 🔧 修复：优先使用保存的name字段，如果没有则使用field_key
                    saved_name = field_data.get('name', '')
                    if saved_name:
                        field_name = saved_name
                    else:
                        field_name = field_key or ''
                    self.field_name_edit.setText(field_name)
                    print(f"  加载字段名称: {field_name}")

                    self.field_label_edit.setText(field_data.get('label', ''))

                    # 设置字段类型
                    field_type = field_data.get('type', 'text')
                    index = self.field_type_combo.findText(field_type)
                    if index >= 0:
                        self.field_type_combo.setCurrentIndex(index)

                    # 设置其他属性
                    self.field_placeholder_edit.setText(field_data.get('placeholder', ''))
                    self.field_validation_edit.setText(field_data.get('validation', ''))
                    self.field_error_edit.setText(field_data.get('error_message', ''))

                    # 加载选项（如果是选择类型）
                    self.field_options_list.clear()
                    options = field_data.get('options', [])
                    for option in options:
                        self.field_options_list.addItem(option)
                    print(f"  ✅ 表单字段数据加载完成")
                else:
                    print(f"  ❌ 无法获取字段数据")
                    # 清空显示
                    self.field_name_edit.clear()
                    self.field_label_edit.clear()
                    self.field_type_combo.setCurrentIndex(0)
                    self.field_placeholder_edit.clear()
                    self.field_validation_edit.clear()
                    self.field_error_edit.clear()
                    self.field_options_list.clear()
            else:
                print(f"  ❌ 没有选中的字段")
        except Exception as e:
            print(f"❌ 表单字段选择处理失败: {e}")
            import traceback
            traceback.print_exc()

    def add_form_field(self):
        """添加表单字段"""
        text, ok = QInputDialog.getText(self, "添加表单字段", "请输入字段名称:")
        if ok and text.strip():
            field_key = text.strip().lower().replace(' ', '_')
            display_name = text.strip()

            field_data = {
                "label": display_name,
                "type": "text",
                "required": False,
                "placeholder": "",
                "validation": "",
                "error_message": "",
                "options": []
            }

            # 添加到列表
            display_text = f"{display_name} (text)"
            item = QListWidgetItem(display_text)
            item.setData(Qt.ItemDataRole.UserRole, field_key)
            item.setData(Qt.ItemDataRole.UserRole + 1, field_data)
            self.form_fields_list.addItem(item)

            # 选中新添加的项
            self.form_fields_list.setCurrentItem(item)
            self.on_config_changed()

    def remove_form_field(self):
        """删除表单字段"""
        current_item = self.form_fields_list.currentItem()
        if current_item:
            self.form_fields_list.takeItem(self.form_fields_list.row(current_item))
            self.on_config_changed()

    def add_field_option(self):
        """添加字段选项"""
        text, ok = QInputDialog.getText(self, "添加选项", "请输入选项值:")
        if ok and text.strip():
            self.field_options_list.addItem(text.strip())
            self.update_current_field_data()
            self.on_config_changed()

    def remove_field_option(self):
        """删除字段选项"""
        current_row = self.field_options_list.currentRow()
        if current_row >= 0:
            self.field_options_list.takeItem(current_row)
            self.update_current_field_data()
            self.on_config_changed()

    def update_current_field_data(self):
        """更新当前选中字段的数据"""
        current_item = self.form_fields_list.currentItem()
        if current_item:
            field_key = current_item.data(Qt.ItemDataRole.UserRole)
            if field_key:
                print(f"🔧 更新表单字段数据: {field_key}")

                # 🔧 修复：直接从UI控件读取数据
                field_name = self.field_name_edit.text()
                field_label = self.field_label_edit.text()
                field_type = self.field_type_combo.currentText()
                field_placeholder = self.field_placeholder_edit.text()
                field_validation = self.field_validation_edit.text()
                field_error = self.field_error_edit.text()

                # 收集选项
                options = []
                for i in range(self.field_options_list.count()):
                    options.append(self.field_options_list.item(i).text())

                print(f"  字段信息: name='{field_name}', label='{field_label}', type='{field_type}', options={len(options)}")

                # 构建完整的字段数据
                updated_field_data = {
                    'name': field_name,
                    'label': field_label,
                    'type': field_type,
                    'placeholder': field_placeholder,
                    'validation': field_validation,
                    'error_message': field_error,
                    'options': options,
                    'required': False  # 默认值
                }

                # 🔧 修复：同时更新UserRole数据和config_data
                current_item.setData(Qt.ItemDataRole.UserRole + 1, updated_field_data)

                # 更新config_data中的对应数据
                if not hasattr(self, 'config_data'):
                    self.config_data = {}
                if 'advanced_support' not in self.config_data:
                    self.config_data['advanced_support'] = {}
                if 'form_fields' not in self.config_data['advanced_support']:
                    self.config_data['advanced_support']['form_fields'] = {}

                self.config_data['advanced_support']['form_fields'][field_key] = updated_field_data.copy()

                print(f"  ✅ 字段数据已同步到UserRole和config_data")

                # 🔧 修复：更新显示文本，使用字段名称而不是标签
                required = updated_field_data.get('required', False)
                display_text = f"{field_name} ({field_type})" + (" *" if required else "")
                current_item.setText(display_text)

                # 触发配置变更信号
                self.config_changed.emit()

    def update_current_field_data_silent(self):
        """更新当前选中字段的数据（不触发信号）"""
        current_item = self.form_fields_list.currentItem()
        if current_item:
            field_key = current_item.data(Qt.ItemDataRole.UserRole)
            if field_key:
                # 直接从UI控件读取数据
                field_name = self.field_name_edit.text()
                field_label = self.field_label_edit.text()
                field_type = self.field_type_combo.currentText()
                field_placeholder = self.field_placeholder_edit.text()
                field_validation = self.field_validation_edit.text()
                field_error = self.field_error_edit.text()

                # 收集选项
                options = []
                for i in range(self.field_options_list.count()):
                    options.append(self.field_options_list.item(i).text())

                # 构建完整的字段数据
                updated_field_data = {
                    'name': field_name,
                    'label': field_label,
                    'type': field_type,
                    'placeholder': field_placeholder,
                    'validation': field_validation,
                    'error_message': field_error,
                    'options': options,
                    'required': False  # 默认值
                }

                # 更新UserRole数据和config_data
                current_item.setData(Qt.ItemDataRole.UserRole + 1, updated_field_data)

                # 更新config_data中的对应数据
                if not hasattr(self, 'config_data'):
                    self.config_data = {}
                if 'advanced_support' not in self.config_data:
                    self.config_data['advanced_support'] = {}
                if 'form_fields' not in self.config_data['advanced_support']:
                    self.config_data['advanced_support']['form_fields'] = {}

                self.config_data['advanced_support']['form_fields'][field_key] = updated_field_data.copy()

                # 🔧 修复：更新显示文本，使用字段名称而不是标签
                required = updated_field_data.get('required', False)
                display_text = f"{field_name} ({field_type})" + (" *" if required else "")
                current_item.setText(display_text)

    def on_config_changed(self):
        """配置改变时的处理"""
        print(f"📝 售后模块配置发生变化，触发保存")
        # 🔧 修复：更新所有当前数据（不触发额外的信号）
        # 只有在配置已加载完成后才处理数据变更
        if hasattr(self.parent(), 'current_config_loaded') and self.parent().current_config_loaded:
            self.update_current_category_data()
            self.update_current_advanced_category_data_silent()
            self.update_current_field_data_silent()
        self.config_changed.emit()
        print(f"✅ 售后模块配置变化处理完成")


