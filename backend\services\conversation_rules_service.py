"""
对话规则服务
实现话题相关性检查、对话轮次限制等功能
"""

import json
import os
import re
from typing import Dict, Any, Tuple, List
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


class ConversationRulesService:
    """对话规则服务"""
    
    def __init__(self):
        self.config = {}
        self.session_data = {}  # 存储会话数据
        self.load_config()
    
    def load_config(self):
        """加载对话规则配置"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), '..', 'salesgpt_config', 'advanced_sales_config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                full_config = json.load(f)
                self.config = full_config.get('conversation_rules', {})
            logger.info("对话规则配置加载成功")
        except Exception as e:
            logger.error(f"加载对话规则配置失败: {e}")
            self.config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置（移除硬编码，返回基本配置）"""
        logger.warning("对话规则配置文件加载失败，使用基本配置。请检查配置文件是否存在且格式正确。")
        return {
            "stay_on_topic": False,
            "max_conversation_turns": 50,
            "session_timeout_minutes": 30,
            "off_topic_redirect": "I can help you with product-related questions.",
            "allowed_topics": [],
            "forbidden_topics": []
        }
    
    def check_topic_relevance(self, message: str, session_id: str) -> Tuple[bool, str]:
        """检查消息是否与主题相关"""
        if not self.config.get('stay_on_topic', True):
            return True, ""

        # 从配置文件读取相关主题关键词
        relevant_keywords = self.config.get('allowed_topics', [])

        # 从配置文件读取禁止的主题关键词
        forbidden_keywords = self.config.get('forbidden_topics', [])

        message_lower = message.lower()

        # 检查是否包含相关关键词
        has_relevant_keywords = any(keyword.lower() in message_lower for keyword in relevant_keywords)

        # 检查是否包含禁止的主题关键词
        has_forbidden_keywords = any(keyword.lower() in message_lower for keyword in forbidden_keywords)

        # 如果包含禁止关键词且没有相关关键词，返回重定向消息
        if has_forbidden_keywords and not has_relevant_keywords:
            # 尝试提取主题
            topic = self._extract_topic(message)
            redirect_message = self.config.get('off_topic_redirect', '').format(topic=topic)
            return False, redirect_message
        
        return True, ""
    
    def _extract_topic(self, message: str) -> str:
        """从消息中提取主题"""
        # 使用配置文件中的禁止话题关键词来识别主题
        forbidden_keywords = self.config.get('forbidden_topics', [])
        message_lower = message.lower()

        # 查找匹配的禁止话题关键词
        for keyword in forbidden_keywords:
            if keyword.lower() in message_lower:
                return keyword

        return "that topic"
    
    async def check_conversation_limits(self, session_id: str) -> Tuple[bool, str]:
        """检查对话限制"""
        if session_id not in self.session_data:
            self.session_data[session_id] = {
                'turn_count': 0,
                'start_time': datetime.now(),
                'last_activity': datetime.now()
            }
        
        session = self.session_data[session_id]
        session['turn_count'] += 1
        session['last_activity'] = datetime.now()
        
        # 检查对话轮次限制
        max_turns = self.config.get('max_conversation_turns', 50)
        if session['turn_count'] > max_turns:
            # 🔧 先导出会话记录到CSV
            await self._export_session_before_cleanup(session_id, "max_turns_reached")

            # 从配置文件读取提示消息模板
            message_template = self.config.get('max_turns_message',
                "This conversation has reached the maximum limit of {max_turns} turns. Please refresh the page to start a new conversation if you need further assistance.")
            message = message_template.format(max_turns=max_turns)
            # 🔧 自动清理过期session数据
            await self._cleanup_session(session_id)
            return False, message

        # 检查会话超时
        timeout_minutes = self.config.get('session_timeout_minutes', 30)
        if datetime.now() - session['start_time'] > timedelta(minutes=timeout_minutes):
            # 🔧 先导出会话记录到CSV
            await self._export_session_before_cleanup(session_id, "session_timeout")

            # 从配置文件读取提示消息模板
            message_template = self.config.get('timeout_message',
                "This conversation session has timed out after {timeout_minutes} minutes. Please refresh the page to start a new conversation.")
            message = message_template.format(timeout_minutes=timeout_minutes)
            # 🔧 自动清理过期session数据
            await self._cleanup_session(session_id)
            return False, message
        
        return True, ""

    async def _cleanup_session(self, session_id: str):
        """清理过期的session数据"""
        try:
            # 🔧 先清空数据库记录
            if hasattr(self, 'salesgpt_service') and self.salesgpt_service:
                await self.salesgpt_service.clear_session_from_database(session_id)

            # 清理内存中的session数据
            if session_id in self.session_data:
                logger.info(f"Cleaning up expired session: {session_id}")
                del self.session_data[session_id]
        except Exception as e:
            logger.error(f"Error cleaning up session {session_id}: {e}")

    def set_salesgpt_service(self, salesgpt_service):
        """设置SalesGPT服务实例，用于导出CSV"""
        self.salesgpt_service = salesgpt_service

    async def _export_session_before_cleanup(self, session_id: str, reason: str):
        """在清理session前导出会话记录"""
        try:
            if hasattr(self, 'salesgpt_service') and self.salesgpt_service:
                filepath = await self.salesgpt_service.export_session_to_csv(session_id, reason)
                if filepath:
                    logger.info(f"Session {session_id} exported before cleanup: {filepath}")
                else:
                    logger.warning(f"Failed to export session {session_id} before cleanup")
            else:
                logger.warning(f"SalesGPT service not available for exporting session {session_id}")
        except Exception as e:
            logger.error(f"Error exporting session {session_id} before cleanup: {e}")
    
    def get_conversation_memory_config(self) -> Dict[str, Any]:
        """获取对话记忆配置"""
        return self.config.get('conversation_memory', {})
    
    def should_include_context(self) -> bool:
        """是否应该包含上下文"""
        memory_config = self.get_conversation_memory_config()
        short_term = memory_config.get('short_term', {})
        return short_term.get('enabled', True) and short_term.get('include_context', True)
    
    def get_max_context_turns(self) -> int:
        """获取最大上下文轮次"""
        memory_config = self.get_conversation_memory_config()
        short_term = memory_config.get('short_term', {})
        return short_term.get('max_turns', 20)
    
    def cleanup_expired_sessions(self):
        """清理过期会话"""
        timeout_minutes = self.config.get('session_timeout_minutes', 30)
        cutoff_time = datetime.now() - timedelta(minutes=timeout_minutes)
        
        expired_sessions = [
            session_id for session_id, data in self.session_data.items()
            if data['last_activity'] < cutoff_time
        ]
        
        for session_id in expired_sessions:
            del self.session_data[session_id]
        
        if expired_sessions:
            logger.info(f"清理了 {len(expired_sessions)} 个过期会话")


# 全局实例
conversation_rules_service = ConversationRulesService()
