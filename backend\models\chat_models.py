from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime
from sqlalchemy import Column, String, DateTime, Boolean, Text, Integer, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()

class ChatSession(Base):
    __tablename__ = "chat_sessions"

    id = Column(String, primary_key=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    admin_control = Column(Boolean, default=False)
    admin_control_at = Column(DateTime, nullable=True)  # 管理员接管时间
    admin_control_by = Column(String, nullable=True)    # 接管的管理员ID
    user_ip = Column(String, nullable=True)
    user_agent = Column(String, nullable=True)
    archived = Column(Boolean, default=False)  # 归档状态
    archived_at = Column(DateTime, nullable=True)  # 归档时间
    archived_by = Column(String, nullable=True)  # 归档的管理员ID

class ChatMessage(Base):
    __tablename__ = "chat_messages"

    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    sender = Column(String, nullable=False)  # 'user', 'ai', 'admin'
    admin_id = Column(String, nullable=True)
    created_at = Column(DateTime, default=func.now())

class SessionReadStatus(Base):
    __tablename__ = "session_read_status"

    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String, nullable=False)
    admin_id = Column(String, nullable=False)
    read_at = Column(DateTime, default=func.now())

    # 确保每个管理员对每个会话只有一条已读记录
    __table_args__ = (
        {'sqlite_autoincrement': True},
    )

class FormSubmission(Base):
    __tablename__ = "form_submissions"

    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String, nullable=False)
    category = Column(String, nullable=False)  # technical_malfunction, replacement_request, etc.
    form_data = Column(JSON, nullable=False)  # 完整的表单数据
    status = Column(Integer, default=0)  # 0=未处理, 1=已处理
    priority = Column(String, default='medium')  # high, medium, low
    customer_email = Column(String, nullable=True)  # 便于查询
    customer_order = Column(String, nullable=True)  # 订单号，便于查询
    submitted_at = Column(DateTime, default=func.now())
    processed_at = Column(DateTime, nullable=True)
    processed_by = Column(String, nullable=True)  # 处理的客服ID
    notes = Column(Text, nullable=True)  # 客服处理备注

class UserPreference(Base):
    __tablename__ = "user_preferences"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String, unique=True, nullable=False)  # 用户标识（session_id的hash或用户ID）
    profile = Column(JSON, default=dict)  # 用户基本偏好
    interests = Column(JSON, default=dict)  # 兴趣和产品偏好
    history = Column(JSON, default=dict)  # 历史行为记录
    learning_data = Column(JSON, default=dict)  # 学习统计数据
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

# Pydantic models for API
class UserMessage(BaseModel):
    content: str
    session_id: str

class ChatMessageResponse(BaseModel):
    id: int
    session_id: str
    content: str
    sender: str
    admin_id: Optional[str] = None
    created_at: datetime

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class ChatSessionResponse(BaseModel):
    id: str
    created_at: datetime
    updated_at: datetime
    admin_control: bool
    admin_control_at: Optional[datetime] = None
    admin_control_by: Optional[str] = None
    user_ip: Optional[str] = None
    user_agent: Optional[str] = None
    archived: bool = False
    archived_at: Optional[datetime] = None
    archived_by: Optional[str] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class AdminMessage(BaseModel):
    session_id: str
    content: str
    admin_id: str

class MarkReadRequest(BaseModel):
    admin_id: str

class SessionReadStatusResponse(BaseModel):
    id: int
    session_id: str
    admin_id: str
    read_at: datetime

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class FormSubmissionResponse(BaseModel):
    id: int
    session_id: str
    category: str
    form_data: Dict[str, Any]
    status: int
    priority: str
    customer_email: Optional[str] = None
    customer_order: Optional[str] = None
    submitted_at: datetime
    processed_at: Optional[datetime] = None
    processed_by: Optional[str] = None
    notes: Optional[str] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class FormSubmissionUpdateRequest(BaseModel):
    status: Optional[int] = None
    notes: Optional[str] = None
    processed_by: Optional[str] = None

class UserPreferenceResponse(BaseModel):
    id: int
    user_id: str
    profile: Dict[str, Any] = {}
    interests: Dict[str, Any] = {}
    history: Dict[str, Any] = {}
    learning_data: Dict[str, Any] = {}
    created_at: datetime
    updated_at: datetime

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
