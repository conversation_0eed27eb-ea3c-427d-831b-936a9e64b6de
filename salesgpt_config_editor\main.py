#!/usr/bin/env python3
"""
杨杋agent 配置管理器
主程序入口

使用方法:
python main.py

功能特点:
- 可视化配置编辑
- 实时配置验证  
- 文件历史记录
- 模板和向导
- 中文界面支持
"""

import sys
import os
import traceback
import logging
from datetime import datetime
from pathlib import Path

# 设置日志记录
def setup_logging():
    """设置日志记录"""
    try:
        # 确定日志文件路径
        if getattr(sys, 'frozen', False):
            # 打包后的可执行文件
            log_dir = Path(sys.executable).parent
        else:
            # 源码运行
            log_dir = Path(__file__).parent

        log_file = log_dir / f"debug_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

        # 配置日志
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )

        logging.info(f"日志文件: {log_file}")
        logging.info(f"Python版本: {sys.version}")
        logging.info(f"平台: {sys.platform}")
        logging.info(f"是否打包: {getattr(sys, 'frozen', False)}")

        return log_file
    except Exception as e:
        print(f"设置日志失败: {e}")
        return None

# 设置日志
log_file = setup_logging()

# 添加当前目录到Python路径
# 处理PyInstaller打包后的路径问题
try:
    logging.info("开始设置Python路径...")

    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        current_dir = Path(sys.executable).parent
        logging.info(f"打包环境 - 可执行文件目录: {current_dir}")

        # 添加临时解压目录到路径
        if hasattr(sys, '_MEIPASS'):
            sys.path.insert(0, sys._MEIPASS)
            logging.info(f"添加临时目录到路径: {sys._MEIPASS}")
    else:
        # 如果是源码运行
        current_dir = Path(__file__).parent
        logging.info(f"源码环境 - 脚本目录: {current_dir}")

    sys.path.insert(0, str(current_dir))
    logging.info(f"当前Python路径: {sys.path[:5]}")  # 只显示前5个
    logging.info("Python路径设置完成")

except Exception as e:
    logging.error(f"设置Python路径失败: {e}")
    logging.error(traceback.format_exc())

try:
    logging.info("开始导入PyQt6...")
    from PyQt6.QtWidgets import QApplication, QMessageBox, QSplashScreen
    logging.info("✅ PyQt6.QtWidgets 导入成功")

    from PyQt6.QtCore import Qt, QTimer
    logging.info("✅ PyQt6.QtCore 导入成功")

    from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor
    logging.info("✅ PyQt6.QtGui 导入成功")

except ImportError as e:
    error_msg = f"错误: 未找到PyQt6库 - {e}"
    logging.error(error_msg)
    logging.error(traceback.format_exc())
    print(error_msg)
    print("请运行以下命令安装依赖:")
    print("pip install PyQt6")
    sys.exit(1)

# 导入主窗口模块
try:
    logging.info("开始导入UI模块...")
    from ui import MainWindow
    logging.info("✅ 主窗口模块导入成功")
except ImportError as e:
    error_msg = f"❌ 导入主窗口模块失败: {e}"
    logging.error(error_msg)
    logging.error(traceback.format_exc())
    print(error_msg)
    print("请检查文件是否存在:")
    print(f"  - {current_dir}/ui/main_window.py")
    print(f"  - {current_dir}/ui/__init__.py")
    sys.exit(1)


class SplashScreen(QSplashScreen):
    """启动画面"""
    
    def __init__(self):
        # 创建一个简单的启动画面
        pixmap = QPixmap(400, 300)
        pixmap.fill(QColor(255, 255, 255))
        
        painter = QPainter(pixmap)
        painter.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        painter.setPen(QColor(33, 150, 243))
        
        # 绘制标题
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, 
                        "杨杋agent 配置管理器\n\n正在启动...")
        
        painter.end()
        
        super().__init__(pixmap)
        self.setWindowFlag(Qt.WindowType.WindowStaysOnTopHint)


def check_dependencies():
    """检查依赖项"""
    missing_deps = []
    
    try:
        import PyQt6  # noqa: F401
    except ImportError:
        missing_deps.append("PyQt6")
    
    if missing_deps:
        print("错误: 缺少以下依赖项:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\n请运行以下命令安装:")
        print("pip install -r requirements.txt")
        return False
    
    return True


def setup_application():
    """设置应用程序"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("SalesGPT Config Editor")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("SalesGPT")
    app.setOrganizationDomain("salesgpt.com")
    
    # 设置应用程序图标（如果有的话）
    # app.setWindowIcon(QIcon("resources/icon.png"))
    
    # 设置全局字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 设置全局样式
    app.setStyleSheet("""
        QApplication {
            font-family: "Microsoft YaHei";
        }
        QToolTip {
            background-color: #2b2b2b;
            color: white;
            border: 1px solid #555;
            padding: 5px;
            border-radius: 3px;
        }
    """)
    
    return app


def main():
    """主函数"""
    try:
        logging.info("=" * 50)
        logging.info("SalesGPT 配置管理器 v1.0 启动")
        logging.info("=" * 50)

        print("SalesGPT 配置管理器 v1.0")
        print("=" * 40)

        # 检查依赖项
        logging.info("检查依赖项...")
        if not check_dependencies():
            logging.error("依赖项检查失败")
            return 1
        logging.info("依赖项检查通过")

        # 创建应用程序
        logging.info("创建Qt应用程序...")
        app = setup_application()
        logging.info("Qt应用程序创建成功")

        # 显示启动画面
        logging.info("显示启动画面...")
        splash = SplashScreen()
        splash.show()
        logging.info("启动画面显示成功")

        # 处理启动画面显示
        app.processEvents()

        # 创建主窗口
        logging.info("创建主窗口...")
        splash.showMessage("正在初始化界面...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter)
        app.processEvents()

        main_window = MainWindow()
        logging.info("主窗口创建成功")

        splash.showMessage("正在加载配置...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter)
        app.processEvents()

        # 延迟显示主窗口
        def show_main_window():
            splash.finish(main_window)
            main_window.show()
            logging.info("主窗口显示成功")

        QTimer.singleShot(1500, show_main_window)  # 1.5秒后显示主窗口

        print("✅ 应用程序启动成功")
        print("📝 使用说明:")
        print("   1. 点击'文件管理'选择配置文件")
        print("   2. 在各个配置面板中编辑设置")
        print("   3. 使用Ctrl+S保存配置")
        print("   4. 使用F5验证配置")
        print()

        logging.info("开始运行应用程序主循环...")
        # 运行应用程序
        result = app.exec()
        logging.info(f"应用程序退出，返回码: {result}")
        return result

    except Exception as e:
        error_msg = f"程序异常: {e}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())

        try:
            if 'splash' in locals():
                splash.close()
            QMessageBox.critical(None, "启动错误", error_msg)
        except:
            pass  # 如果连错误对话框都无法显示，就忽略

        print(error_msg)
        return 1


if __name__ == "__main__":
    try:
        logging.info("程序开始执行")
        exit_code = main()
        logging.info(f"程序正常退出，返回码: {exit_code}")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logging.info("用户中断程序")
        print("\n👋 用户中断，程序退出")
        sys.exit(0)
    except Exception as e:
        error_msg = f"未处理的异常: {e}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        print(f"❌ {error_msg}")
        print(f"详细错误信息已保存到日志文件: {log_file}")
        sys.exit(1)
