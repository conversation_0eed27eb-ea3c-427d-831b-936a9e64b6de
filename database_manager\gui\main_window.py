#!/usr/bin/env python3
"""
主窗口GUI
数据库管理工具的主界面
"""

import sys
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QSplitter, QMenuBar, QToolBar, QStatusBar, QLabel,
                            QTreeWidget, QTreeWidgetItem, QTabWidget, QMessageBox,
                            QFileDialog, QComboBox, QPushButton, QGroupBox,
                            QTextEdit, QLineEdit, QSpinBox, QCheckBox)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QAction, QIcon, QFont

from gui.table_viewer import TableViewer
from gui.query_editor import QueryEditor
from gui.connection_dialog import ConnectionDialog
from gui.export_dialog import ExportDialog
from core.database import db_manager
from core.config import config_manager

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("YF AI Chat 数据库管理器")
        self.setMinimumSize(1000, 700)
        
        # 加载窗口几何信息
        geometry = config_manager.get_window_geometry()
        self.resize(geometry['width'], geometry['height'])
        self.move(geometry['x'], geometry['y'])
        
        self.init_ui()
        self.init_connections()
        
        # 尝试连接到上次使用的数据库
        last_db_path = config_manager.get_last_database_path()
        if last_db_path:
            self.connect_to_database(last_db_path)
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_tool_bar()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧面板
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # 右侧面板
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([300, 900])
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        connect_action = QAction('连接数据库(&C)', self)
        connect_action.setShortcut('Ctrl+O')
        connect_action.triggered.connect(self.show_connection_dialog)
        file_menu.addAction(connect_action)
        
        disconnect_action = QAction('断开连接(&D)', self)
        disconnect_action.triggered.connect(self.disconnect_database)
        file_menu.addAction(disconnect_action)
        
        file_menu.addSeparator()
        
        export_action = QAction('导出数据(&E)', self)
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.show_export_dialog)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 查看菜单
        view_menu = menubar.addMenu('查看(&V)')
        
        refresh_action = QAction('刷新(&R)', self)
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.refresh_data)
        view_menu.addAction(refresh_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        about_action = QAction('关于(&A)', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_tool_bar(self):
        """创建工具栏"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # 连接按钮
        connect_btn = QPushButton("连接数据库")
        connect_btn.clicked.connect(self.show_connection_dialog)
        toolbar.addWidget(connect_btn)
        
        toolbar.addSeparator()
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_data)
        toolbar.addWidget(refresh_btn)
        
        # 导出按钮
        export_btn = QPushButton("导出")
        export_btn.clicked.connect(self.show_export_dialog)
        toolbar.addWidget(export_btn)
        
        toolbar.addSeparator()
        
        # 数据库路径显示
        self.db_path_label = QLabel("未连接数据库")
        self.db_path_label.setStyleSheet("color: gray; font-style: italic;")
        toolbar.addWidget(self.db_path_label)
    
    def create_left_panel(self):
        """创建左侧面板"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 数据库信息组
        db_info_group = QGroupBox("数据库信息")
        db_info_layout = QVBoxLayout(db_info_group)
        
        self.db_info_text = QTextEdit()
        self.db_info_text.setMaximumHeight(100)
        self.db_info_text.setReadOnly(True)
        db_info_layout.addWidget(self.db_info_text)
        
        left_layout.addWidget(db_info_group)
        
        # 表列表组
        tables_group = QGroupBox("数据表")
        tables_layout = QVBoxLayout(tables_group)
        
        self.tables_tree = QTreeWidget()
        self.tables_tree.setHeaderLabel("表名")
        self.tables_tree.itemClicked.connect(self.on_table_selected)
        tables_layout.addWidget(self.tables_tree)
        
        left_layout.addWidget(tables_group)
        
        # 表结构信息组
        structure_group = QGroupBox("表结构")
        structure_layout = QVBoxLayout(structure_group)
        
        self.structure_text = QTextEdit()
        self.structure_text.setReadOnly(True)
        self.structure_text.setMaximumHeight(200)
        structure_layout.addWidget(self.structure_text)
        
        left_layout.addWidget(structure_group)
        
        return left_widget
    
    def create_right_panel(self):
        """创建右侧面板"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 表格查看器标签页
        self.table_viewer = TableViewer()
        self.tab_widget.addTab(self.table_viewer, "数据查看")
        
        # SQL查询编辑器标签页
        self.query_editor = QueryEditor()
        self.tab_widget.addTab(self.query_editor, "SQL查询")
        
        right_layout.addWidget(self.tab_widget)
        
        return right_widget
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 连接状态标签
        self.connection_status = QLabel("未连接")
        self.status_bar.addWidget(self.connection_status)
        
        self.status_bar.addPermanentWidget(QLabel("就绪"))
    
    def init_connections(self):
        """初始化信号连接"""
        # 连接查询编辑器的信号
        self.query_editor.query_executed.connect(self.on_query_executed)
        
        # 连接表格查看器的信号
        self.table_viewer.data_changed.connect(self.on_data_changed)
    
    def show_connection_dialog(self):
        """显示连接对话框"""
        dialog = ConnectionDialog(self)
        if dialog.exec() == dialog.DialogCode.Accepted:
            db_path = dialog.get_selected_path()
            if db_path:
                self.connect_to_database(db_path)
    
    def connect_to_database(self, db_path: str):
        """连接到数据库"""
        try:
            if db_manager.connect(db_path):
                config_manager.add_database_path(db_path)
                self.update_ui_after_connection()
                self.status_bar.showMessage(f"已连接到数据库: {db_path}", 3000)
        except Exception as e:
            QMessageBox.critical(self, "连接错误", str(e))
    
    def disconnect_database(self):
        """断开数据库连接"""
        db_manager.disconnect()
        self.update_ui_after_disconnection()
        self.status_bar.showMessage("已断开数据库连接", 3000)
    
    def update_ui_after_connection(self):
        """连接后更新UI"""
        # 更新连接状态
        self.connection_status.setText("已连接")
        self.connection_status.setStyleSheet("color: green;")
        
        # 更新数据库路径显示
        self.db_path_label.setText(f"数据库: {db_manager.database_path}")
        self.db_path_label.setStyleSheet("color: black;")
        
        # 更新数据库信息
        self.update_database_info()
        
        # 更新表列表
        self.update_tables_list()
    
    def update_ui_after_disconnection(self):
        """断开连接后更新UI"""
        # 更新连接状态
        self.connection_status.setText("未连接")
        self.connection_status.setStyleSheet("color: red;")
        
        # 更新数据库路径显示
        self.db_path_label.setText("未连接数据库")
        self.db_path_label.setStyleSheet("color: gray; font-style: italic;")
        
        # 清空显示
        self.db_info_text.clear()
        self.tables_tree.clear()
        self.structure_text.clear()
        self.table_viewer.clear_data()
    
    def update_database_info(self):
        """更新数据库信息显示"""
        info = db_manager.get_database_info()
        if info:
            info_text = f"路径: {info.get('path', 'N/A')}\n"
            info_text += f"大小: {info.get('size', 0) / 1024:.2f} KB\n"
            info_text += f"表数量: {info.get('tables', 0)}\n"
            info_text += f"SQLite版本: {info.get('version', 'N/A')}"
            self.db_info_text.setText(info_text)
    
    def update_tables_list(self):
        """更新表列表"""
        self.tables_tree.clear()
        try:
            tables = db_manager.get_tables()
            for table in tables:
                item = QTreeWidgetItem([table])
                self.tables_tree.addTopLevelItem(item)
        except Exception as e:
            QMessageBox.warning(self, "警告", f"获取表列表失败: {e}")
    
    def on_table_selected(self, item):
        """表被选中时的处理"""
        table_name = item.text(0)
        
        # 更新表结构显示
        self.update_table_structure(table_name)
        
        # 在表格查看器中显示数据
        self.table_viewer.load_table_data(table_name)
        
        # 切换到数据查看标签页
        self.tab_widget.setCurrentIndex(0)
    
    def update_table_structure(self, table_name: str):
        """更新表结构显示"""
        try:
            columns = db_manager.get_table_info(table_name)
            structure_text = f"表名: {table_name}\n\n"
            structure_text += "列信息:\n"
            structure_text += "-" * 40 + "\n"
            
            for col in columns:
                pk_mark = " (主键)" if col['pk'] else ""
                null_mark = " NOT NULL" if col['notnull'] else ""
                default_mark = f" DEFAULT {col['default_value']}" if col['default_value'] else ""
                
                structure_text += f"{col['name']}: {col['type']}{pk_mark}{null_mark}{default_mark}\n"
            
            self.structure_text.setText(structure_text)
        except Exception as e:
            self.structure_text.setText(f"获取表结构失败: {e}")
    
    def refresh_data(self):
        """刷新数据"""
        if db_manager.is_connected:
            self.update_tables_list()
            self.table_viewer.refresh_current_table()
            self.status_bar.showMessage("数据已刷新", 2000)
    
    def show_export_dialog(self):
        """显示导出对话框"""
        if not db_manager.is_connected:
            QMessageBox.warning(self, "警告", "请先连接数据库")
            return
        
        dialog = ExportDialog(self)
        dialog.exec()
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", 
                         "YF AI Chat 数据库管理器\n\n"
                         "专为YF AI Chat后端数据库设计的管理工具\n"
                         "支持数据查看、编辑、查询和导出功能")
    
    def on_query_executed(self, result_data, message):
        """查询执行完成的处理"""
        self.status_bar.showMessage(message, 3000)
    
    def on_data_changed(self, message):
        """数据变更的处理"""
        self.status_bar.showMessage(message, 3000)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 保存窗口几何信息
        geometry = self.geometry()
        config_manager.set_window_geometry(
            geometry.width(), geometry.height(),
            geometry.x(), geometry.y()
        )
        
        # 断开数据库连接
        db_manager.disconnect()
        
        event.accept()
