"""
PyInstaller hook for pydantic
"""
from PyInstaller.utils.hooks import collect_all

datas, binaries, hiddenimports = collect_all('pydantic')

# 添加额外的隐藏导入
hiddenimports += [
    'pydantic.fields',
    'pydantic.main',
    'pydantic.types',
    'pydantic.validators',
    'pydantic.json',
    'pydantic.parse',
    'pydantic.schema',
    'pydantic.utils',
    'pydantic.error_wrappers',
    'pydantic.env_settings',
    'pydantic_settings',
    'pydantic.v1',
    'pydantic.v1.fields',
    'pydantic.v1.main',
    'pydantic.v1.types',
    'pydantic.v1.validators',
]
