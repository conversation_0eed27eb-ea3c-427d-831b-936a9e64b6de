#!/usr/bin/env python3
"""
SQL查询编辑器组件
用于执行SQL查询和显示结果
"""

import pandas as pd
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QSplitter, QComboBox, QLabel, QMessageBox,
                            QGroupBox, QHeaderView, QAbstractItemView)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QTextCursor

from core.database import db_manager
from core.config import config_manager

class QueryEditor(QWidget):
    # 信号定义
    query_executed = pyqtSignal(pd.DataFrame, str)  # 查询执行完成信号
    
    def __init__(self):
        super().__init__()
        self.query_history = []
        self.current_result = pd.DataFrame()
        
        self.init_ui()
        self.load_query_history()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)
        
        # 上半部分：查询编辑器
        query_panel = self.create_query_panel()
        splitter.addWidget(query_panel)
        
        # 下半部分：结果显示
        result_panel = self.create_result_panel()
        splitter.addWidget(result_panel)
        
        # 设置分割器比例
        splitter.setSizes([300, 400])
    
    def create_query_panel(self):
        """创建查询编辑面板"""
        group = QGroupBox("SQL查询编辑器")
        layout = QVBoxLayout(group)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        # 查询历史下拉框
        toolbar_layout.addWidget(QLabel("查询历史:"))
        self.history_combo = QComboBox()
        self.history_combo.setMinimumWidth(200)
        self.history_combo.currentTextChanged.connect(self.on_history_selected)
        toolbar_layout.addWidget(self.history_combo)
        
        toolbar_layout.addStretch()
        
        # 执行按钮
        self.execute_btn = QPushButton("执行查询 (Ctrl+Enter)")
        self.execute_btn.clicked.connect(self.execute_query)
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        toolbar_layout.addWidget(self.execute_btn)
        
        # 清空按钮
        clear_btn = QPushButton("清空")
        clear_btn.clicked.connect(self.clear_query)
        toolbar_layout.addWidget(clear_btn)
        
        layout.addLayout(toolbar_layout)
        
        # SQL编辑器
        self.query_editor = QTextEdit()
        self.query_editor.setPlaceholderText("在此输入SQL查询语句...\n\n示例:\nSELECT * FROM chat_sessions LIMIT 10;\nSELECT COUNT(*) FROM chat_messages;")
        
        # 设置等宽字体
        font = QFont("Consolas", 11)
        if not font.exactMatch():
            font = QFont("Courier New", 11)
        self.query_editor.setFont(font)
        
        # 设置快捷键
        self.query_editor.keyPressEvent = self.query_editor_key_press
        
        layout.addWidget(self.query_editor)
        
        # 常用查询按钮
        common_queries_layout = QHBoxLayout()
        
        # 预设查询按钮
        queries = [
            ("查看所有表", "SELECT name FROM sqlite_master WHERE type='table';"),
            ("会话统计", "SELECT COUNT(*) as total_sessions FROM chat_sessions;"),
            ("消息统计", "SELECT COUNT(*) as total_messages FROM chat_messages;"),
            ("最近会话", "SELECT * FROM chat_sessions ORDER BY updated_at DESC LIMIT 10;")
        ]
        
        for name, query in queries:
            btn = QPushButton(name)
            btn.clicked.connect(lambda checked, q=query: self.set_query(q))
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                }
            """)
            common_queries_layout.addWidget(btn)
        
        common_queries_layout.addStretch()
        layout.addLayout(common_queries_layout)
        
        return group
    
    def create_result_panel(self):
        """创建结果显示面板"""
        group = QGroupBox("查询结果")
        layout = QVBoxLayout(group)
        
        # 结果信息栏
        info_layout = QHBoxLayout()
        self.result_info_label = QLabel("未执行查询")
        self.result_info_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        info_layout.addWidget(self.result_info_label)
        
        info_layout.addStretch()
        
        # 导出结果按钮
        self.export_result_btn = QPushButton("导出结果")
        self.export_result_btn.clicked.connect(self.export_result)
        self.export_result_btn.setEnabled(False)
        info_layout.addWidget(self.export_result_btn)
        
        layout.addLayout(info_layout)
        
        # 结果表格
        self.result_table = QTableWidget()
        self.result_table.setAlternatingRowColors(True)
        self.result_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        
        # 设置表格头部
        header = self.result_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        header.setStretchLastSection(True)
        
        layout.addWidget(self.result_table)
        
        return group
    
    def query_editor_key_press(self, event):
        """查询编辑器按键事件"""
        # Ctrl+Enter 执行查询
        if event.key() == Qt.Key.Key_Return and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
            self.execute_query()
        else:
            # 调用原始的按键处理
            QTextEdit.keyPressEvent(self.query_editor, event)
    
    def load_query_history(self):
        """加载查询历史"""
        self.query_history = config_manager.get_query_history()
        self.update_history_combo()
    
    def update_history_combo(self):
        """更新历史查询下拉框"""
        self.history_combo.clear()
        self.history_combo.addItem("选择历史查询...")
        for query in self.query_history:
            # 显示查询的前50个字符
            display_text = query[:50] + "..." if len(query) > 50 else query
            display_text = display_text.replace('\n', ' ')
            self.history_combo.addItem(display_text, query)
    
    def on_history_selected(self, text):
        """历史查询被选中"""
        if self.history_combo.currentIndex() > 0:
            query = self.history_combo.currentData()
            if query:
                self.query_editor.setText(query)
    
    def set_query(self, query: str):
        """设置查询文本"""
        self.query_editor.setText(query)
    
    def clear_query(self):
        """清空查询"""
        self.query_editor.clear()
    
    def execute_query(self):
        """执行查询"""
        if not db_manager.is_connected:
            QMessageBox.warning(self, "警告", "请先连接数据库")
            return
        
        query = self.query_editor.toPlainText().strip()
        if not query:
            QMessageBox.warning(self, "警告", "请输入查询语句")
            return
        
        try:
            # 执行查询
            self.execute_btn.setEnabled(False)
            self.execute_btn.setText("执行中...")
            
            result_data, message = db_manager.execute_query(query)
            
            # 更新结果显示
            self.update_result_display(result_data, message)
            
            # 添加到历史记录
            config_manager.add_query_to_history(query)
            self.load_query_history()
            
            # 发送信号
            self.query_executed.emit(result_data, message)
            
        except Exception as e:
            QMessageBox.critical(self, "查询错误", str(e))
            self.result_info_label.setText(f"查询失败: {e}")
            self.result_info_label.setStyleSheet("color: #e74c3c;")
        
        finally:
            self.execute_btn.setEnabled(True)
            self.execute_btn.setText("执行查询 (Ctrl+Enter)")
    
    def update_result_display(self, data: pd.DataFrame, message: str):
        """更新结果显示"""
        self.current_result = data
        
        # 更新信息标签
        self.result_info_label.setText(message)
        self.result_info_label.setStyleSheet("color: #27ae60;")
        
        if data.empty:
            # 清空表格
            self.result_table.setRowCount(0)
            self.result_table.setColumnCount(0)
            self.export_result_btn.setEnabled(False)
        else:
            # 显示数据
            self.display_data_in_table(data)
            self.export_result_btn.setEnabled(True)
    
    def display_data_in_table(self, data: pd.DataFrame):
        """在表格中显示数据"""
        # 设置表格大小
        self.result_table.setRowCount(len(data))
        self.result_table.setColumnCount(len(data.columns))
        
        # 设置列标题
        self.result_table.setHorizontalHeaderLabels(list(data.columns))
        
        # 填充数据
        for row in range(len(data)):
            for col in range(len(data.columns)):
                value = data.iloc[row, col]
                
                # 处理特殊值
                if pd.isna(value):
                    display_value = ""
                elif isinstance(value, (dict, list)):
                    display_value = str(value)
                else:
                    display_value = str(value)
                
                item = QTableWidgetItem(display_value)
                item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # 设置为只读
                self.result_table.setItem(row, col, item)
        
        # 调整列宽
        self.result_table.resizeColumnsToContents()
    
    def export_result(self):
        """导出查询结果"""
        if self.current_result.empty:
            QMessageBox.warning(self, "警告", "没有可导出的数据")
            return
        
        from PyQt6.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出查询结果", "query_result.csv", 
            "CSV文件 (*.csv);;Excel文件 (*.xlsx);;JSON文件 (*.json)"
        )
        
        if file_path:
            try:
                from core.export import export_manager
                
                if file_path.endswith('.csv'):
                    export_manager.export_to_csv(self.current_result, file_path)
                elif file_path.endswith('.xlsx'):
                    export_manager.export_to_excel(self.current_result, file_path)
                elif file_path.endswith('.json'):
                    export_manager.export_to_json(self.current_result, file_path)
                
                QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "导出错误", f"导出失败: {e}")
    
    def clear_result(self):
        """清空结果显示"""
        self.result_table.setRowCount(0)
        self.result_table.setColumnCount(0)
        self.current_result = pd.DataFrame()
        self.result_info_label.setText("未执行查询")
        self.result_info_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        self.export_result_btn.setEnabled(False)
