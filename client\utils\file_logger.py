#!/usr/bin/env python3
"""
客服端文件日志系统
将详细日志写入文件，终端只显示错误信息
"""
import os
import logging
from datetime import datetime
from logging.handlers import RotatingFileHandler

class ClientFileLogger:
    def __init__(self, name="client", log_dir="."):
        """
        初始化客服端文件日志系统
        
        Args:
            name: 日志名称
            log_dir: 日志文件目录
        """
        self.name = name
        self.log_dir = log_dir
        
        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
        
        # 设置日志文件路径
        log_file = os.path.join(log_dir, f"{name}.log")
        
        # 创建logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # 清除现有的处理器
        self.logger.handlers.clear()
        
        # 文件处理器 - 详细日志
        file_handler = RotatingFileHandler(
            log_file, 
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器 - 只显示错误
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.ERROR)
        
        # 设置格式
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_formatter = logging.Formatter(
            '❌ [%(asctime)s] %(levelname)s: %(message)s',
            datefmt='%H:%M:%S'
        )
        
        file_handler.setFormatter(file_formatter)
        console_handler.setFormatter(console_formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
        # 记录启动信息
        self.info(f"客服端日志系统初始化完成 - 日志文件: {log_file}")
    
    def debug(self, message):
        """调试信息"""
        self.logger.debug(message)
    
    def info(self, message):
        """一般信息"""
        self.logger.info(message)
    
    def warning(self, message):
        """警告信息"""
        self.logger.warning(message)
    
    def error(self, message):
        """错误信息 - 会显示在终端"""
        self.logger.error(message)
    
    def critical(self, message):
        """严重错误 - 会显示在终端"""
        self.logger.critical(message)
    
    def startup(self, message):
        """启动信息 - 写入文件，终端显示简化版本"""
        self.logger.info(f"[STARTUP] {message}")
        print(f"🚀 {message}")
    
    def success(self, message):
        """成功信息 - 写入文件，终端显示简化版本"""
        self.logger.info(f"[SUCCESS] {message}")
        print(f"✅ {message}")
    
    def shutdown(self, message):
        """关闭信息 - 写入文件，终端显示简化版本"""
        self.logger.info(f"[SHUTDOWN] {message}")
        print(f"🔄 {message}")
    
    def login_attempt(self, username, success=True):
        """登录尝试"""
        status = "成功" if success else "失败"
        self.logger.info(f"[LOGIN] 用户 {username} 登录{status}")
        if success:
            print(f"👤 用户 {username} 登录成功")
        else:
            print(f"❌ 用户 {username} 登录失败")
    
    def api_request(self, method, url, status_code=None, duration=None):
        """API请求日志"""
        duration_str = f" ({duration:.2f}ms)" if duration else ""
        status_str = f" - {status_code}" if status_code else ""
        self.logger.info(f"[API] {method} {url}{status_str}{duration_str}")
    
    def websocket_event(self, event, details=None):
        """WebSocket事件"""
        detail_info = f" - {details}" if details else ""
        self.logger.info(f"[WS] {event}{detail_info}")
    
    def session_action(self, action, session_id, admin_id=None):
        """会话操作"""
        admin_info = f" (管理员: {admin_id})" if admin_id else ""
        short_session = session_id[:12] + "..." if len(session_id) > 12 else session_id
        self.logger.info(f"[SESSION] {action} - 会话: {session_id}{admin_info}")
        print(f"📋 {action} - 会话: {short_session}{admin_info}")
    
    def message_action(self, action, session_id, content=None, admin_id=None):
        """消息操作"""
        admin_info = f" (管理员: {admin_id})" if admin_id else ""
        content_info = f" - 内容: {content[:50]}..." if content and len(content) > 50 else f" - 内容: {content}" if content else ""
        short_session = session_id[:12] + "..." if len(session_id) > 12 else session_id
        self.logger.info(f"[MESSAGE] {action} - 会话: {session_id}{admin_info}{content_info}")
        print(f"💬 {action} - 会话: {short_session}{admin_info}")
    
    def ui_action(self, action, details=None):
        """UI操作"""
        detail_info = f" - {details}" if details else ""
        self.logger.info(f"[UI] {action}{detail_info}")
    
    def data_operation(self, operation, count=None):
        """数据操作"""
        count_info = f" ({count} 项)" if count is not None else ""
        self.logger.info(f"[DATA] {operation}{count_info}")
    
    def connection_status(self, status, server_url=None):
        """连接状态"""
        server_info = f" - 服务器: {server_url}" if server_url else ""
        self.logger.info(f"[CONNECTION] {status}{server_info}")
        if "成功" in status or "连接" in status:
            print(f"🌐 {status}")
        elif "失败" in status or "断开" in status:
            print(f"❌ {status}")

# 创建全局日志实例 - 日志保存在client目录
import os
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # client目录
client_logger = ClientFileLogger("client", current_dir)
