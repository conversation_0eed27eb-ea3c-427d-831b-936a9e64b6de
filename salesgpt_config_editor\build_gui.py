#!/usr/bin/env python3
"""
GUI配置编辑器打包脚本
使用PyInstaller将GUI应用程序打包为独立的可执行文件

使用方法:
python build_gui.py

输出:
- dist/杨杋ai_agent配置管理器.exe (Windows可执行文件)
- dist/杨杋ai_agent配置管理器/ (包含所有依赖的文件夹)
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装 (版本: {PyInstaller.__version__})")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        print("正在安装PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False

def clean_build_dirs():
    """清理之前的构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理spec文件
    spec_files = list(Path('.').glob('*.spec'))
    for spec_file in spec_files:
        print(f"🧹 删除spec文件: {spec_file}")
        spec_file.unlink()

def create_version_info():
    """创建版本信息文件"""
    version_info = """# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
# filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
# Set not needed items to zero 0.
filevers=(1,0,0,0),
prodvers=(1,0,0,0),
# Contains a bitmask that specifies the valid bits 'flags'r
mask=0x3f,
# Contains a bitmask that specifies the Boolean attributes of the file.
flags=0x0,
# The operating system for which this file was designed.
# 0x4 - NT and there is no need to change it.
OS=0x4,
# The general type of file.
# 0x1 - the file is an application.
fileType=0x1,
# The function of the file.
# 0x0 - the function is not defined for this fileType
subtype=0x0,
# Creation date and time stamp.
date=(0, 0)
),
  kids=[
StringFileInfo(
  [
  StringTable(
    u'040904B0',
    [StringStruct(u'CompanyName', u'杨杋科技'),
    StringStruct(u'FileDescription', u'杨杋AI Agent配置管理器'),
    StringStruct(u'FileVersion', u'*******'),
    StringStruct(u'InternalName', u'salesgpt_config_editor'),
    StringStruct(u'LegalCopyright', u'Copyright © 2025 杨杋科技'),
    StringStruct(u'OriginalFilename', u'杨杋ai_agent配置管理器.exe'),
    StringStruct(u'ProductName', u'杨杋AI Agent配置管理器'),
    StringStruct(u'ProductVersion', u'*******')])
  ]), 
VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)"""
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    print("✅ 创建版本信息文件")

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")

    # PyInstaller命令参数
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包成单个文件
        '--windowed',                   # 不显示控制台窗口（正式版）
        '--name=杨杋ai_agent配置管理器',      # 可执行文件名称
        '--icon=resources/app_icon.ico', # 应用程序图标
        '--version-file=version_info.txt', # 版本信息
        '--add-data=resources;resources',  # 添加资源文件夹
        '--add-data=ui;ui',               # 添加UI模块
        '--add-data=core;core',           # 添加核心模块
        # 添加UI模块的隐藏导入
        '--hidden-import=ui.main_window',
        '--hidden-import=ui.file_manager_widget',
        '--hidden-import=ui.agent_config_widget',
        '--hidden-import=ui.product_management_widget',
        '--hidden-import=ui.sales_process_widget',
        '--hidden-import=ui.after_sales_widget',
        '--hidden-import=ui.advanced_settings_widget',
        '--hidden-import=ui.config_utils',
        # 添加核心模块的隐藏导入
        '--hidden-import=core.simple_config_manager',
        '--hidden-import=core.config_manager',
        # 添加PyQt6相关的隐藏导入
        '--hidden-import=PyQt6.QtCore',
        '--hidden-import=PyQt6.QtGui',
        '--hidden-import=PyQt6.QtWidgets',
        # 添加OmegaConf相关的隐藏导入
        '--hidden-import=omegaconf',
        '--hidden-import=omegaconf.dictconfig',
        '--hidden-import=omegaconf.listconfig',
        '--clean',                        # 清理临时文件
        '--noconfirm',                   # 不询问覆盖
        'main.py'                        # 主程序文件
    ]
    
    try:
        # 执行PyInstaller命令
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def create_portable_package():
    """创建便携版包"""
    print("📦 创建便携版包...")
    
    # 创建便携版目录
    portable_dir = Path('dist/杨杋ai_agent配置管理器_便携版')
    portable_dir.mkdir(exist_ok=True)
    
    # 复制可执行文件
    exe_file = Path('dist/杨杋ai_agent配置管理器.exe')
    if exe_file.exists():
        shutil.copy2(exe_file, portable_dir / '杨杋ai_agent配置管理器.exe')
        print("✅ 复制可执行文件")
    
    # 创建使用说明
    readme_content = """# 杨杋AI Agent配置管理器 - 便携版

## 使用说明

1. 双击 `杨杋ai_agent配置管理器.exe` 启动程序
2. 首次运行时，程序会要求选择agent配置文件位置
3. 通常配置文件位于: `backend/salesgpt_config/advanced_sales_config.json`
4. 选择配置文件后，即可开始编辑各项配置

## 功能特点

- ✅ 可视化配置编辑
- ✅ 实时配置验证
- ✅ 文件历史记录
- ✅ 模板和向导
- ✅ 中文界面支持

## 配置文件说明

程序会编辑以下配置内容:
- Agent基础信息 (客服名称、公司信息等)
- 产品目录管理
- 销售流程配置
- 售后服务设置
- 高级设置选项

## 注意事项

1. 请确保有配置文件的读写权限
2. 建议在修改前备份原配置文件
3. 程序会自动验证配置格式的正确性
4. 如遇问题，请检查配置文件路径是否正确

## 技术支持

如有问题，请联系技术支持。

---
版本: 1.0.0
构建时间: {build_time}
"""
    
    from datetime import datetime
    build_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    with open(portable_dir / 'README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content.format(build_time=build_time))
    
    print("✅ 创建使用说明")
    print(f"📁 便携版包位置: {portable_dir.absolute()}")

def cleanup_temp_files():
    """清理临时文件"""
    print("🧹 清理临时文件...")
    
    temp_files = ['version_info.txt']
    for temp_file in temp_files:
        if os.path.exists(temp_file):
            os.remove(temp_file)
    
    # 清理build目录
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # 清理spec文件
    spec_files = list(Path('.').glob('*.spec'))
    for spec_file in spec_files:
        spec_file.unlink()
    
    print("✅ 清理完成")

def main():
    """主函数"""
    print("🚀 开始打包杨杋AI Agent配置管理器...")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists('main.py'):
        print("❌ 错误: 请在salesgpt_config_editor目录下运行此脚本")
        return False
    
    # 检查PyInstaller
    if not check_pyinstaller():
        return False
    
    # 清理之前的构建
    clean_build_dirs()
    
    # 创建版本信息
    create_version_info()
    
    # 构建可执行文件
    if not build_executable():
        return False
    
    # 创建便携版包
    create_portable_package()
    
    # 清理临时文件
    cleanup_temp_files()
    
    print("=" * 50)
    print("🎉 打包完成!")
    print(f"📁 输出目录: {Path('dist').absolute()}")
    print("📋 输出文件:")
    print("   - 杨杋ai_agent配置管理器.exe (单文件版)")
    print("   - 杨杋ai_agent配置管理器_便携版/ (便携版包)")
    
    return True

if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)
