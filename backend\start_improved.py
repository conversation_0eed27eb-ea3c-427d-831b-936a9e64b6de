#!/usr/bin/env python3
"""
改进的后端启动脚本
包含更好的错误处理和日志管理
"""

import os
import sys
import asyncio
import signal
import logging
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def setup_signal_handlers():
    """设置信号处理器，优雅关闭服务"""
    def signal_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在优雅关闭服务...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def check_environment():
    """检查环境配置"""
    from utils.path_utils import get_env_file_path
    from dotenv import load_dotenv
    
    env_file = get_env_file_path()
    if not env_file.exists():
        print(f"❌ 环境配置文件不存在: {env_file}")
        print("请先创建 .env 文件或运行配置工具")
        return False
    
    load_dotenv(dotenv_path=env_file)
    
    # 检查关键配置
    required_vars = ["GEMINI_API_KEY", "ADMIN_TOKEN", "WORDPRESS_TOKEN"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少必要的环境变量: {', '.join(missing_vars)}")
        return False
    
    print("✅ 环境配置检查通过")
    return True

def main():
    """主启动函数"""
    print("🚀 YF AI Chat Backend - 改进版启动")
    print("=" * 50)
    
    # 设置信号处理
    setup_signal_handlers()
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    # 导入并启动应用
    try:
        print("📦 正在加载应用模块...")
        from main import app, logger
        import uvicorn
        from logging_config import setup_logging_filters, configure_uvicorn_logging
        
        # 设置日志过滤器
        setup_logging_filters()
        
        # 获取配置
        host = os.getenv("HOST", "0.0.0.0")
        port = int(os.getenv("PORT", 8000))
        https_mode = os.getenv("HTTPS_MODE", "disabled").lower()
        
        print(f"🌐 服务器配置: {host}:{port}")
        print(f"🔒 HTTPS模式: {https_mode}")
        
        # Uvicorn配置
        uvicorn_config = {
            "app": app,
            "host": host,
            "port": port,
            "reload": False,
            "access_log": False,
            "log_level": "warning",
            "server_header": False,
            "date_header": False,
            "timeout_keep_alive": 5,
            "timeout_graceful_shutdown": 10,
            "limit_concurrency": 1000,
            "limit_max_requests": 10000,
            "backlog": 2048,
            "log_config": configure_uvicorn_logging()
        }
        
        # SSL配置
        if https_mode == "direct":
            from utils.path_utils import get_ssl_cert_path, get_ssl_key_path
            cert_path = get_ssl_cert_path()
            key_path = get_ssl_key_path()
            
            if cert_path and key_path:
                uvicorn_config["ssl_certfile"] = str(cert_path)
                uvicorn_config["ssl_keyfile"] = str(key_path)
                print(f"🔒 SSL证书已配置")
            else:
                print("⚠️  SSL证书未找到，使用HTTP模式")
        
        print("✅ 应用配置完成")
        print("🎯 启动提示:")
        print("   - 连接重置错误已被过滤")
        print("   - 无效HTTP请求警告已被静默处理")
        print("   - 只显示重要的错误和警告信息")
        print("=" * 50)
        
        # 启动服务器
        uvicorn.run(**uvicorn_config)
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
