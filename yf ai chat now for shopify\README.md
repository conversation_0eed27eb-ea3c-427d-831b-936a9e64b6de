# YF AI Chat Now for Shopify

WordPress插件的Shopify主题嵌入版本

## 📋 版本信息

**当前版本**: 2.1.2 (2025-01-04)

### 🔒 版本 2.1.2 安全更新内容
- **安全增强**：移除所有可能暴露API URL、Token和Session ID的控制台日志
- **隐私保护**：清理敏感信息输出，防止在浏览器开发者工具中泄露配置
- **日志优化**：保留必要的调试信息，但不暴露具体的敏感数据
- **配置安全**：更新配置文件模板，提醒用户修改默认值

### 🔧 版本 2.1.1 更新内容
- **用户消息显示修复**：同步WordPress版本的CSS修复，解决用户消息不显示问题
- **样式优化**：为用户消息添加 `!important` 规则，确保在各种Shopify主题中正确显示
- **功能同步**：与WordPress版本保持功能一致性
- **后端兼容**：支持最新的CSV导出和清空功能

## 📁 项目结构

```
yf ai chat now for shopify/
├── assets/                 # 静态资源文件
│   ├── yf-chat-shopify.js  # 主要JavaScript文件
│   ├── yf-chat-shopify.css # 样式文件
│   └── icons/              # 图标文件
├── snippets/               # Shopify代码片段
│   └── yf-chat-widget.liquid # 聊天组件代码
├── templates/              # 安装模板
│   └── theme-integration.liquid # 主题集成代码
├── docs/                   # 文档
│   ├── installation.md    # 安装说明
│   └── configuration.md   # 配置说明
└── README.md              # 项目说明
```

## 🎯 功能特性

### ✅ 保持的WordPress功能
- 智能AI聊天对话
- 客服人工接管
- 聊天历史记录
- 实时消息推送
- 多LLM支持 (Gemini/OpenAI/DeepSeek)
- Dylan环保电器助手

### 🆕 Shopify特有功能
- 商品信息集成
- 购物车操作支持
- 订单查询功能
- Shopify主题适配

## 🔧 技术架构

```
Shopify Store → JavaScript Widget → 后端API (chat.22668.xyz:8000)
                     ↓
              客服端管理界面 (PyQt6)
```

## 📋 安装步骤

### 1. 上传文件
1. 将 `assets/` 目录下的文件上传到主题的 `assets/` 目录
2. 将 `snippets/` 目录下的文件上传到主题的 `snippets/` 目录

### 2. 修改主题
在 `theme.liquid` 文件的 `</body>` 标签前添加：
```liquid
{% include 'yf-chat-widget' %}
```

### 3. 配置API
修改 `yf-chat-shopify.js` 中的配置：
```javascript
const YF_CHAT_CONFIG = {
    apiUrl: 'https://chat.22668.xyz:8000',
    apiToken: 'YOUR_WORDPRESS_TOKEN_HERE',
    // 其他配置...
};
```

## 🛠️ 开发说明

### 基于WordPress版本改造
- **复用后端API**：完全使用现有的后端服务
- **保持功能一致**：所有核心功能保持不变
- **适配Shopify环境**：针对Shopify主题优化

### 主要改造点
1. **API调用方式**：从WordPress AJAX改为直接REST API调用
2. **认证方式**：使用Bearer Token认证
3. **嵌入方式**：从PHP钩子改为Liquid模板嵌入
4. **样式适配**：适应Shopify主题环境

## 🔗 相关链接

- **后端API文档**：https://chat.22668.xyz:8000/docs
- **WordPress版本**：../yf ai chat now/
- **客服端**：../client/

## 📞 技术支持

如需技术支持，请联系：<EMAIL>

## 🚀 快速开始

### 1. 准备工作
- 确保后端API服务运行在 `https://chat.22668.xyz:8000`
- 获取有效的API Token (使用WordPress版本的token)
- 拥有Shopify商店的主题编辑权限

### 2. 安装步骤
```bash
# 1. 上传文件到Shopify主题
# - assets/yf-chat-shopify.css → 主题/assets/
# - assets/yf-chat-shopify.js → 主题/assets/
# - snippets/yf-chat-widget.liquid → 主题/snippets/

# 2. 编辑theme.liquid文件，在</body>前添加：
{% include 'yf-chat-widget' %}

# 3. 配置API信息
# 编辑 snippets/yf-chat-widget.liquid 中的配置
```

### 3. 配置API Token
```javascript
window.YF_CHAT_CONFIG = {
    apiUrl: 'https://chat.22668.xyz:8000',
    apiToken: 'YOUR_ACTUAL_TOKEN_HERE',  // 替换为真实token
    // ... 其他配置
};
```

## 🔧 核心改造说明

### WordPress → Shopify 主要变化

#### 1. API调用方式
```javascript
// WordPress版本 (AJAX)
fetch('/wp-admin/admin-ajax.php', {
    method: 'POST',
    body: new URLSearchParams({
        action: 'yf_chat_message',
        nonce: wpNonce
    })
})

// Shopify版本 (直接API)
fetch('https://chat.22668.xyz:8000/api/chat', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer YOUR_TOKEN',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        message: userMessage,
        session_id: sessionId
    })
})
```

#### 2. 嵌入方式
```php
// WordPress版本 (PHP钩子)
add_action('wp_footer', 'yf_chat_widget');

// Shopify版本 (Liquid模板)
{% include 'yf-chat-widget' %}
```

#### 3. 认证方式
```javascript
// WordPress版本 (Nonce)
nonce: wpNonce

// Shopify版本 (Bearer Token)
'Authorization': 'Bearer YOUR_TOKEN'
```

## 🛍️ Shopify特有功能

### 1. 商品信息自动获取
```javascript
// 自动获取当前页面商品信息
window.YF_CHAT_SHOPIFY_PRODUCT = {
    id: 商品ID,
    title: '商品标题',
    price: 价格,
    url: '当前页面URL'
};
```

### 2. 购物车操作集成
```javascript
// 添加商品到购物车
YF_CHAT_SHOPIFY_CART.addToCart(variantId, quantity)
    .then(data => console.log('已添加到购物车'))
    .catch(error => console.error('添加失败'));

// 获取购物车信息
YF_CHAT_SHOPIFY_CART.getCart()
    .then(cart => console.log('购物车:', cart));
```

### 3. 客户信息集成
```liquid
{% if customer %}
window.YF_CHAT_SHOPIFY_CUSTOMER = {
    id: {{ customer.id }},
    email: '{{ customer.email }}',
    firstName: '{{ customer.first_name }}'
};
{% endif %}
```

## 📊 功能对比

| 功能 | WordPress版本 | Shopify版本 | 状态 |
|------|---------------|-------------|------|
| AI聊天对话 | ✅ | ✅ | 完全兼容 |
| 客服接管 | ✅ | ✅ | 完全兼容 |
| 聊天历史 | ✅ | ✅ | 完全兼容 |
| 实时推送 | ✅ | ✅ | 完全兼容 |
| 多LLM支持 | ✅ | ✅ | 完全兼容 |
| 商品信息集成 | ❌ | ✅ | Shopify独有 |
| 购物车操作 | ❌ | ✅ | Shopify独有 |
| 客户信息获取 | ✅ (WP用户) | ✅ (Shopify客户) | 平台适配 |

## 🎯 Dylan AI助手优势

### 环保电器专业咨询
- **产品专业性**: 除湿机、空气净化器专业知识
- **购买引导**: 直接引导到商品页面或购买链接
- **库存查询**: 可集成Shopify库存API
- **个性化推荐**: 基于客户浏览历史推荐

### Shopify电商场景优化
- **产品咨询**: 直接询问当前页面商品
- **比较推荐**: 对比不同型号产品
- **购买决策**: 协助客户做出购买决定
- **售后支持**: 订单查询、使用指导

## 🔍 故障排除

### 常见问题

#### 1. 聊天按钮不显示
```bash
# 检查清单
□ 文件是否正确上传到对应目录
□ theme.liquid是否正确添加包含代码
□ 浏览器控制台是否有错误
□ CSS文件是否正确加载
```

#### 2. 无法发送消息
```bash
# 检查清单
□ API Token是否正确配置
□ 后端服务是否正常运行
□ 网络连接是否正常
□ CORS设置是否正确
```

#### 3. 样式显示异常
```bash
# 检查清单
□ CSS文件是否正确上传
□ 主题是否有冲突样式
□ 浏览器缓存是否清除
□ 响应式设计是否适配
```

## 📈 性能优化

### 1. 加载优化
- CSS和JS文件压缩
- 图标使用SVG格式
- 懒加载非关键资源

### 2. API优化
- 合理设置轮询间隔
- 实现消息去重机制
- 优化网络请求频率

### 3. 用户体验优化
- 响应式设计适配
- 加载状态提示
- 错误处理机制

## 🔄 版本更新

### 更新流程
1. 下载新版本文件
2. 备份当前配置
3. 替换文件并更新配置
4. 测试功能是否正常

### 版本历史
- **v1.0.0**: 初始版本，基于WordPress插件改造
- 功能完整性: 95%
- 兼容性: 支持所有现代浏览器

---
*基于YF AI Chat Now WordPress插件改造，为Shopify商家提供专业的AI客服解决方案！*
