#!/usr/bin/env python3
"""
后端路径管理工具
确保exe能够在其所在目录读取和创建配置文件和数据文件
支持开发环境和打包环境的路径处理
"""

import os
import sys
from pathlib import Path
from typing import Optional


def get_backend_directory() -> Path:
    """
    获取backend目录
    - 开发环境：返回backend目录
    - 打包环境：返回exe所在目录
    """
    if getattr(sys, 'frozen', False):
        # 打包后的环境 - exe所在目录就是backend目录
        exe_path = Path(sys.executable)
        return exe_path.parent
    else:
        # 开发环境 - 从utils目录向上找到backend目录
        script_path = Path(__file__)
        # 当前文件在 backend/utils/path_utils.py
        # 所以backend目录是上级目录的上级目录
        return script_path.parent.parent


def get_data_directory() -> Path:
    """获取数据目录路径"""
    backend_dir = get_backend_directory()
    data_dir = backend_dir / "data"
    data_dir.mkdir(exist_ok=True)
    return data_dir


def get_database_path() -> Path:
    """获取数据库文件路径"""
    # 数据库文件存储在data目录中
    data_dir = get_data_directory()
    return data_dir / "chat_data.db"


def get_config_directory() -> Path:
    """获取config目录路径"""
    backend_dir = get_backend_directory()
    config_dir = backend_dir / "config"
    config_dir.mkdir(exist_ok=True)
    return config_dir


def get_salesgpt_config_directory() -> Path:
    """获取salesgpt_config目录路径"""
    backend_dir = get_backend_directory()
    salesgpt_config_dir = backend_dir / "salesgpt_config"
    salesgpt_config_dir.mkdir(exist_ok=True)
    return salesgpt_config_dir


def get_blacklist_cache_directory() -> Path:
    """获取blacklist_cache目录路径"""
    backend_dir = get_backend_directory()
    blacklist_dir = backend_dir / "blacklist_cache"
    blacklist_dir.mkdir(exist_ok=True)
    return blacklist_dir


def get_history_directory() -> Path:
    """获取history目录路径"""
    backend_dir = get_backend_directory()
    history_dir = backend_dir / "history"
    history_dir.mkdir(exist_ok=True)
    return history_dir


def get_logs_directory() -> Path:
    """获取logs目录路径"""
    backend_dir = get_backend_directory()
    logs_dir = backend_dir / "logs"
    logs_dir.mkdir(exist_ok=True)
    return logs_dir


def get_ssl_directory() -> Path:
    """获取SSL证书目录路径"""
    backend_dir = get_backend_directory()
    ssl_dir = backend_dir / "ssl"
    ssl_dir.mkdir(exist_ok=True)
    return ssl_dir


def get_ssl_cert_path() -> Optional[Path]:
    """
    获取SSL证书文件路径
    支持环境变量配置和自动检测

    Returns:
        证书文件路径，如果未找到则返回None
    """
    ssl_dir = get_ssl_directory()

    # 首先检查环境变量指定的证书文件
    cert_file = os.getenv("SSL_CERT_FILE", "ssl/certificate.pem")
    cert_path = get_file_path(cert_file)

    if cert_path.exists():
        return cert_path

    # 如果启用自动检测，搜索常见证书文件名
    if os.getenv("SSL_AUTO_DETECT", "true").lower() == "true":
        common_cert_names = [
            "certificate.pem",
            "cert.pem",
            "server.crt",
            "ssl.crt",
            "fullchain.pem"
        ]

        for cert_name in common_cert_names:
            auto_path = ssl_dir / cert_name
            if auto_path.exists():
                return auto_path

    return None


def get_ssl_key_path() -> Optional[Path]:
    """
    获取SSL私钥文件路径
    支持环境变量配置和自动检测

    Returns:
        私钥文件路径，如果未找到则返回None
    """
    ssl_dir = get_ssl_directory()

    # 首先检查环境变量指定的私钥文件
    key_file = os.getenv("SSL_KEY_FILE", "ssl/private.key")
    key_path = get_file_path(key_file)

    if key_path.exists():
        return key_path

    # 如果启用自动检测，搜索常见私钥文件名
    if os.getenv("SSL_AUTO_DETECT", "true").lower() == "true":
        common_key_names = [
            "private.key",
            "privkey.pem",
            "server.key",
            "ssl.key",
            "key.pem"
        ]

        for key_name in common_key_names:
            auto_path = ssl_dir / key_name
            if auto_path.exists():
                return auto_path

    return None


def get_env_file_path() -> Path:
    """获取.env文件路径"""
    backend_dir = get_backend_directory()
    return backend_dir / ".env"


def get_file_path(relative_path: str) -> Path:
    """
    获取相对于backend目录的文件路径
    
    Args:
        relative_path: 相对于backend目录的路径
    
    Returns:
        完整的文件路径
    """
    backend_dir = get_backend_directory()
    return backend_dir / relative_path


def ensure_directory_exists(directory_path: Path) -> None:
    """确保目录存在，如果不存在则创建"""
    directory_path.mkdir(parents=True, exist_ok=True)


def get_database_url() -> str:
    """
    获取数据库URL
    兼容开发环境和打包环境
    """
    db_path = get_database_path()
    return f"sqlite+aiosqlite:///{db_path}"


def copy_default_files_if_needed():
    """
    如果必要的文件不存在，则从默认模板复制
    """
    backend_dir = get_backend_directory()
    
    # 检查并复制.env文件
    env_file = get_env_file_path()
    env_example = backend_dir / ".env.example"
    
    if not env_file.exists() and env_example.exists():
        import shutil
        shutil.copy2(env_example, env_file)
        print(f"✅ 已从模板创建.env文件: {env_file}")
    
    # 确保必要的目录存在
    directories = [
        get_data_directory(),
        get_config_directory(),
        get_salesgpt_config_directory(),
        get_blacklist_cache_directory(),
        get_history_directory(),
        get_logs_directory(),
        get_ssl_directory()
    ]
    
    for directory in directories:
        ensure_directory_exists(directory)


def get_runtime_info() -> dict:
    """获取运行时环境信息，用于调试"""
    backend_dir = get_backend_directory()
    
    return {
        "is_frozen": getattr(sys, 'frozen', False),
        "executable_path": sys.executable if getattr(sys, 'frozen', False) else None,
        "script_path": __file__ if not getattr(sys, 'frozen', False) else None,
        "backend_directory": str(backend_dir),
        "database_path": str(get_database_path()),
        "env_file_path": str(get_env_file_path()),
        "data_directory": str(get_data_directory()),
        "config_directory": str(get_config_directory()),
        "salesgpt_config_directory": str(get_salesgpt_config_directory()),
        "blacklist_cache_directory": str(get_blacklist_cache_directory()),
        "history_directory": str(get_history_directory()),
        "logs_directory": str(get_logs_directory()),
        "ssl_directory": str(get_ssl_directory()),
        "ssl_cert_path": str(get_ssl_cert_path()) if get_ssl_cert_path() else None,
        "ssl_key_path": str(get_ssl_key_path()) if get_ssl_key_path() else None
    }


if __name__ == "__main__":
    """测试路径管理功能"""
    print("🔍 后端路径管理工具测试")
    print("=" * 50)
    
    info = get_runtime_info()
    for key, value in info.items():
        print(f"{key}: {value}")
    
    print("\n📁 检查目录结构...")
    copy_default_files_if_needed()
    print("✅ 路径管理工具测试完成")
