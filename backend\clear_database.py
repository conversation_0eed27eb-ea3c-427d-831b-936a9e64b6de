#!/usr/bin/env python3
"""
数据库清空工具
用于清空聊天数据库，方便测试和重新开始
"""
import os
import sqlite3
import sys
from datetime import datetime

def get_database_path():
    """获取数据库文件路径"""
    # 使用新的路径管理工具
    from utils.path_utils import get_database_path as get_db_path
    return str(get_db_path())

def backup_database(db_path):
    """备份数据库"""
    if not os.path.exists(db_path):
        print(f"⚠️  数据库文件不存在: {db_path}")
        return None
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{db_path}.backup_{timestamp}"
    
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ 数据库已备份到: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return None

def show_database_stats(db_path):
    """显示数据库统计信息"""
    if not os.path.exists(db_path):
        print(f"⚠️  数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        if not tables:
            print("📊 数据库为空（无表）")
            conn.close()
            return
        
        print("📊 当前数据库统计:")
        print("=" * 40)
        
        # 统计会话数量
        try:
            cursor.execute("SELECT COUNT(*) FROM chat_sessions")
            session_count = cursor.fetchone()[0]
            print(f"会话数量: {session_count}")
        except sqlite3.OperationalError:
            print("会话表不存在")
        
        # 统计消息数量
        try:
            cursor.execute("SELECT COUNT(*) FROM chat_messages")
            message_count = cursor.fetchone()[0]
            print(f"消息数量: {message_count}")
            
            # 按发送者统计
            cursor.execute("SELECT sender, COUNT(*) FROM chat_messages GROUP BY sender")
            sender_stats = cursor.fetchall()
            for sender, count in sender_stats:
                print(f"  - {sender}: {count} 条")
                
        except sqlite3.OperationalError:
            print("消息表不存在")
        
        # 显示最近的会话
        try:
            cursor.execute("""
                SELECT id, created_at, updated_at 
                FROM chat_sessions 
                ORDER BY updated_at DESC 
                LIMIT 5
            """)
            recent_sessions = cursor.fetchall()
            
            if recent_sessions:
                print("\n最近的会话:")
                for session_id, created, updated in recent_sessions:
                    print(f"  - {session_id[:30]}... (更新: {updated})")
                    
        except sqlite3.OperationalError:
            pass
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 读取数据库统计失败: {e}")

def clear_database(db_path, confirm=True):
    """清空数据库"""
    if confirm:
        print(f"\n⚠️  即将清空数据库: {db_path}")
        response = input("确定要继续吗？这将删除所有聊天记录！(y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("❌ 操作已取消")
            return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        if not tables:
            print("📊 数据库已经是空的")
            conn.close()
            return True
        
        # 清空所有表
        cleared_tables = []
        for (table_name,) in tables:
            try:
                cursor.execute(f"DELETE FROM {table_name}")
                row_count = cursor.rowcount
                cleared_tables.append((table_name, row_count))
                print(f"✅ 清空表 {table_name}: {row_count} 行")
            except Exception as e:
                print(f"❌ 清空表 {table_name} 失败: {e}")
        
        # 重置自增ID
        try:
            cursor.execute("DELETE FROM sqlite_sequence")
            print("✅ 重置自增ID序列")
        except Exception:
            pass  # 如果没有自增字段，这个操作会失败，但不影响清空
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 数据库清空完成！")
        print(f"共清空 {len(cleared_tables)} 个表")
        
        return True
        
    except Exception as e:
        print(f"❌ 清空数据库失败: {e}")
        return False

def main():
    """主函数"""
    print("🗑️  YF AI Chat 数据库清空工具")
    print("=" * 50)
    
    # 获取数据库路径
    db_path = get_database_path()
    print(f"数据库路径: {db_path}")
    
    # 解析命令行参数
    force_clear = '--force' in sys.argv or '-f' in sys.argv
    no_backup = '--no-backup' in sys.argv
    stats_only = '--stats' in sys.argv or '-s' in sys.argv
    
    # 显示帮助
    if '--help' in sys.argv or '-h' in sys.argv:
        print("\n使用方法:")
        print("  python clear_database.py [选项]")
        print("\n选项:")
        print("  -h, --help      显示帮助信息")
        print("  -s, --stats     只显示统计信息，不清空")
        print("  -f, --force     强制清空，不询问确认")
        print("  --no-backup     不创建备份")
        print("\n示例:")
        print("  python clear_database.py              # 交互式清空")
        print("  python clear_database.py --stats      # 只查看统计")
        print("  python clear_database.py --force      # 强制清空")
        return
    
    # 显示当前统计
    show_database_stats(db_path)
    
    # 如果只是查看统计，直接返回
    if stats_only:
        return
    
    # 创建备份（除非指定不备份）
    if not no_backup and os.path.exists(db_path):
        backup_path = backup_database(db_path)
        if not backup_path:
            print("⚠️  备份失败，是否继续？")
            response = input("继续清空数据库？(y/N): ")
            if response.lower() not in ['y', 'yes']:
                print("❌ 操作已取消")
                return
    
    # 清空数据库
    success = clear_database(db_path, confirm=not force_clear)
    
    if success:
        print("\n📋 清空后统计:")
        show_database_stats(db_path)
        print("\n💡 提示:")
        print("  - 重启后端服务以重新初始化数据库")
        print("  - 清除浏览器localStorage以重置前端状态")
        print("  - 如需恢复数据，可使用备份文件")

if __name__ == "__main__":
    main()
