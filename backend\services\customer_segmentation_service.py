"""
客户细分服务
根据用户消息中的关键词识别客户类型，提供个性化的销售策略
"""

import json
import os
import re
from typing import Dict, Any, List, Tuple, Optional
from collections import Counter
import logging
from utils.path_utils import get_salesgpt_config_directory

logger = logging.getLogger(__name__)


class CustomerSegmentationService:
    """客户细分服务"""
    
    def __init__(self):
        self.config = {}
        self.customer_profiles = {}  # 存储客户档案
        self.load_config()
    
    def load_config(self):
        """加载客户细分配置"""
        try:
            config_dir = get_salesgpt_config_directory()
            config_path = config_dir / 'advanced_sales_config.json'
            with open(config_path, 'r', encoding='utf-8') as f:
                full_config = json.load(f)
                personalization = full_config.get('personalization', {})
                self.config = personalization.get('customer_segmentation', {})
            logger.info("客户细分配置加载成功")
        except Exception as e:
            logger.error(f"加载客户细分配置失败: {e}")
            self.config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "price_sensitive": {
                "keywords": ["price", "cost", "discount", "cheap", "budget", "affordable"],
                "sales_approach": "value_emphasis",
                "recommended_products": ["entry_level"],
                "persuasion_points": ["cost_effectiveness", "long_term_savings"]
            },
            "quality_focused": {
                "keywords": ["quality", "brand", "premium", "best", "top", "excellent"],
                "sales_approach": "technical_emphasis",
                "recommended_products": ["premium"],
                "persuasion_points": ["technical_specs", "certifications"]
            }
        }
    
    def analyze_customer_type(self, message: str, session_id: str = None) -> Tuple[str, float, Dict[str, Any]]:
        """
        分析客户类型
        
        Args:
            message: 用户消息
            session_id: 会话ID（可选，用于累积分析）
            
        Returns:
            Tuple[客户类型, 置信度, 详细信息]
        """
        message_lower = message.lower()
        type_scores = {}
        
        # 计算每种客户类型的匹配分数
        for customer_type, type_config in self.config.items():
            keywords = type_config.get('keywords', [])
            score = self._calculate_keyword_score(message_lower, keywords)
            type_scores[customer_type] = score
        
        # 找到最高分的客户类型
        if not type_scores:
            return "unknown", 0.0, {}
        
        best_type = max(type_scores, key=type_scores.get)
        best_score = type_scores[best_type]
        
        # 如果有会话ID，更新客户档案
        if session_id:
            self._update_customer_profile(session_id, best_type, best_score, message)
        
        # 获取客户类型的详细信息
        type_info = self.config.get(best_type, {})
        
        return best_type, best_score, {
            "sales_approach": type_info.get('sales_approach', ''),
            "recommended_products": type_info.get('recommended_products', []),
            "persuasion_points": type_info.get('persuasion_points', []),
            "characteristics": type_info.get('characteristics', []),
            "all_scores": type_scores
        }
    
    def _calculate_keyword_score(self, message: str, keywords: List[str]) -> float:
        """计算关键词匹配分数"""
        if not keywords:
            return 0.0
        
        message_words = re.findall(r'\b\w+\b', message.lower())
        if not message_words:
            return 0.0
        
        # 计算匹配的关键词数量
        matched_keywords = 0
        for keyword in keywords:
            if keyword.lower() in message:
                matched_keywords += 1
        
        # 计算分数：匹配关键词数 / 总关键词数
        score = matched_keywords / len(keywords)
        
        # 考虑消息长度的影响（避免长消息获得不公平的高分）
        length_factor = min(1.0, len(message_words) / 20)  # 20个词以内不惩罚
        
        return score * length_factor
    
    def _update_customer_profile(self, session_id: str, customer_type: str, score: float, message: str):
        """更新客户档案"""
        if session_id not in self.customer_profiles:
            self.customer_profiles[session_id] = {
                "type_history": [],
                "messages": [],
                "dominant_type": None,
                "confidence": 0.0
            }
        
        profile = self.customer_profiles[session_id]
        profile["type_history"].append({
            "type": customer_type,
            "score": score,
            "message": message[:100]  # 只保存前100个字符
        })
        profile["messages"].append(message)
        
        # 计算主导客户类型
        type_counts = Counter([entry["type"] for entry in profile["type_history"]])
        dominant_type = type_counts.most_common(1)[0][0] if type_counts else "unknown"
        
        # 计算平均置信度
        type_scores = [entry["score"] for entry in profile["type_history"] if entry["type"] == dominant_type]
        avg_confidence = sum(type_scores) / len(type_scores) if type_scores else 0.0
        
        profile["dominant_type"] = dominant_type
        profile["confidence"] = avg_confidence
    
    def get_customer_profile(self, session_id: str) -> Dict[str, Any]:
        """获取客户档案"""
        return self.customer_profiles.get(session_id, {
            "dominant_type": "unknown",
            "confidence": 0.0,
            "type_history": [],
            "messages": []
        })
    
    def get_sales_strategy(self, customer_type: str) -> Dict[str, Any]:
        """获取销售策略"""
        type_config = self.config.get(customer_type, {})
        return {
            "sales_approach": type_config.get('sales_approach', 'general'),
            "recommended_products": type_config.get('recommended_products', []),
            "persuasion_points": type_config.get('persuasion_points', []),
            "communication_style": self._get_communication_style(customer_type),
            "key_messages": self._get_key_messages(customer_type)
        }
    
    def _get_communication_style(self, customer_type: str) -> str:
        """根据客户类型获取沟通风格"""
        style_mapping = {
            "price_sensitive": "concise",
            "quality_focused": "detailed", 
            "convenience_focused": "friendly",
            "health_focused": "detailed"
        }
        return style_mapping.get(customer_type, "friendly")
    
    def _get_key_messages(self, customer_type: str) -> List[str]:
        """根据客户类型获取关键信息"""
        messages_mapping = {
            "price_sensitive": [
                "Great value for money",
                "Long-term cost savings",
                "Competitive pricing",
                "No hidden costs"
            ],
            "quality_focused": [
                "Premium quality materials",
                "Advanced technology",
                "Industry certifications",
                "Proven performance"
            ],
            "convenience_focused": [
                "Easy to use",
                "Minimal maintenance",
                "Smart automation",
                "Time-saving features"
            ],
            "health_focused": [
                "Health benefits",
                "Safety certified",
                "Medical grade",
                "Family protection"
            ]
        }
        return messages_mapping.get(customer_type, ["High quality air purification solutions"])
    
    def get_customer_types(self) -> List[str]:
        """获取所有客户类型"""
        return list(self.config.keys())
    
    def get_type_keywords(self, customer_type: str) -> List[str]:
        """获取客户类型的关键词"""
        return self.config.get(customer_type, {}).get('keywords', [])


# 全局实例
customer_segmentation_service = CustomerSegmentationService()
