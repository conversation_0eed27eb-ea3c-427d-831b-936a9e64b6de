#!/usr/bin/env python3
"""
配置管理模块
负责保存和加载数据库路径等配置信息
"""

import json
import os
import sys
from pathlib import Path
from typing import List, Optional

class ConfigManager:
    def __init__(self):
        # 获取配置文件路径
        if getattr(sys, 'frozen', False):
            # 如果是打包后的exe文件
            self.config_dir = Path(os.path.dirname(sys.executable))
        else:
            # 如果是Python脚本
            self.config_dir = Path(os.path.dirname(os.path.abspath(__file__))).parent
        
        self.config_file = self.config_dir / "config.json"
        self.config = self._load_config()
    
    def _load_config(self) -> dict:
        """加载配置文件"""
        default_config = {
            "database_paths": [],
            "last_database_path": "",
            "window_geometry": {
                "width": 1200,
                "height": 800,
                "x": 100,
                "y": 100
            },
            "table_settings": {
                "rows_per_page": 100,
                "auto_refresh": True
            },
            "export_settings": {
                "default_encoding": "utf-8",
                "default_separator": ",",
                "include_headers": True
            },
            "query_history": []
        }
        
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置，确保所有必要的键都存在
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            except (json.JSONDecodeError, FileNotFoundError):
                return default_config
        else:
            return default_config
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def add_database_path(self, path: str):
        """添加数据库路径到历史记录"""
        if path and path not in self.config["database_paths"]:
            self.config["database_paths"].insert(0, path)
            # 只保留最近的10个路径
            self.config["database_paths"] = self.config["database_paths"][:10]
        self.config["last_database_path"] = path
        self.save_config()
    
    def get_database_paths(self) -> List[str]:
        """获取数据库路径历史记录"""
        return self.config["database_paths"]
    
    def get_last_database_path(self) -> str:
        """获取上次使用的数据库路径"""
        return self.config["last_database_path"]
    
    def set_window_geometry(self, width: int, height: int, x: int, y: int):
        """保存窗口几何信息"""
        self.config["window_geometry"] = {
            "width": width,
            "height": height,
            "x": x,
            "y": y
        }
        self.save_config()
    
    def get_window_geometry(self) -> dict:
        """获取窗口几何信息"""
        return self.config["window_geometry"]
    
    def add_query_to_history(self, query: str):
        """添加查询到历史记录"""
        if query and query not in self.config["query_history"]:
            self.config["query_history"].insert(0, query)
            # 只保留最近的50个查询
            self.config["query_history"] = self.config["query_history"][:50]
            self.save_config()
    
    def get_query_history(self) -> List[str]:
        """获取查询历史记录"""
        return self.config["query_history"]
    
    def get_table_settings(self) -> dict:
        """获取表格设置"""
        return self.config["table_settings"]
    
    def update_table_settings(self, settings: dict):
        """更新表格设置"""
        self.config["table_settings"].update(settings)
        self.save_config()
    
    def get_export_settings(self) -> dict:
        """获取导出设置"""
        return self.config["export_settings"]
    
    def update_export_settings(self, settings: dict):
        """更新导出设置"""
        self.config["export_settings"].update(settings)
        self.save_config()

# 全局配置管理器实例
config_manager = ConfigManager()
