# Shopify插件故障排除指南

## 🚨 常见错误及解决方案

### 1. "Illegal return statement" 语法错误

**错误信息**: `Uncaught SyntaxError: Illegal return statement`

**原因**: JavaScript文件中存在不在函数内部的 `return` 语句

**解决方案**: 
- ✅ 已修复：更新了 `yf-chat-shopify.js` 和 `yf-chat-widget.liquid` 文件
- 确保使用最新版本的文件

### 2. "Configuration not found" 错误

**错误信息**: `YF AI Chat: Configuration not found`

**原因**: 配置对象未正确加载

**解决方案**:
1. 确保 `snippets/yf-chat-widget.liquid` 文件已正确上传
2. 确保在 `theme.liquid` 中正确包含了代码片段：
   ```liquid
   {% include 'yf-chat-widget' %}
   ```
3. 检查文件路径是否正确

### 3. 聊天按钮不显示

**可能原因**:
- CSS文件未正确加载
- JavaScript初始化失败
- 配置验证失败

**解决步骤**:
1. 打开浏览器开发者工具 (F12)
2. 查看控制台 (Console) 是否有错误信息
3. 检查网络 (Network) 标签，确认所有文件都成功加载
4. 验证配置信息是否正确

### 4. API连接失败

**错误信息**: 网络请求失败或超时

**检查项目**:
1. **API URL**: 确保使用正确的地址 `https://chat.22668.xyz` (Cloudflare隧道已处理端口转发)
2. **API Token**: 确保token有效且长度大于10个字符
3. **网络连接**: 确保服务器可访问
4. **CORS设置**: 确保后端允许跨域请求

## 🔧 配置验证清单

### 必需配置项
- [ ] `apiUrl`: 后端API地址
- [ ] `apiToken`: 有效的API令牌
- [ ] CSS文件正确加载
- [ ] JavaScript文件正确加载

### 文件上传清单
- [ ] `assets/yf-chat-shopify.js` - 主要JavaScript文件
- [ ] `assets/yf-chat-shopify.css` - 样式文件  
- [ ] `snippets/yf-chat-widget.liquid` - 配置和初始化代码

### 主题集成清单
- [ ] 在 `theme.liquid` 的 `</body>` 前添加 `{% include 'yf-chat-widget' %}`
- [ ] 清除浏览器缓存
- [ ] 测试聊天功能

## 🐛 调试技巧

### 1. 启用详细日志
在浏览器控制台中运行：
```javascript
// 检查配置是否加载
console.log('Config loaded:', typeof window.YF_CHAT_CONFIG !== 'undefined');

// 检查配置内容（注意：生产环境中不要这样做）
if (window.YF_CHAT_CONFIG) {
    console.log('API URL configured:', !!window.YF_CHAT_CONFIG.apiUrl);
    console.log('Token configured:', !!window.YF_CHAT_CONFIG.apiToken);
}
```

### 2. 检查DOM元素
```javascript
// 检查聊天按钮是否存在
console.log('Chat button:', document.getElementById('yf-chat-button'));

// 检查聊天窗口是否存在
console.log('Chat window:', document.getElementById('yf-chat-window'));
```

### 3. 网络请求测试
```javascript
// 测试API连接
fetch('https://chat.22668.xyz/api/health')
    .then(response => console.log('API Status:', response.status))
    .catch(error => console.log('API Error:', error));
```

## 📞 获取支持

如果以上解决方案都无法解决问题，请提供以下信息：

1. **错误信息**: 浏览器控制台的完整错误信息
2. **Shopify主题**: 使用的主题名称和版本
3. **浏览器信息**: 浏览器类型和版本
4. **配置信息**: API URL和Token配置（隐藏敏感信息）
5. **网络状态**: 是否可以访问 `https://chat.22668.xyz`

## 🔄 版本更新日志

### v2.1.5 (最新) - 生产就绪版本
- ✅ 移除了扳手测试按钮 (生产环境清理)
- ✅ 修复了 "Illegal return statement" 语法错误
- ✅ 改进了配置验证逻辑
- ✅ 增强了错误处理机制
- ✅ 恢复正确的API URL配置 (无端口号，Cloudflare隧道处理)

### v2.1.3
- 修复按钮显示问题
- 增强CSS优先级

### v2.1.2
- 安全增强版本
- 清理控制台日志输出
