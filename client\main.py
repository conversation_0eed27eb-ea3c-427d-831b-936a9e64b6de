import sys
import os
import json
import async<PERSON>
import websockets
from datetime import datetime
from typing import Dict, List, Optional
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QSplitter, QListWidget, QTextEdit, QLineEdit, QPushButton,
    QLabel, QTabWidget, QTableWidget, QTableWidgetItem,
    QHeaderView, QMessageBox, QDialog, QFormLayout,
    QDialogButtonBox, QComboBox, QSpinBox, QCheckBox,
    QGroupBox, QScrollArea, QFrame, QProgressBar, QGraphicsDropShadowEffect,
    QMenu, QDateEdit, QListWidgetItem, QInputDialog
)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QSettings, QPropertyAnimation, QEasingCurve, QRect, QParallelAnimationG<PERSON>, QDate, QObject
from PyQt6.QtGui import QFont, QIcon, QPixmap, QAction, QColor, QPalette
import requests
import base64
import re
from dotenv import load_dotenv
from user_manager import UserManager
from utils.file_logger import client_logger as logger

# Windows系统事件监听
if sys.platform == "win32":
    try:
        import win32gui
        import win32con
        import win32api
        WINDOWS_API_AVAILABLE = True
    except ImportError:
        WINDOWS_API_AVAILABLE = False
        logger.warning("Windows API不可用，系统状态监听功能将被禁用")
else:
    WINDOWS_API_AVAILABLE = False

# Load environment variables from exe directory
from config_utils import get_config_path, ensure_all_config_files

# 确保所有配置文件存在
ensure_all_config_files()

# 从exe所在目录加载.env文件
env_path = get_config_path('.env')
load_dotenv(env_path)

# 时区配置
try:
    from timezone_utils import format_time_for_chat, format_time_for_session_list, parse_iso_time, get_current_time
    TIMEZONE_AVAILABLE = True
    print("✅ 客户端时区配置模块加载成功，使用洛杉矶时间")
except ImportError as e:
    print(f"⚠️ 时区配置模块不可用: {e}，使用系统默认时区")
    TIMEZONE_AVAILABLE = False

    # 提供备用函数
    def format_time_for_chat(dt):
        if dt is None:
            return ""
        if isinstance(dt, str):
            try:
                dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
            except:
                return ""
        return dt.strftime('%H:%M:%S')

    def format_time_for_session_list(dt):
        if dt is None:
            return ""
        if isinstance(dt, str):
            try:
                dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
            except:
                return ""
        return dt.strftime('%m-%d %H:%M')

    def parse_iso_time(iso_string):
        if not iso_string:
            return None
        try:
            return datetime.fromisoformat(iso_string.replace('Z', '+00:00'))
        except:
            return None

    def get_current_time():
        return datetime.now()

class DPIHelper:
    """增强的DPI适配助手类"""

    @staticmethod
    def debug_dpi_info():
        """调试DPI信息"""
        screen = QApplication.primaryScreen()
        if screen:
            device_ratio = screen.devicePixelRatio()
            logical_dpi = screen.logicalDotsPerInch()
            logical_ratio = logical_dpi / 96.0
            physical_dpi = screen.physicalDotsPerInch()

            print(f"🔍 DPI调试信息:")
            print(f"  设备像素比: {device_ratio}")
            print(f"  逻辑DPI: {logical_dpi} (比例: {logical_ratio:.2f})")
            print(f"  物理DPI: {physical_dpi}")
            print(f"  最终使用比例: {DPIHelper.get_dpi_ratio():.2f}")
            print(f"  屏幕尺寸: {screen.size().width()}x{screen.size().height()}")
            print(f"  可用区域: {screen.availableGeometry().width()}x{screen.availableGeometry().height()}")

    @staticmethod
    def get_dpi_ratio():
        """获取DPI缩放比例 - 改进的高DPI检测"""
        screen = QApplication.primaryScreen()
        if screen:
            # 使用devicePixelRatio获取更准确的缩放比例
            device_ratio = screen.devicePixelRatio()
            logical_ratio = screen.logicalDotsPerInch() / 96.0

            # 综合考虑设备像素比和逻辑DPI，优先使用逻辑DPI
            # 这样可以更好地处理Windows的DPI缩放设置
            if logical_ratio > 1.0:
                ratio = logical_ratio
            else:
                ratio = device_ratio

            # 限制DPI缩放范围，避免过度缩放
            return max(1.0, min(ratio, 3.0))
        return 1.0

    @staticmethod
    def get_screen_geometry():
        """获取屏幕几何信息"""
        screen = QApplication.primaryScreen()
        if screen:
            return screen.availableGeometry()
        return None

    @staticmethod
    def scale_size(size):
        """智能缩放尺寸 - 改进的缩放策略"""
        ratio = DPIHelper.get_dpi_ratio()

        # 根据不同的DPI范围使用不同的缩放策略
        # 对于高DPI环境，使用更保守的缩放以避免元素过大
        if ratio <= 1.0:
            return size
        elif ratio <= 1.25:
            return int(size * ratio)
        elif ratio <= 1.5:
            return int(size * (1.0 + (ratio - 1.0) * 0.9))  # 从0.8提高到0.9
        elif ratio <= 2.0:
            return int(size * (1.0 + (ratio - 1.0) * 0.8))  # 从0.7提高到0.8
        else:
            return int(size * (1.0 + (ratio - 1.0) * 0.7))  # 从0.6提高到0.7

    @staticmethod
    def scale_font_size(size):
        """智能缩放字体大小"""
        ratio = DPIHelper.get_dpi_ratio()

        # 字体缩放更加保守，确保可读性
        if ratio <= 1.0:
            return size
        elif ratio <= 1.25:
            return int(size * ratio)
        elif ratio <= 1.5:
            return int(size * (1.0 + (ratio - 1.0) * 0.6))
        elif ratio <= 2.0:
            return int(size * (1.0 + (ratio - 1.0) * 0.5))
        else:
            return int(size * (1.0 + (ratio - 1.0) * 0.4))

    @staticmethod
    def get_optimal_window_size(base_width, base_height):
        """获取最佳窗口大小"""
        ratio = DPIHelper.get_dpi_ratio()
        screen_geometry = DPIHelper.get_screen_geometry()

        # 基础缩放
        if ratio <= 1.25:
            scale_factor = ratio
        else:
            scale_factor = 1.0 + (ratio - 1.0) * 0.75

        scaled_width = int(base_width * scale_factor)
        scaled_height = int(base_height * scale_factor)

        # 确保窗口不会超出屏幕
        if screen_geometry:
            max_width = int(screen_geometry.width() * 0.8)
            max_height = int(screen_geometry.height() * 0.8)
            scaled_width = min(scaled_width, max_width)
            scaled_height = min(scaled_height, max_height)

        return scaled_width, scaled_height

    @staticmethod
    def get_spacing(base_spacing):
        """获取适配的间距 - 改进的间距计算"""
        ratio = DPIHelper.get_dpi_ratio()
        # 对于间距，使用更保守的缩放以避免元素过于分散
        if ratio <= 1.25:
            return max(base_spacing, int(base_spacing * ratio))
        else:
            # 高DPI环境下，间距缩放更保守
            return max(base_spacing, int(base_spacing * (1.0 + (ratio - 1.0) * 0.6)))

    @staticmethod
    def get_padding(base_padding):
        """获取适配的内边距 - 改进的内边距计算"""
        ratio = DPIHelper.get_dpi_ratio()
        # 对于内边距，使用更保守的缩放
        if ratio <= 1.25:
            return max(base_padding, int(base_padding * ratio))
        else:
            # 高DPI环境下，内边距缩放更保守
            return max(base_padding, int(base_padding * (1.0 + (ratio - 1.0) * 0.5)))

class AppleStyleSheet:
    """Apple风格样式表管理类"""

    @staticmethod
    def get_main_style():
        return """
        /* 主窗口样式 - Apple风格 */
        QMainWindow {
            background-color: #F2F2F7;
            color: #1C1C1E;
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Arial, sans-serif;
        }

        /* Apple风格按钮 */
        QPushButton {
            background-color: #007AFF;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-size: 15px;
            font-weight: 600;
            min-height: 24px;
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Arial, sans-serif;
        }

        QPushButton:hover {
            background-color: #0056CC;
        }

        QPushButton:pressed {
            background-color: #004499;
        }

        QPushButton:disabled {
            background-color: #C7C7CC;
            color: #8E8E93;
        }

        /* Apple风格输入框 */
        QLineEdit {
            border: 1px solid #D1D1D6;
            border-radius: 10px;
            padding: 12px 16px;
            font-size: 15px;
            background-color: #FFFFFF;
            selection-background-color: #007AFF;
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Arial, sans-serif;
        }

        QLineEdit:focus {
            border-color: #007AFF;
            outline: none;
        }

        /* Apple风格文本区域 */
        QTextEdit {
            border: 1px solid #D1D1D6;
            border-radius: 10px;
            padding: 12px;
            font-size: 15px;
            background-color: #FFFFFF;
            selection-background-color: #007AFF;
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Arial, sans-serif;
        }

        QTextEdit:focus {
            border-color: #007AFF;
        }

        /* Apple风格列表 */
        QListWidget {
            border: 1px solid #D1D1D6;
            border-radius: 12px;
            background-color: #FFFFFF;
            alternate-background-color: #F2F2F7;
            selection-background-color: #007AFF;
            selection-color: white;
            outline: none;
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Arial, sans-serif;
        }

        QListWidget::item {
            padding: 12px 16px;
            border-bottom: 1px solid #F2F2F7;
            border-radius: 8px;
            margin: 1px;
        }

        QListWidget::item:hover {
            background-color: #F2F2F7;
        }

        QListWidget::item:selected {
            background-color: #007AFF;
            color: white;
        }

        /* Apple风格标签 */
        QLabel {
            color: #1C1C1E;
            font-size: 15px;
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Arial, sans-serif;
        }

        /* Apple风格分割器 */
        QSplitter::handle {
            background-color: #D1D1D6;
            width: 1px;
            height: 1px;
        }

        QSplitter::handle:hover {
            background-color: #007AFF;
        }

        /* Apple风格状态栏 */
        QStatusBar {
            background-color: #FFFFFF;
            border-top: 1px solid #D1D1D6;
            color: #8E8E93;
            font-size: 13px;
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Arial, sans-serif;
        }

        /* Apple风格对话框 */
        QDialog {
            background-color: #FFFFFF;
            border-radius: 16px;
        }

        /* Apple风格表单 */
        QFormLayout QLabel {
            font-weight: 600;
            color: #1C1C1E;
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Arial, sans-serif;
        }

        /* Apple风格滚动条 */
        QScrollBar:vertical {
            background-color: transparent;
            width: 8px;
            border-radius: 4px;
        }

        QScrollBar::handle:vertical {
            background-color: #C7C7CC;
            border-radius: 4px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background-color: #8E8E93;
        }

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            height: 0px;
        }
        """

    @staticmethod
    def get_card_style():
        """Apple风格卡片样式"""
        return """
        QFrame[cardStyle="true"] {
            background-color: #FFFFFF;
            border-radius: 16px;
            border: 1px solid #D1D1D6;
        }
        """

    @staticmethod
    def get_primary_button_style():
        """Apple风格主要按钮样式"""
        return """
        QPushButton[buttonStyle="primary"] {
            background-color: #007AFF;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-size: 15px;
            font-weight: 600;
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Arial, sans-serif;
        }

        QPushButton[buttonStyle="primary"]:hover {
            background-color: #0056CC;
        }
        """

    @staticmethod
    def get_secondary_button_style():
        """Apple风格次要按钮样式"""
        return """
        QPushButton[buttonStyle="secondary"] {
            background-color: transparent;
            color: #007AFF;
            border: 1px solid #007AFF;
            border-radius: 12px;
            padding: 10px 20px;
            font-size: 15px;
            font-weight: 500;
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Arial, sans-serif;
        }

        QPushButton[buttonStyle="secondary"]:hover {
            background-color: rgba(0, 122, 255, 0.1);
        }
        """

class LoadingWidget(QWidget):
    """现代化加载组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(40, 40)
        self.angle = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.rotate)
        self.timer.setInterval(50)  # 20 FPS

    def paintEvent(self, event):
        from PyQt6.QtGui import QPainter, QPen
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 绘制旋转的圆环
        pen = QPen(QColor("#2196F3"))
        pen.setWidth(3)
        pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(pen)

        rect = self.rect().adjusted(5, 5, -5, -5)
        painter.drawArc(rect, self.angle * 16, 120 * 16)

    def rotate(self):
        self.angle = (self.angle + 10) % 360
        self.update()

    def start_animation(self):
        self.timer.start()
        self.show()

    def stop_animation(self):
        self.timer.stop()
        self.hide()

class AnimatedButton(QPushButton):
    """带动画效果的按钮 - 修复悬停位置偏移"""

    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        # 简化动画效果，避免CSS属性警告

    def enterEvent(self, event):
        # 简化悬停效果，避免CSS警告
        super().enterEvent(event)

    def leaveEvent(self, event):
        # 简化离开效果
        super().leaveEvent(event)

class ModernCard(QFrame):
    """现代化卡片组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setProperty("cardStyle", True)
        self.setup_shadow()

    def setup_shadow(self):
        """添加阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)

class ArchiveManager:
    """归档管理器 - 处理会话归档和查看"""

    def __init__(self):
        self.archive_file = os.path.join(os.path.dirname(__file__), 'archived_sessions.json')
        self.archived_sessions = self.load_archived_sessions()

    def load_archived_sessions(self):
        """加载归档的会话"""
        try:
            if os.path.exists(self.archive_file):
                with open(self.archive_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"加载归档会话失败: {e}")
            return {}

    def save_archived_sessions(self):
        """保存归档的会话"""
        try:
            with open(self.archive_file, 'w', encoding='utf-8') as f:
                json.dump(self.archived_sessions, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存归档会话失败: {e}")
            return False

    def archive_session(self, session_data, messages=None):
        """归档会话"""
        try:
            session_id = session_data['id']
            archive_time = get_current_time().isoformat()

            # 创建归档记录
            archived_session = {
                'session_data': session_data,
                'messages': messages or [],
                'archived_at': archive_time,
                'archive_category': self.get_archive_category(session_data)
            }

            self.archived_sessions[session_id] = archived_session
            return self.save_archived_sessions()
        except Exception as e:
            logger.error(f"归档会话失败: {e}")
            return False

    def get_archive_category(self, session_data):
        """根据会话数据确定归档分类"""
        created_time = parse_iso_time(session_data.get('created_at'))
        if not created_time:
            return "其他"

        now = get_current_time()
        # 确保时区一致性
        if created_time.tzinfo is None:
            created_time = created_time.replace(tzinfo=now.tzinfo)
        elif now.tzinfo is None:
            now = now.replace(tzinfo=created_time.tzinfo)

        # 计算日期差异（使用日期而不是时间戳，避免时区问题）
        created_date = created_time.date()
        now_date = now.date()
        days_ago = (now_date - created_date).days

        if days_ago == 0:
            return "今日"
        elif days_ago == 1:
            return "昨日"
        elif days_ago <= 7:
            return "本周"
        elif days_ago <= 30:
            return "本月"
        else:
            return "更早"

    def get_archived_sessions_by_category(self):
        """按分类获取归档会话"""
        categories = {
            "今日": [],
            "昨日": [],
            "本周": [],
            "本月": [],
            "更早": [],
            "其他": []
        }

        for session_id, archived_session in self.archived_sessions.items():
            category = archived_session.get('archive_category', '其他')
            categories[category].append({
                'session_id': session_id,
                'session_data': archived_session['session_data'],
                'messages': archived_session['messages'],
                'archived_at': archived_session['archived_at']
            })

        # 按归档时间排序
        for category in categories:
            categories[category].sort(key=lambda x: x['archived_at'], reverse=True)

        return categories

    def remove_archived_session(self, session_id):
        """删除归档会话"""
        if session_id in self.archived_sessions:
            del self.archived_sessions[session_id]
            return self.save_archived_sessions()
        return False

    def get_archived_session(self, session_id):
        """获取特定的归档会话"""
        return self.archived_sessions.get(session_id)

class ArchiveViewDialog(QDialog):
    """归档查看对话框"""

    def __init__(self, parent=None, api_base_url=None, api_headers=None, admin_id=None):
        super().__init__(parent)
        self.api_base_url = api_base_url
        self.api_headers = api_headers
        self.admin_id = admin_id
        self.archived_sessions = []
        self.setup_ui()
        self.load_archived_sessions()

    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("📁 归档会话")
        self.setModal(True)
        self.resize(1000, 700)

        # 居中显示
        if self.parent():
            parent_geometry = self.parent().geometry()
            x = parent_geometry.x() + (parent_geometry.width() - self.width()) // 2
            y = parent_geometry.y() + (parent_geometry.height() - self.height()) // 2
            self.move(x, y)

        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("📁 归档会话管理")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #1C1C1E;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # 主要内容区域
        content_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：分类列表
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 10, 0)

        category_label = QLabel("📂 分类")
        category_label.setStyleSheet("font-weight: bold; font-size: 16px; margin-bottom: 5px;")
        left_layout.addWidget(category_label)

        self.category_list = QListWidget()
        self.category_list.setMaximumWidth(200)
        self.category_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #D1D1D6;
                border-radius: 8px;
                background-color: #FFFFFF;
                font-size: 14px;
            }
            QListWidget::item {
                padding: 8px 12px;
                border-bottom: 1px solid #F2F2F7;
            }
            QListWidget::item:selected {
                background-color: #007AFF;
                color: white;
            }
        """)
        self.category_list.currentItemChanged.connect(self.on_category_changed)
        left_layout.addWidget(self.category_list)

        # 右侧：会话列表和详情
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(10, 0, 0, 0)

        # 会话列表
        sessions_label = QLabel("💬 会话列表")
        sessions_label.setStyleSheet("font-weight: bold; font-size: 16px; margin-bottom: 5px;")
        right_layout.addWidget(sessions_label)

        self.sessions_list = QListWidget()
        self.sessions_list.setMaximumHeight(200)
        self.sessions_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #D1D1D6;
                border-radius: 8px;
                background-color: #FFFFFF;
                font-size: 13px;
            }
            QListWidget::item {
                padding: 10px 12px;
                border-bottom: 1px solid #F2F2F7;
            }
            QListWidget::item:selected {
                background-color: #007AFF;
                color: white;
            }
        """)
        self.sessions_list.currentItemChanged.connect(self.on_session_changed)
        right_layout.addWidget(self.sessions_list)

        # 消息显示区域
        messages_label = QLabel("📝 消息内容")
        messages_label.setStyleSheet("font-weight: bold; font-size: 16px; margin-bottom: 5px; margin-top: 10px;")
        right_layout.addWidget(messages_label)

        self.messages_area = QTextEdit()
        self.messages_area.setReadOnly(True)
        self.messages_area.setStyleSheet("""
            QTextEdit {
                border: 1px solid #D1D1D6;
                border-radius: 8px;
                background-color: #FFFFFF;
                font-size: 14px;
                line-height: 1.5;
            }
        """)
        right_layout.addWidget(self.messages_area)

        content_splitter.addWidget(left_panel)
        content_splitter.addWidget(right_panel)
        content_splitter.setSizes([200, 800])

        layout.addWidget(content_splitter)

        # 底部按钮
        button_layout = QHBoxLayout()

        self.delete_btn = AnimatedButton("↩️ 取消归档")
        self.delete_btn.setProperty("buttonStyle", "secondary")
        self.delete_btn.clicked.connect(self.delete_selected_archive)
        self.delete_btn.setEnabled(False)

        self.close_btn = AnimatedButton("关闭")
        self.close_btn.setProperty("buttonStyle", "primary")
        self.close_btn.clicked.connect(self.accept)

        button_layout.addWidget(self.delete_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)

        layout.addLayout(button_layout)

    def load_archived_sessions(self):
        """从后端API加载归档会话"""
        try:
            if not self.api_base_url or not self.api_headers:
                QMessageBox.warning(self, "错误", "API配置不完整")
                return

            # 从后端获取归档会话
            response = requests.get(
                f"{self.api_base_url}/api/admin/archived-sessions",
                headers=self.api_headers,
                params={"admin_id": self.admin_id} if self.admin_id else {},
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                self.archived_sessions = data.get('sessions', [])
                self.update_category_list()
            else:
                error_msg = response.json().get('detail', '未知错误') if response.content else '网络错误'
                QMessageBox.warning(self, "加载失败", f"无法加载归档会话: {error_msg}")

        except requests.exceptions.RequestException as e:
            logger.error(f"加载归档会话网络请求失败: {e}")
            QMessageBox.warning(self, "网络错误", f"无法连接到服务器: {str(e)}")
        except Exception as e:
            logger.error(f"加载归档会话失败: {e}")
            QMessageBox.warning(self, "错误", f"加载归档会话时出现错误: {str(e)}")

    def update_category_list(self):
        """更新分类列表"""
        # 按归档时间分类
        categories = {
            "今日": [],
            "昨日": [],
            "本周": [],
            "本月": [],
            "更早": []
        }

        for session in self.archived_sessions:
            archived_at = parse_iso_time(session.get('archived_at'))
            if not archived_at:
                categories["更早"].append(session)
                continue

            now = get_current_time()
            # 确保时区一致性
            if archived_at.tzinfo is None:
                archived_at = archived_at.replace(tzinfo=now.tzinfo)
            elif now.tzinfo is None:
                now = now.replace(tzinfo=archived_at.tzinfo)

            # 计算日期差异
            archived_date = archived_at.date()
            now_date = now.date()
            days_ago = (now_date - archived_date).days

            if days_ago == 0:
                categories["今日"].append(session)
            elif days_ago == 1:
                categories["昨日"].append(session)
            elif days_ago <= 7:
                categories["本周"].append(session)
            elif days_ago <= 30:
                categories["本月"].append(session)
            else:
                categories["更早"].append(session)

        # 清空分类列表
        self.category_list.clear()

        # 添加有内容的分类
        for category, sessions in categories.items():
            if sessions:  # 只显示有会话的分类
                item = QListWidgetItem(f"{category} ({len(sessions)})")
                item.setData(Qt.ItemDataRole.UserRole, (category, sessions))
                self.category_list.addItem(item)

        # 默认选择第一个分类
        if self.category_list.count() > 0:
            self.category_list.setCurrentRow(0)

    def on_category_changed(self, current, previous):
        """分类选择改变"""
        if not current:
            return

        category_data = current.data(Qt.ItemDataRole.UserRole)
        if not category_data:
            return

        category, sessions = category_data

        # 清空会话列表
        self.sessions_list.clear()

        # 添加会话
        for session in sessions:
            session_id = session.get('id')

            # 格式化显示文本
            created_time = parse_iso_time(session.get('created_at'))
            if created_time:
                time_str = format_time_for_session_list(created_time)
            else:
                time_str = "未知时间"

            archived_time = parse_iso_time(session.get('archived_at'))
            if archived_time:
                archived_str = format_time_for_session_list(archived_time)
            else:
                archived_str = "未知时间"

            archived_by = session.get('archived_by', '未知')
            display_text = f"会话 {session_id[:8]}...\n创建: {time_str}\n归档: {archived_str}\n归档人: {archived_by}"

            item = QListWidgetItem(display_text)
            item.setData(Qt.ItemDataRole.UserRole, session)
            self.sessions_list.addItem(item)

        # 清空消息显示
        self.messages_area.clear()
        self.delete_btn.setEnabled(False)

    def on_session_changed(self, current, previous):
        """会话选择改变"""
        if not current:
            self.messages_area.clear()
            self.delete_btn.setEnabled(False)
            return

        session = current.data(Qt.ItemDataRole.UserRole)
        session_id = session.get('id')

        # 从后端获取会话消息
        self.load_session_messages(session_id)
        self.delete_btn.setEnabled(True)

    def load_session_messages(self, session_id):
        """从后端加载会话消息"""
        try:
            response = requests.get(
                f"{self.api_base_url}/api/admin/session/{session_id}/messages",
                headers=self.api_headers,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                messages = data.get('messages', [])
                self.display_messages(messages)
            else:
                self.messages_area.setPlainText("无法加载消息内容")

        except Exception as e:
            logger.error(f"加载归档会话消息失败: {e}")
            self.messages_area.setPlainText(f"加载消息失败: {str(e)}")

    def display_messages(self, messages):
        """显示消息"""
        html_content = "<div style='font-family: Microsoft YaHei; line-height: 1.6;'>"

        for msg in messages:
            timestamp = parse_iso_time(msg.get('created_at'))
            if timestamp:
                time_str = format_time_for_chat(timestamp)
            else:
                time_str = "未知时间"

            sender = msg.get('sender', 'unknown')
            content = msg.get('content', '')

            # 根据发送者设置样式
            if sender == 'user':
                html_content += f"""
                <div style='margin: 10px 0; text-align: right;'>
                    <div style='background-color: #007AFF; color: white; padding: 10px 15px;
                               border-radius: 18px; display: inline-block; max-width: 70%;
                               word-wrap: break-word; margin-left: 30%;'>
                        {content}
                    </div>
                    <div style='font-size: 12px; color: #8E8E93; margin-top: 5px;'>
                        用户 • {time_str}
                    </div>
                </div>
                """
            elif sender == 'assistant':
                html_content += f"""
                <div style='margin: 10px 0; text-align: left;'>
                    <div style='background-color: #F2F2F7; color: #1C1C1E; padding: 10px 15px;
                               border-radius: 18px; display: inline-block; max-width: 70%;
                               word-wrap: break-word; margin-right: 30%;'>
                        {content}
                    </div>
                    <div style='font-size: 12px; color: #8E8E93; margin-top: 5px;'>
                        AI助手 • {time_str}
                    </div>
                </div>
                """
            elif sender == 'admin':
                html_content += f"""
                <div style='margin: 10px 0; text-align: left;'>
                    <div style='background-color: #34C759; color: white; padding: 10px 15px;
                               border-radius: 18px; display: inline-block; max-width: 70%;
                               word-wrap: break-word; margin-right: 30%;'>
                        {content}
                    </div>
                    <div style='font-size: 12px; color: #8E8E93; margin-top: 5px;'>
                        客服 • {time_str}
                    </div>
                </div>
                """

        html_content += "</div>"
        self.messages_area.setHtml(html_content)

    def delete_selected_archive(self):
        """取消归档选中的会话"""
        current_item = self.sessions_list.currentItem()
        if not current_item:
            return

        session = current_item.data(Qt.ItemDataRole.UserRole)
        session_id = session.get('id')

        reply = QMessageBox.question(
            self, "确认取消归档",
            f"确定要取消归档会话 {session_id[:12]}... 吗？\n会话将重新出现在活跃会话列表中。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                response = requests.post(
                    f"{self.api_base_url}/api/admin/session/{session_id}/unarchive",
                    headers=self.api_headers,
                    timeout=10
                )

                if response.status_code == 200:
                    # 重新加载归档列表
                    self.load_archived_sessions()
                    QMessageBox.information(self, "操作成功", "会话已取消归档")
                else:
                    error_msg = response.json().get('detail', '未知错误') if response.content else '网络错误'
                    QMessageBox.warning(self, "操作失败", f"取消归档失败: {error_msg}")

            except Exception as e:
                logger.error(f"取消归档失败: {e}")
                QMessageBox.warning(self, "操作失败", f"取消归档时出现错误: {str(e)}")

class AsyncWorker(QThread):
    """异步工作线程基类"""
    finished = pyqtSignal(object)
    error = pyqtSignal(str)

    def __init__(self, func, *args, **kwargs):
        super().__init__()
        self.func = func
        self.args = args
        self.kwargs = kwargs

    def run(self):
        try:
            result = self.func(*self.args, **self.kwargs)
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))

class SystemEventMonitor(QObject):
    """系统事件监听器 - 监听锁屏/解锁事件"""
    screen_unlocked = pyqtSignal()
    screen_locked = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.monitoring = False
        self.check_timer = QTimer()
        self.check_timer.timeout.connect(self.check_system_state)
        self.last_foreground_window = None
        self.was_locked = False

    def start_monitoring(self):
        """开始监听系统状态"""
        if not WINDOWS_API_AVAILABLE:
            logger.warning("Windows API不可用，无法监听系统状态")
            return

        self.monitoring = True
        # 每5秒检查一次系统状态
        self.check_timer.start(5000)
        logger.info("系统状态监听已启动")

    def stop_monitoring(self):
        """停止监听系统状态"""
        self.monitoring = False
        self.check_timer.stop()
        logger.info("系统状态监听已停止")

    def check_system_state(self):
        """检查系统状态（简化版本，检测屏幕锁定状态）"""
        if not self.monitoring or not WINDOWS_API_AVAILABLE:
            return

        try:
            # 检测当前前台窗口
            current_window = win32gui.GetForegroundWindow()

            # 检测是否处于锁屏状态
            # 当系统锁定时，通常没有前台窗口或前台窗口为特殊的系统窗口
            is_locked = self.is_screen_locked()

            if is_locked and not self.was_locked:
                # 刚刚锁屏
                self.was_locked = True
                self.screen_locked.emit()
                logger.info("检测到系统锁屏")

            elif not is_locked and self.was_locked:
                # 刚刚解锁
                self.was_locked = False
                self.screen_unlocked.emit()
                logger.info("检测到系统解锁")

        except Exception as e:
            logger.error(f"检查系统状态时出错: {e}")

    def is_screen_locked(self):
        """检测屏幕是否被锁定"""
        if not WINDOWS_API_AVAILABLE:
            return False

        try:
            # 方法1：检查是否有活动的前台窗口
            foreground_window = win32gui.GetForegroundWindow()
            if foreground_window == 0:
                return True

            # 方法2：检查桌面窗口
            desktop_window = win32gui.GetDesktopWindow()
            if foreground_window == desktop_window:
                return True

            # 方法3：尝试获取窗口标题
            try:
                window_title = win32gui.GetWindowText(foreground_window)
                # 如果窗口标题为空或包含锁屏相关关键词
                if not window_title or "lock" in window_title.lower():
                    return True
            except:
                return True

            return False

        except Exception as e:
            logger.error(f"检测屏幕锁定状态时出错: {e}")
            return False

class WebSocketThread(QThread):
    message_received = pyqtSignal(dict)
    connection_status = pyqtSignal(bool)
    connection_error = pyqtSignal(str)
    health_check_needed = pyqtSignal()  # 新增信号用于健康检查

    def __init__(self, server_url, admin_id, token):
        super().__init__()
        self.server_url = server_url
        self.admin_id = admin_id
        self.token = token
        self.websocket = None
        self.running = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5  # seconds
        self.last_activity = datetime.now()
        # 移除定时器，改用信号机制

    async def connect_websocket(self):
        while self.running and self.reconnect_attempts < self.max_reconnect_attempts:
            try:
                uri = f"{self.server_url}/ws/admin/{self.admin_id}"
                headers = {"Authorization": f"Bearer {self.token}"}

                async with websockets.connect(
                    uri,
                    extra_headers=headers,
                    ping_interval=30,  # 心跳检测
                    ping_timeout=10,
                    close_timeout=5  # 减少关闭超时时间
                ) as websocket:
                    self.websocket = websocket
                    self.connection_status.emit(True)
                    self.reconnect_attempts = 0  # 重置重连计数

                    try:
                        async for message in websocket:
                            if not self.running:
                                break

                            try:
                                data = json.loads(message)
                                self.update_activity()  # 更新活动时间
                                self.message_received.emit(data)
                            except json.JSONDecodeError as e:
                                print(f"Invalid JSON received: {message}, error: {e}")
                    except websockets.exceptions.ConnectionClosed:
                        print("WebSocket connection closed during message loop")
                    finally:
                        # 确保websocket引用被清理
                        self.websocket = None

            except websockets.exceptions.ConnectionClosed:
                print("WebSocket connection closed")
                self.websocket = None
                self.connection_status.emit(False)
                if self.running:
                    await self.attempt_reconnect()
            except Exception as e:
                error_msg = f"WebSocket connection error: {e}"
                print(error_msg)
                self.websocket = None
                self.connection_error.emit(error_msg)
                self.connection_status.emit(False)
                if self.running:
                    await self.attempt_reconnect()

    async def attempt_reconnect(self):
        """尝试重新连接"""
        self.reconnect_attempts += 1
        if self.reconnect_attempts < self.max_reconnect_attempts:
            print(f"Attempting to reconnect ({self.reconnect_attempts}/{self.max_reconnect_attempts})...")
            await asyncio.sleep(self.reconnect_delay)
        else:
            self.connection_error.emit("Maximum reconnection attempts reached")

    def check_connection_health(self):
        """检查连接健康状态"""
        if not self.running:
            return

        # 检查连接是否活跃
        now = datetime.now()
        time_since_activity = (now - self.last_activity).total_seconds()

        # 如果超过60秒没有活动，认为连接可能有问题
        if time_since_activity > 60:
            logger.warning(f"WebSocket连接可能不活跃，上次活动: {time_since_activity:.1f}秒前")
            if self.websocket:
                # 尝试重新连接
                self.force_reconnect()

    def force_reconnect(self):
        """强制重新连接"""
        logger.info("强制重新连接WebSocket")
        self.reconnect_attempts = 0  # 重置重连计数
        if self.websocket:
            self.websocket = None
        # 连接会在下一个循环中自动重试

    def update_activity(self):
        """更新活动时间"""
        self.last_activity = datetime.now()

    def run(self):
        self.running = True
        # 不在子线程中启动定时器
        asyncio.run(self.connect_websocket())

    def stop(self):
        self.running = False
        # 不在子线程中操作定时器
        if self.websocket:
            try:
                # 安全地关闭WebSocket连接
                if hasattr(self.websocket, 'close'):
                    # 尝试优雅关闭，但不强制创建新的事件循环
                    try:
                        # 检查是否在同一个线程的事件循环中
                        loop = asyncio.get_running_loop()
                        if loop and not loop.is_closed():
                            # 在当前循环中创建关闭任务
                            asyncio.create_task(self.websocket.close())
                        else:
                            # 如果没有运行的循环，直接设置websocket为None
                            # 让连接自然超时关闭，避免跨循环操作
                            self.websocket = None
                    except RuntimeError:
                        # 没有运行的事件循环，不强制关闭
                        # WebSocket连接会在线程结束时自动清理
                        self.websocket = None
                        print("WebSocket will be closed when thread terminates")
            except Exception as e:
                print(f"Error during websocket cleanup: {e}")
                self.websocket = None

        # 优雅退出线程
        self.quit()
        self.wait(3000)  # 等待最多3秒

    def send_message(self, message_data):
        """发送消息（线程安全）"""
        if self.websocket and self.running:
            # 使用QTimer在主线程中调度发送
            QTimer.singleShot(0, lambda: self._send_message_async(message_data))

    def _send_message_async(self, message_data):
        """异步发送消息"""
        if self.websocket and self.running:
            try:
                # 检查是否在WebSocket线程的事件循环中
                try:
                    loop = asyncio.get_running_loop()
                    if loop and not loop.is_closed():
                        # 在当前循环中发送消息
                        asyncio.create_task(self.websocket.send(json.dumps(message_data)))
                    else:
                        print("No running event loop for sending message")
                except RuntimeError:
                    # 没有运行的事件循环，消息发送失败
                    print("Cannot send message: no event loop available")
            except Exception as e:
                print(f"Error sending message: {e}")

class LoginDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)

        # 从设置中获取应用标题
        self.settings = QSettings("YFCompany", "YFAIChat")
        app_title = self.settings.value("app_title", "YF AI Chat - 智能客服管理系统")
        self.setWindowTitle(f"{app_title} - 管理员登录")

        # 智能DPI适配窗口大小 - 针对125%缩放优化
        dpi_ratio = DPIHelper.get_dpi_ratio()
        if dpi_ratio >= 1.25:
            # 高DPI环境下使用更大的基础尺寸
            base_width, base_height = 500, 650
        else:
            base_width, base_height = 480, 600

        width, height = DPIHelper.get_optimal_window_size(base_width, base_height)

        # 确保最小尺寸以防止重叠，针对125%缩放调整
        min_width = DPIHelper.scale_size(420)
        min_height = DPIHelper.scale_size(580)
        width = max(width, min_width)
        height = max(height, min_height)

        self.setFixedSize(width, height)

        # 设置窗口属性 - 简化以避免DPI问题
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.FramelessWindowHint)
        # 移除透明背景以避免UpdateLayeredWindowIndirect错误
        # self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # 初始化组件
        self.loading_widget = None
        self.shadow_effect = None
        self.setup_ui()
        self.setup_animations()
        self.setup_shadow_effect()

    def setup_ui(self):
        # 创建主容器
        self.main_container = QFrame(self)
        self.main_container.setObjectName("mainContainer")

        # 设置现代化样式 - 简化以避免透明度问题
        border_radius = DPIHelper.scale_size(16)
        self.setStyleSheet(f"""
            QDialog {{
                background-color: #F5F5F5;
            }}
            QFrame#mainContainer {{
                background-color: white;
                border-radius: {border_radius}px;
                border: 2px solid #E0E0E0;
            }}
        """)

        # 主布局
        dialog_layout = QVBoxLayout(self)
        dialog_layout.setContentsMargins(0, 0, 0, 0)
        dialog_layout.addWidget(self.main_container)

        # 容器内布局 - 智能间距，根据DPI动态调整
        main_layout = QVBoxLayout(self.main_container)
        dpi_ratio = DPIHelper.get_dpi_ratio()

        # 根据DPI比例动态调整内边距和间距
        if dpi_ratio >= 1.25:
            # 高DPI环境下使用更大的间距
            padding = DPIHelper.get_padding(32)
            spacing = DPIHelper.get_spacing(18)
        else:
            padding = DPIHelper.get_padding(28)
            spacing = DPIHelper.get_spacing(16)

        main_layout.setContentsMargins(padding, padding, padding, padding)
        main_layout.setSpacing(spacing)

        # 标题区域 - 现代化设计，优化间距
        title_layout = QVBoxLayout()
        title_layout.setSpacing(DPIHelper.get_spacing(10))  # 减少标题区域间距

        # Logo区域 - 使用渐变背景的图标，适当缩小以节省空间
        icon_size = DPIHelper.scale_size(64)  # 从80减少到64
        icon_container = QFrame()
        icon_container.setFixedSize(icon_size, icon_size)
        icon_container.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                border-radius: {icon_size // 2}px;
                border: 2px solid rgba(33, 150, 243, 0.3);
            }}
        """)

        icon_layout = QVBoxLayout(icon_container)
        icon_layout.setContentsMargins(0, 0, 0, 0)

        icon_label = QLabel("🔐")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setFont(QFont("Segoe UI Emoji", DPIHelper.scale_font_size(24)))  # 从32减少到24
        icon_label.setStyleSheet("color: white; background: transparent; border: none;")
        icon_layout.addWidget(icon_label)

        # 居中图标容器
        icon_center_layout = QHBoxLayout()
        icon_center_layout.addStretch()
        icon_center_layout.addWidget(icon_container)
        icon_center_layout.addStretch()

        # 主标题 - 使用应用设置中的标题
        app_title = self.settings.value("app_title", "YF AI Chat - 智能客服管理系统")
        title = QLabel(app_title)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFont(QFont("Microsoft YaHei", DPIHelper.scale_font_size(18), QFont.Weight.Bold))  # 从20减少到18
        title.setStyleSheet("color: #1976D2; margin: 4px 0;")  # 减少边距

        # 副标题 - 适当缩小字体
        subtitle = QLabel("请输入您的管理员凭据以继续")
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle.setFont(QFont("Microsoft YaHei", DPIHelper.scale_font_size(11)))  # 从12减少到11
        subtitle.setStyleSheet("color: #757575; margin: 2px 0 8px 0;")  # 减少边距

        title_layout.addLayout(icon_center_layout)
        title_layout.addWidget(title)
        title_layout.addWidget(subtitle)
        main_layout.addLayout(title_layout)

        # 表单区域 - 现代化设计，优化间距
        form_layout = QVBoxLayout()
        form_layout.setSpacing(DPIHelper.get_spacing(16))  # 从20减少到16

        # 创建输入框样式 - 适当缩小以节省空间
        input_height = DPIHelper.scale_size(42)  # 从48减少到42
        input_radius = DPIHelper.scale_size(10)  # 从12减少到10
        input_padding_v = DPIHelper.scale_size(10)  # 从12减少到10
        input_padding_h = DPIHelper.scale_size(14)  # 从16减少到14
        font_size = DPIHelper.scale_font_size(13)  # 从14减少到13

        input_style = f"""
            QLineEdit {{
                border: 2px solid #E8EAF0;
                border-radius: {input_radius}px;
                padding: {input_padding_v}px {input_padding_h}px;
                font-size: {font_size}px;
                background-color: #FAFBFC;
                color: #2D3748;
                selection-background-color: #2196F3;
                selection-color: white;
                min-height: {input_height}px;
                max-height: {input_height}px;
            }}
            QLineEdit:focus {{
                border-color: #2196F3;
                background-color: white;
            }}
            QLineEdit:hover {{
                border-color: #CBD5E0;
                background-color: white;
            }}
        """

        # 用户名输入组 - 优化间距
        username_group = QVBoxLayout()
        username_group.setSpacing(DPIHelper.get_spacing(6))  # 从8减少到6

        username_label = QLabel("👤 用户名")
        username_label.setFont(QFont("Microsoft YaHei", DPIHelper.scale_font_size(12), QFont.Weight.Medium))  # 从13减少到12
        username_label.setStyleSheet("color: #4A5568; margin: 0; padding: 0;")

        self.username_input = QLineEdit()
        self.username_input.setText(os.getenv("DEFAULT_ADMIN_USERNAME", "admin"))
        self.username_input.setPlaceholderText("请输入管理员用户名")
        self.username_input.setFont(QFont("Microsoft YaHei", DPIHelper.scale_font_size(13)))  # 从14减少到13
        self.username_input.setStyleSheet(input_style)

        username_group.addWidget(username_label)
        username_group.addWidget(self.username_input)
        form_layout.addLayout(username_group)

        # 添加用户名和密码之间的间距 - 减少间距
        form_layout.addSpacing(DPIHelper.get_spacing(12))  # 从16减少到12

        # 密码输入组 - 优化间距
        password_group = QVBoxLayout()
        password_group.setSpacing(DPIHelper.get_spacing(6))  # 从8减少到6

        password_label = QLabel("🔒 密码")
        password_label.setFont(QFont("Microsoft YaHei", DPIHelper.scale_font_size(12), QFont.Weight.Medium))  # 从13减少到12
        password_label.setStyleSheet("color: #4A5568; margin: 0; padding: 0;")

        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setText(os.getenv("DEFAULT_ADMIN_PASSWORD", "admin"))
        self.password_input.setPlaceholderText("请输入管理员密码")
        self.password_input.setFont(QFont("Microsoft YaHei", DPIHelper.scale_font_size(13)))  # 从14减少到13
        self.password_input.setStyleSheet(input_style)

        # 回车键登录
        self.password_input.returnPressed.connect(self.accept)

        password_group.addWidget(password_label)
        password_group.addWidget(self.password_input)
        form_layout.addLayout(password_group)

        # 添加表单到主布局
        main_layout.addLayout(form_layout)

        # 添加弹性空间 - 减少间距
        main_layout.addSpacing(DPIHelper.get_spacing(24))  # 从32减少到24

        # 按钮区域 - 现代化设计，优化尺寸
        button_layout = QHBoxLayout()
        button_layout.setSpacing(DPIHelper.get_spacing(10))  # 从12减少到10
        button_layout.setContentsMargins(0, 0, 0, 0)

        # 按钮尺寸 - 适当缩小以节省空间
        button_height = DPIHelper.scale_size(42)  # 从48减少到42
        button_radius = DPIHelper.scale_size(10)  # 从12减少到10
        button_font_size = DPIHelper.scale_font_size(13)  # 从14减少到13

        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setFont(QFont("Microsoft YaHei", button_font_size, QFont.Weight.Medium))
        self.cancel_btn.clicked.connect(self.reject)
        self.cancel_btn.setCursor(Qt.CursorShape.PointingHandCursor)

        cancel_style = f"""
            QPushButton {{
                background-color: #F7FAFC;
                color: #4A5568;
                border: 2px solid #E2E8F0;
                border-radius: {button_radius}px;
                padding: 0px 24px;
                font-size: {button_font_size}px;
                font-weight: 500;
                min-height: {button_height}px;
                max-height: {button_height}px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: #EDF2F7;
                border-color: #CBD5E0;
                color: #2D3748;
            }}
            QPushButton:pressed {{
                background-color: #E2E8F0;
            }}
        """

        # 登录按钮
        self.login_btn = QPushButton("登录")
        self.login_btn.setFont(QFont("Microsoft YaHei", button_font_size, QFont.Weight.Bold))
        self.login_btn.clicked.connect(self.accept)
        self.login_btn.setDefault(True)
        self.login_btn.setCursor(Qt.CursorShape.PointingHandCursor)

        login_style = f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                border: none;
                border-radius: {button_radius}px;
                padding: 0px 32px;
                font-size: {button_font_size}px;
                font-weight: 600;
                min-height: {button_height}px;
                max-height: {button_height}px;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976D2, stop:1 #1565C0);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1565C0, stop:1 #0D47A1);
            }}
            QPushButton:disabled {{
                background-color: #CBD5E0;
                color: #A0AEC0;
            }}
        """

        self.cancel_btn.setStyleSheet(cancel_style)
        self.login_btn.setStyleSheet(login_style)

        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.login_btn)

        # 添加按钮到主布局
        main_layout.addLayout(button_layout)

    def setup_shadow_effect(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(DPIHelper.scale_size(30))
        self.shadow_effect.setColor(QColor(0, 0, 0, 60))
        self.shadow_effect.setOffset(0, DPIHelper.scale_size(8))
        self.main_container.setGraphicsEffect(self.shadow_effect)

    def setup_animations(self):
        """设置动画效果"""
        # 初始化动画对象为None，在需要时创建
        self.fade_animation = None
        self.scale_animation = None
        self.animation_group = None

    def create_animations(self):
        """创建动画对象"""
        if not self.fade_animation:
            # 淡入淡出动画
            self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
            self.fade_animation.setDuration(400)
            self.fade_animation.setEasingCurve(QEasingCurve.Type.OutCubic)

        if not self.scale_animation:
            # 缩放动画
            self.scale_animation = QPropertyAnimation(self.main_container, b"geometry")
            self.scale_animation.setDuration(400)
            self.scale_animation.setEasingCurve(QEasingCurve.Type.OutBack)

    def showEvent(self, event):
        """显示时的动画效果和居中显示"""
        super().showEvent(event)

        # 智能居中显示
        self.center_on_screen()

        # 创建动画对象
        self.create_animations()

        # 设置初始状态
        self.setWindowOpacity(0)

        # 设置容器初始缩放
        final_geometry = self.main_container.geometry()
        scale_factor = 0.8
        scaled_width = int(final_geometry.width() * scale_factor)
        scaled_height = int(final_geometry.height() * scale_factor)

        initial_geometry = QRect(
            final_geometry.x() + (final_geometry.width() - scaled_width) // 2,
            final_geometry.y() + (final_geometry.height() - scaled_height) // 2,
            scaled_width,
            scaled_height
        )
        self.main_container.setGeometry(initial_geometry)

        # 启动动画组
        if not self.animation_group:
            self.animation_group = QParallelAnimationGroup()

        # 确保动画对象存在后再设置
        if self.fade_animation and self.scale_animation:
            # 淡入动画
            self.fade_animation.setStartValue(0)
            self.fade_animation.setEndValue(1)

            # 缩放动画
            self.scale_animation.setStartValue(initial_geometry)
            self.scale_animation.setEndValue(final_geometry)

            self.animation_group.addAnimation(self.fade_animation)
            self.animation_group.addAnimation(self.scale_animation)
            self.animation_group.start()
        else:
            # 如果动画对象创建失败，直接显示
            self.setWindowOpacity(1)

        # 智能焦点设置
        QTimer.singleShot(500, self.set_initial_focus)

    def center_on_screen(self):
        """智能居中显示 - 优先在桌面中央显示"""
        screen_geometry = DPIHelper.get_screen_geometry()
        if not screen_geometry:
            return

        # 直接在屏幕中央显示，不考虑父窗口
        # 这样确保登录窗口始终在桌面中央
        self.move(
            screen_geometry.x() + (screen_geometry.width() - self.width()) // 2,
            screen_geometry.y() + (screen_geometry.height() - self.height()) // 2
        )

    def set_initial_focus(self):
        """设置初始焦点"""
        if self.username_input.text().strip():
            self.password_input.setFocus()
            self.password_input.selectAll()
        else:
            self.username_input.setFocus()

    def show_loading(self):
        """显示现代化加载状态"""
        if not self.loading_widget:
            self.loading_widget = LoadingWidget(self.main_container)
            # 居中放置加载动画
            container_rect = self.main_container.rect()
            self.loading_widget.move(
                container_rect.center().x() - 20,
                container_rect.center().y() - 20
            )

        self.loading_widget.start_animation()
        self.login_btn.setEnabled(False)
        self.login_btn.setText("🔄 登录中...")

        # 添加加载时的视觉反馈
        self.login_btn.setStyleSheet(self.login_btn.styleSheet() + """
            QPushButton:disabled {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #CBD5E0, stop:0.5 #A0AEC0, stop:1 #CBD5E0);
                color: #718096;
            }
        """)

    def hide_loading(self):
        """隐藏加载状态"""
        if self.loading_widget:
            self.loading_widget.stop_animation()

        self.login_btn.setEnabled(True)
        self.login_btn.setText("登录")

        # 恢复按钮样式
        button_height = DPIHelper.scale_size(48)
        button_radius = DPIHelper.scale_size(12)
        button_font_size = DPIHelper.scale_font_size(14)

        login_style = f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                border: none;
                border-radius: {button_radius}px;
                padding: 0px 32px;
                font-size: {button_font_size}px;
                font-weight: 600;
                min-height: {button_height}px;
                max-height: {button_height}px;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976D2, stop:1 #1565C0);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1565C0, stop:1 #0D47A1);
            }}
        """
        self.login_btn.setStyleSheet(login_style)

    def get_credentials(self):
        return self.username_input.text().strip(), self.password_input.text()

    def mousePressEvent(self, event):
        """处理鼠标按下事件，实现窗口拖拽"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """处理鼠标移动事件，实现窗口拖拽"""
        if (event.buttons() == Qt.MouseButton.LeftButton and
            hasattr(self, 'drag_position')):
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

    def keyPressEvent(self, event):
        """处理键盘事件"""
        if event.key() == Qt.Key.Key_Escape:
            self.reject()
        elif event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
            if not self.login_btn.isEnabled():
                return
            self.accept()
        else:
            super().keyPressEvent(event)

    def closeEvent(self, event):
        """处理关闭事件"""
        # 安全停止所有动画
        try:
            if hasattr(self, 'animation_group') and self.animation_group:
                self.animation_group.stop()
        except RuntimeError:
            pass  # 对象可能已被删除

        try:
            if hasattr(self, 'fade_animation') and self.fade_animation:
                self.fade_animation.stop()
        except RuntimeError:
            pass  # 对象可能已被删除

        try:
            if hasattr(self, 'scale_animation') and self.scale_animation:
                self.scale_animation.stop()
        except RuntimeError:
            pass  # 对象可能已被删除

        # 停止加载动画
        try:
            if self.loading_widget:
                self.loading_widget.stop_animation()
        except RuntimeError:
            pass  # 对象可能已被删除

        super().closeEvent(event)

class SessionListWidget(QListWidget):
    session_selected = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.sessions = {}
        self.session_items = {}  # 缓存项目映射
        self.itemClicked.connect(self.on_item_clicked)
        self.itemDoubleClicked.connect(self.on_item_double_clicked)
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
        self.setup_style()

    def setup_style(self):
        """设置现代化样式"""
        self.setStyleSheet("""
            QListWidget {
                border: none;
                background-color: transparent;
                outline: none;
            }
            QListWidget::item {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                padding: 12px;
                margin: 4px;
                min-height: 60px;
            }
            QListWidget::item:hover {
                background-color: #F5F5F5;
                border-color: #BDBDBD;
            }
            QListWidget::item:selected {
                background-color: #E3F2FD;
                border-color: #2196F3;
                color: #1976D2;
            }
        """)

    def add_session(self, session_data):
        """添加或更新会话"""
        session_id = session_data['id']

        # 检查是否已存在
        if session_id in self.sessions:
            # 更新现有会话
            self.update_session(session_data)
            return

        self.sessions[session_id] = session_data

        # 创建显示文本
        display_text = self.format_session_text(session_data)

        # 创建列表项
        from PyQt6.QtWidgets import QListWidgetItem
        item = QListWidgetItem(display_text)
        item.setData(Qt.ItemDataRole.UserRole, session_id)

        # 添加到列表
        self.addItem(item)
        self.session_items[session_id] = item

    def update_session(self, session_data):
        """更新现有会话"""
        session_id = session_data['id']
        if session_id in self.session_items:
            self.sessions[session_id] = session_data
            item = self.session_items[session_id]
            item.setText(self.format_session_text(session_data))

    def format_session_text(self, session_data):
        """格式化会话显示文本"""
        session_id = session_data['id']
        created_time = parse_iso_time(session_data['created_at'])
        if not created_time:
            created_time = datetime.fromisoformat(session_data['created_at'].replace('Z', '+00:00'))

        # 基本信息
        display_text = f"会话 {session_id[:8]}...\n"
        display_text += f"创建时间: {format_time_for_session_list(created_time)}\n"

        # 状态标识
        status_parts = []

        # 已读/未读状态 (模拟数据，实际应该从后端获取)
        is_read = session_data.get('is_read', False)
        if is_read:
            status_parts.append("✅ 已读")
        else:
            status_parts.append("🔴 未读")

        if session_data.get('admin_control'):
            status_parts.append("🔧 人工接管")
        if session_data.get('active', True):
            status_parts.append("🟢 活跃")
        else:
            status_parts.append("⚪ 非活跃")

        if status_parts:
            display_text += " | ".join(status_parts)

        return display_text

    def on_item_clicked(self, item):
        """处理项目点击"""
        session_id = item.data(Qt.ItemDataRole.UserRole)
        if session_id:
            self.session_selected.emit(session_id)

    def update_sessions(self, sessions_data):
        """高效更新会话列表（增量更新）"""
        new_session_ids = {session['id'] for session in sessions_data}
        current_session_ids = set(self.sessions.keys())

        # 移除不存在的会话
        to_remove = current_session_ids - new_session_ids
        for session_id in to_remove:
            self.remove_session(session_id)

        # 添加或更新会话
        for session in sessions_data:
            self.add_session(session)

    def remove_session(self, session_id):
        """移除会话"""
        if session_id in self.session_items:
            item = self.session_items[session_id]
            row = self.row(item)
            self.takeItem(row)
            del self.session_items[session_id]
            del self.sessions[session_id]

    def get_selected_session_id(self):
        """获取当前选中的会话ID"""
        current_item = self.currentItem()
        if current_item:
            return current_item.data(Qt.ItemDataRole.UserRole)
        return None

    def on_item_double_clicked(self, item):
        """处理双击事件 - 快速接管会话"""
        session_id = item.data(Qt.ItemDataRole.UserRole)
        if session_id:
            # 发出信号，让主窗口处理快速接管
            self.session_selected.emit(session_id)
            # 可以在这里添加快速接管的逻辑

    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.itemAt(position)
        if not item:
            return

        session_id = item.data(Qt.ItemDataRole.UserRole)
        session_data = self.sessions.get(session_id)
        if not session_data:
            return

        # 创建右键菜单
        menu = QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: #FFFFFF;
                border: 1px solid #D1D1D6;
                border-radius: 8px;
                padding: 5px;
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Arial, sans-serif;
            }
            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
                color: #1C1C1E;
            }
            QMenu::item:selected {
                background-color: #007AFF;
                color: white;
            }
        """)

        # 添加菜单项
        view_action = menu.addAction("👁️ 查看会话")
        view_action.triggered.connect(lambda: self.session_selected.emit(session_id))

        if session_data.get('admin_control'):
            release_action = menu.addAction("❌ 取消接管")
            release_action.triggered.connect(lambda: self.release_session_control(session_id))
        else:
            take_action = menu.addAction("🔧 接管会话")
            take_action.triggered.connect(lambda: self.take_session_control(session_id))

        menu.addSeparator()

        mark_read_action = menu.addAction("✅ 标记为已读")
        mark_read_action.triggered.connect(lambda: self.mark_session_read(session_id))

        archive_action = menu.addAction("📁 归档会话")
        archive_action.triggered.connect(lambda: self.archive_session(session_id))

        menu.addSeparator()

        copy_id_action = menu.addAction("📋 复制会话ID")
        copy_id_action.triggered.connect(lambda: self.copy_session_id(session_id))

        # 显示菜单
        menu.exec(self.mapToGlobal(position))

    def take_session_control(self, session_id):
        """接管会话控制"""
        # 这里可以发出信号或直接调用主窗口的方法
        print(f"接管会话: {session_id}")

    def release_session_control(self, session_id):
        """取消接管会话"""
        print(f"取消接管会话: {session_id}")

    def mark_session_read(self, session_id):
        """标记会话为已读"""
        if session_id in self.sessions:
            self.sessions[session_id]['is_read'] = True
            # 更新显示
            self.update_session(self.sessions[session_id])
            print(f"标记已读: {session_id}")

    def archive_session(self, session_id):
        """归档会话"""
        if session_id in self.sessions:
            # 获取主窗口引用来执行归档
            main_window = self.window()
            if hasattr(main_window, 'archive_session'):
                main_window.archive_session(session_id)
            else:
                print(f"归档会话: {session_id}")

    def copy_session_id(self, session_id):
        """复制会话ID到剪贴板"""
        from PyQt6.QtWidgets import QApplication
        clipboard = QApplication.clipboard()
        clipboard.setText(session_id)
        print(f"已复制会话ID: {session_id}")

    def update_session_control_status(self, session_id, admin_control):
        """更新会话的控制状态"""
        if session_id in self.sessions:
            self.sessions[session_id]['admin_control'] = admin_control
            # 更新显示
            self.update_session(self.sessions[session_id])

class ChatWidget(QWidget):
    message_sent = pyqtSignal(str, str)  # session_id, message
    control_requested = pyqtSignal(str, bool)  # session_id, take_control

    def __init__(self):
        super().__init__()
        self.current_session_id = None
        self.messages = []
        self.message_cache = {}  # 消息缓存
        self.message_ids = set()  # 用于去重的消息ID集合
        self.is_controlled = False
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 聊天管理工具栏 - 紧凑设计
        tools_card = ModernCard()
        tools_layout = QVBoxLayout(tools_card)
        tools_layout.setContentsMargins(12, 8, 12, 8)
        tools_layout.setSpacing(6)

        # 超紧凑工具栏 - 单行布局
        main_toolbar_layout = QHBoxLayout()
        main_toolbar_layout.setSpacing(12)

        # 标题
        tools_title = QLabel("📊")
        tools_title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        tools_title.setStyleSheet("color: #1C1C1E;")
        tools_title.setToolTip("聊天管理工具")

        # 状态筛选
        self.unread_btn = AnimatedButton("🔴")
        self.unread_btn.setProperty("buttonStyle", "secondary")
        self.unread_btn.setCheckable(True)
        self.unread_btn.clicked.connect(self.filter_unread_sessions)
        self.unread_btn.setToolTip("显示未读会话")
        self.unread_btn.setStyleSheet("QPushButton { font-size: 12px; padding: 4px 6px; min-height: 16px; min-width: 20px; }")

        self.read_btn = AnimatedButton("✅")
        self.read_btn.setProperty("buttonStyle", "secondary")
        self.read_btn.setCheckable(True)
        self.read_btn.clicked.connect(self.filter_read_sessions)
        self.read_btn.setToolTip("显示已读会话")
        self.read_btn.setStyleSheet("QPushButton { font-size: 12px; padding: 4px 6px; min-height: 16px; min-width: 20px; }")

        # 分隔符
        separator1 = QLabel("|")
        separator1.setStyleSheet("color: #D1D1D6; font-size: 12px;")

        # 时间筛选按钮
        self.today_btn = AnimatedButton("今天")
        self.today_btn.setProperty("buttonStyle", "secondary")
        self.today_btn.clicked.connect(lambda: self.filter_by_time("today"))
        self.today_btn.setToolTip("显示今天的会话")
        self.today_btn.setStyleSheet("QPushButton { font-size: 11px; padding: 3px 6px; min-height: 16px; }")

        self.yesterday_btn = AnimatedButton("昨天")
        self.yesterday_btn.setProperty("buttonStyle", "secondary")
        self.yesterday_btn.clicked.connect(lambda: self.filter_by_time("yesterday"))
        self.yesterday_btn.setToolTip("显示昨天的会话")
        self.yesterday_btn.setStyleSheet("QPushButton { font-size: 11px; padding: 3px 6px; min-height: 16px; }")

        self.week_btn = AnimatedButton("本周")
        self.week_btn.setProperty("buttonStyle", "secondary")
        self.week_btn.clicked.connect(lambda: self.filter_by_time("week"))
        self.week_btn.setToolTip("显示本周的会话")
        self.week_btn.setStyleSheet("QPushButton { font-size: 11px; padding: 3px 6px; min-height: 16px; }")

        self.month_btn = AnimatedButton("本月")
        self.month_btn.setProperty("buttonStyle", "secondary")
        self.month_btn.clicked.connect(lambda: self.filter_by_time("month"))
        self.month_btn.setToolTip("显示本月的会话")
        self.month_btn.setStyleSheet("QPushButton { font-size: 11px; padding: 3px 6px; min-height: 16px; }")

        # 分隔符
        separator2 = QLabel("|")
        separator2.setStyleSheet("color: #D1D1D6; font-size: 12px;")

        # 操作按钮
        self.archive_btn = AnimatedButton("📁")
        self.archive_btn.setProperty("buttonStyle", "secondary")
        self.archive_btn.clicked.connect(self.archive_selected_sessions)
        self.archive_btn.setToolTip("归档选中的会话")
        self.archive_btn.setStyleSheet("QPushButton { font-size: 12px; padding: 3px 6px; min-height: 16px; min-width: 20px; }")

        self.export_btn = AnimatedButton("📤")
        self.export_btn.setProperty("buttonStyle", "secondary")
        self.export_btn.clicked.connect(self.export_chat_data)
        self.export_btn.setToolTip("导出聊天数据")
        self.export_btn.setStyleSheet("QPushButton { font-size: 12px; padding: 3px 6px; min-height: 16px; min-width: 20px; }")

        # 添加所有组件到主布局
        main_toolbar_layout.addWidget(tools_title)
        main_toolbar_layout.addWidget(self.unread_btn)
        main_toolbar_layout.addWidget(self.read_btn)
        main_toolbar_layout.addWidget(separator1)
        main_toolbar_layout.addWidget(self.today_btn)
        main_toolbar_layout.addWidget(self.yesterday_btn)
        main_toolbar_layout.addWidget(self.week_btn)
        main_toolbar_layout.addWidget(self.month_btn)
        main_toolbar_layout.addWidget(separator2)
        main_toolbar_layout.addWidget(self.archive_btn)
        main_toolbar_layout.addWidget(self.export_btn)
        main_toolbar_layout.addStretch()

        tools_layout.addLayout(main_toolbar_layout)

        # 自定义时间范围 - 极简设计，添加到主工具栏
        separator3 = QLabel("|")
        separator3.setStyleSheet("color: #D1D1D6; font-size: 12px;")

        custom_time_label = QLabel("📅")
        custom_time_label.setStyleSheet("color: #8E8E93; font-size: 12px;")
        custom_time_label.setToolTip("自定义时间范围")

        # 起始日期
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-7))
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDisplayFormat("MM-dd")
        self.start_date_edit.setStyleSheet("""
            QDateEdit {
                border: 1px solid #D1D1D6;
                border-radius: 4px;
                padding: 2px 4px;
                font-size: 10px;
                background-color: #FFFFFF;
                color: #1C1C1E;
                font-weight: 500;
                min-width: 40px;
                max-width: 50px;
                min-height: 16px;
            }
            QDateEdit:focus {
                border-color: #007AFF;
            }
        """)

        # 分隔符
        separator_date = QLabel("~")
        separator_date.setStyleSheet("color: #8E8E93; font-size: 10px;")

        # 截止日期
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDisplayFormat("MM-dd")
        self.end_date_edit.setStyleSheet("""
            QDateEdit {
                border: 1px solid #D1D1D6;
                border-radius: 4px;
                padding: 2px 4px;
                font-size: 10px;
                background-color: #FFFFFF;
                color: #1C1C1E;
                font-weight: 500;
                min-width: 40px;
                max-width: 50px;
                min-height: 16px;
            }
            QDateEdit:focus {
                border-color: #007AFF;
            }
        """)

        # 应用按钮
        self.apply_custom_time_btn = AnimatedButton("✓")
        self.apply_custom_time_btn.setProperty("buttonStyle", "primary")
        self.apply_custom_time_btn.clicked.connect(self.apply_custom_time_filter)
        self.apply_custom_time_btn.setToolTip("应用自定义时间范围筛选")
        self.apply_custom_time_btn.setStyleSheet("""
            QPushButton {
                font-size: 10px;
                padding: 2px 4px;
                min-height: 16px;
                min-width: 18px;
                border-radius: 4px;
                font-weight: 600;
            }
        """)

        # 将自定义时间范围添加到主工具栏的末尾
        main_toolbar_layout.addWidget(separator3)
        main_toolbar_layout.addWidget(custom_time_label)
        main_toolbar_layout.addWidget(self.start_date_edit)
        main_toolbar_layout.addWidget(separator_date)
        main_toolbar_layout.addWidget(self.end_date_edit)
        main_toolbar_layout.addWidget(self.apply_custom_time_btn)

        layout.addWidget(tools_card)

        # 会话信息卡片
        info_card = ModernCard()
        info_layout = QVBoxLayout(info_card)
        info_layout.setContentsMargins(15, 15, 15, 15)

        self.session_info = QLabel("💬 选择一个会话开始对话")
        self.session_info.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Medium))
        self.session_info.setStyleSheet("color: #666666;")
        self.session_info.setAlignment(Qt.AlignmentFlag.AlignCenter)

        info_layout.addWidget(self.session_info)
        layout.addWidget(info_card)

        # 消息区域卡片
        messages_card = ModernCard()
        messages_layout = QVBoxLayout(messages_card)
        messages_layout.setContentsMargins(0, 0, 0, 0)

        self.messages_area = QTextEdit()
        self.messages_area.setReadOnly(True)
        self.messages_area.setFont(QFont("Microsoft YaHei", 12))
        self.messages_area.setStyleSheet("""
            QTextEdit {
                border: none;
                background-color: #FAFAFA;
                padding: 15px;
                line-height: 1.5;
            }
        """)

        messages_layout.addWidget(self.messages_area)
        layout.addWidget(messages_card)

        # 控制面板
        control_card = ModernCard()
        control_layout = QHBoxLayout(control_card)
        control_layout.setContentsMargins(15, 10, 15, 10)

        self.take_control_btn = AnimatedButton("🔧 接管会话")
        self.take_control_btn.setProperty("buttonStyle", "secondary")
        self.take_control_btn.clicked.connect(self.take_control)
        self.take_control_btn.setEnabled(False)
        self.take_control_btn.setStyleSheet("""
            QPushButton[buttonStyle="secondary"] {
                color: #1C1C1E !important;  /* 黑色文字，更明显 */
            }
            QPushButton[buttonStyle="secondary"]:hover {
                color: #1C1C1E !important;
            }
            QPushButton[buttonStyle="secondary"]:pressed {
                color: #1C1C1E !important;
            }
            QPushButton[buttonStyle="secondary"]:disabled {
                color: #8E8E93 !important;  /* 禁用时的灰色 */
            }
        """)

        self.manual_takeover_btn = AnimatedButton("👨‍💼 人工接管")
        self.manual_takeover_btn.setProperty("buttonStyle", "primary")
        self.manual_takeover_btn.clicked.connect(self.manual_takeover)
        self.manual_takeover_btn.setEnabled(False)
        self.manual_takeover_btn.hide()
        self.manual_takeover_btn.setStyleSheet("""
            QPushButton[buttonStyle="primary"] {
                background-color: #FF9500 !important;  /* 橙色背景 */
                color: white !important;
            }
            QPushButton[buttonStyle="primary"]:hover {
                background-color: #E6850E !important;
            }
            QPushButton[buttonStyle="primary"]:pressed {
                background-color: #CC7700 !important;
            }
            QPushButton[buttonStyle="primary"]:disabled {
                background-color: #C7C7CC !important;
                color: #8E8E93 !important;
            }
        """)

        # 取消接管按钮
        self.cancel_takeover_btn = AnimatedButton("❌ 取消接管")
        self.cancel_takeover_btn.setProperty("buttonStyle", "secondary")
        self.cancel_takeover_btn.clicked.connect(self.cancel_takeover)
        self.cancel_takeover_btn.setEnabled(False)
        self.cancel_takeover_btn.hide()
        self.cancel_takeover_btn.setStyleSheet("""
            QPushButton[buttonStyle="secondary"] {
                background-color: #FF3B30 !important;  /* 红色背景 */
                color: white !important;
                border: none !important;
            }
            QPushButton[buttonStyle="secondary"]:hover {
                background-color: #D70015 !important;
            }
            QPushButton[buttonStyle="secondary"]:pressed {
                background-color: #B8000C !important;
            }
            QPushButton[buttonStyle="secondary"]:disabled {
                background-color: #C7C7CC !important;
                color: #8E8E93 !important;
            }
        """)

        self.status_label = QLabel("状态: 未选择会话")
        self.status_label.setFont(QFont("Microsoft YaHei", 10))
        self.status_label.setStyleSheet("color: #888888;")

        control_layout.addWidget(self.take_control_btn)
        control_layout.addWidget(self.manual_takeover_btn)
        control_layout.addWidget(self.cancel_takeover_btn)
        control_layout.addStretch()
        control_layout.addWidget(self.status_label)

        layout.addWidget(control_card)

        # 消息输入区域
        input_card = ModernCard()
        input_layout = QHBoxLayout(input_card)
        input_layout.setContentsMargins(15, 15, 15, 15)
        input_layout.setSpacing(10)

        self.message_input = QLineEdit()
        self.message_input.setPlaceholderText("💭 输入回复消息...")
        self.message_input.setFont(QFont("Microsoft YaHei", 12))
        self.message_input.returnPressed.connect(self.send_message)
        self.message_input.setEnabled(False)
        self.message_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #E0E0E0;
                border-radius: 20px;
                padding: 12px 20px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #2196F3;
            }
            QLineEdit:disabled {
                background-color: #F5F5F5;
                color: #BDBDBD;
            }
        """)

        self.send_btn = AnimatedButton("发送 📤")
        self.send_btn.setProperty("buttonStyle", "primary")
        self.send_btn.clicked.connect(self.send_message)
        self.send_btn.setEnabled(False)
        self.send_btn.setStyleSheet("""
            QPushButton {
                border-radius: 20px;
                padding: 12px 24px;
                font-weight: 600;
                color: #1C1C1E !important;  /* 黑色文字，更明显 */
            }
            QPushButton:hover {
                color: #1C1C1E !important;
            }
            QPushButton:pressed {
                color: #1C1C1E !important;
            }
            QPushButton:disabled {
                color: #8E8E93 !important;  /* 禁用时的灰色 */
            }
        """)

        input_layout.addWidget(self.message_input)
        input_layout.addWidget(self.send_btn)

        layout.addWidget(input_card)
        self.setLayout(layout)

    def set_session(self, session_id, messages_data):
        """设置当前会话"""
        self.current_session_id = session_id
        self.messages = messages_data
        self.message_cache[session_id] = messages_data.copy()

        # 🔧 重置消息ID集合并重新构建（使用强化去重）
        self.message_ids.clear()
        import hashlib
        for msg in messages_data:
            message_id = msg.get('id')
            content = msg.get('content', '')
            sender = msg.get('sender', '')

            # 主要标识符：数据库ID
            if message_id:
                self.message_ids.add(message_id)

            # 备用标识符：内容+发送者的哈希
            content_hash = hashlib.md5(f"{sender}_{content}".encode()).hexdigest()[:12]
            secondary_id = f"{sender}_{content_hash}"
            self.message_ids.add(secondary_id)

        # 更新会话信息
        self.session_info.setText(f"💬 会话 {session_id[:12]}...")
        self.status_label.setText("状态: 已连接")

        # 更新消息显示
        self.update_messages_display()

        # 启用控件
        self.take_control_btn.setEnabled(True)
        self.message_input.setEnabled(False)  # 需要先接管才能发送消息
        self.send_btn.setEnabled(False)

    def update_messages_display(self):
        """高效更新消息显示"""
        if not self.current_session_id:
            return

        self.messages_area.clear()

        # 构建HTML格式的消息
        html_content = "<div style='font-family: Microsoft YaHei; line-height: 1.6;'>"

        for msg in self.messages:
            timestamp = parse_iso_time(msg['created_at'])
            if not timestamp:
                timestamp = datetime.fromisoformat(msg['created_at'].replace('Z', '+00:00'))
            time_str = format_time_for_chat(timestamp)

            sender = msg['sender']
            content = msg['content'].replace('\n', '<br>')

            # 根据发送者设置不同样式，确保格式一致
            if sender == 'user':
                html_content += f"""
                <div style='margin: 10px 0; padding: 12px; background-color: #E3F2FD;
                           border-radius: 12px; border-left: 4px solid #2196F3; display: block; clear: both;'>
                    <div style='font-size: 11px; color: #666; margin-bottom: 5px; display: block;'>
                        👤 用户 · {time_str}
                    </div>
                    <div style='color: #333; display: block; word-wrap: break-word; white-space: pre-wrap;'>{content}</div>
                </div>
                """
            elif sender == 'ai':
                html_content += f"""
                <div style='margin: 10px 0; padding: 12px; background-color: #F3E5F5;
                           border-radius: 12px; border-left: 4px solid #9C27B0; display: block; clear: both;'>
                    <div style='font-size: 11px; color: #666; margin-bottom: 5px; display: block;'>
                        🤖 AI助手 · {time_str}
                    </div>
                    <div style='color: #333; display: block; word-wrap: break-word; white-space: pre-wrap;'>{content}</div>
                </div>
                """
            elif sender == 'admin':
                html_content += f"""
                <div style='margin: 10px 0; padding: 12px; background-color: #FFF3E0;
                           border-radius: 12px; border-left: 4px solid #FF9800; display: block; clear: both;'>
                    <div style='font-size: 11px; color: #666; margin-bottom: 5px; display: block; word-wrap: break-word; white-space: normal;'>
                        👨‍💼 人工客服 · {time_str}
                    </div>
                    <div style='color: #333; display: block; word-wrap: break-word; white-space: pre-wrap;'>{content}</div>
                </div>
                """

        html_content += "</div>"
        self.messages_area.setHtml(html_content)

        # 滚动到底部
        scrollbar = self.messages_area.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def add_message(self, message_data):
        """添加新消息（增量更新）- 强化去重机制"""
        if message_data.get('session_id') == self.current_session_id:
            # 🔧 强化去重：使用多重标识符
            message_id = message_data.get('id')
            content = message_data.get('content', '')
            created_at = message_data.get('created_at', '')
            sender = message_data.get('sender', '')

            # 主要标识符：数据库ID
            primary_id = message_id if message_id else None

            # 备用标识符：内容+发送者的哈希（忽略时间戳差异）
            import hashlib
            content_hash = hashlib.md5(f"{sender}_{content}".encode()).hexdigest()[:12]
            secondary_id = f"{sender}_{content_hash}"

            # 检查是否已经添加过这条消息（使用任一标识符）
            if primary_id and primary_id in self.message_ids:
                logger.debug(f"消息已存在（主ID）- ID: {primary_id}")
                print(f"⚠️ 消息已存在（主ID），跳过重复添加: {primary_id}")
                return

            if secondary_id in self.message_ids:
                logger.debug(f"消息已存在（备用ID）- ID: {secondary_id}")
                print(f"⚠️ 消息已存在（备用ID），跳过重复添加: {secondary_id}")
                return

            # 🔧 额外检查：遍历现有消息，检查内容重复
            for existing_msg in self.messages:
                if (existing_msg.get('sender') == sender and
                    existing_msg.get('content') == content):
                    logger.debug(f"发现内容重复的消息，跳过添加")
                    print(f"⚠️ 发现内容重复的消息，跳过添加: {content[:30]}...")
                    return

            # 添加到去重集合（同时添加主ID和备用ID）
            if primary_id:
                self.message_ids.add(primary_id)
            self.message_ids.add(secondary_id)

            logger.debug(f"添加新消息 - 主ID: {primary_id}, 备用ID: {secondary_id}, 发送者: {sender}")

            self.messages.append(message_data)

            # 更新缓存
            if self.current_session_id in self.message_cache:
                self.message_cache[self.current_session_id].append(message_data)

            # 只添加新消息而不是重绘整个区域
            self.append_single_message(message_data)

    def append_single_message(self, message_data):
        """追加单条消息 - 使用重新构建整个消息区域的方法确保格式一致"""
        # 注意：消息已经在add_message方法中添加到列表和缓存了，这里只需要更新显示
        # 重新构建整个消息显示区域，确保格式一致
        self.update_messages_display()

    def take_control(self):
        """接管会话控制"""
        if self.current_session_id:
            self.is_controlled = True
            self.take_control_btn.hide()
            self.manual_takeover_btn.show()
            self.manual_takeover_btn.setEnabled(True)
            self.message_input.setEnabled(True)
            self.send_btn.setEnabled(True)
            self.status_label.setText("状态: 已接管控制")
            self.control_requested.emit(self.current_session_id, True)

    def manual_takeover(self):
        """人工接管会话"""
        if self.current_session_id:
            self.is_controlled = True  # 人工接管状态仍然是控制状态
            self.take_control_btn.hide()
            self.manual_takeover_btn.hide()
            self.cancel_takeover_btn.show()
            self.cancel_takeover_btn.setEnabled(True)
            # 人工接管后仍然允许发送消息
            self.message_input.setEnabled(True)
            self.send_btn.setEnabled(True)
            self.status_label.setText("状态: 人工接管中 (可发送消息)")
            # 修复：人工接管也需要调用后端API设置数据库状态
            self.control_requested.emit(self.current_session_id, True)

    def cancel_takeover(self):
        """取消人工接管"""
        if self.current_session_id:
            self.is_controlled = False  # 取消接管后回到未控制状态
            self.take_control_btn.show()
            self.take_control_btn.setEnabled(True)
            self.manual_takeover_btn.hide()
            self.cancel_takeover_btn.hide()
            # 取消接管后禁用输入框和发送按钮
            self.message_input.setEnabled(False)
            self.send_btn.setEnabled(False)
            self.status_label.setText("状态: 已取消人工接管 (需重新接管)")
            # 发送取消接管信号到后端
            self.control_requested.emit(self.current_session_id, False)

    def send_message(self):
        """发送消息"""
        message = self.message_input.text().strip()
        # 修改条件：只要有会话ID且输入框启用就可以发送消息
        if message and self.current_session_id and self.message_input.isEnabled():
            self.message_sent.emit(self.current_session_id, message)
            self.message_input.clear()

            # 添加发送动画效果
            self.send_btn.setText("发送中...")
            self.send_btn.setEnabled(False)
            QTimer.singleShot(1000, self.reset_send_button)

    def reset_send_button(self):
        """重置发送按钮状态"""
        self.send_btn.setText("发送 📤")
        self.send_btn.setEnabled(True)

    def clear_session(self):
        """清空当前会话"""
        self.current_session_id = None
        self.messages = []
        self.message_ids.clear()  # 清空消息ID集合
        self.is_controlled = False

        self.session_info.setText("💬 选择一个会话开始对话")
        self.status_label.setText("状态: 未选择会话")
        self.messages_area.clear()

        # 重置控件状态
        self.take_control_btn.setEnabled(False)
        self.take_control_btn.show()
        self.manual_takeover_btn.hide()
        self.cancel_takeover_btn.hide()
        self.message_input.setEnabled(False)
        self.send_btn.setEnabled(False)

    def filter_unread_sessions(self):
        """筛选未读会话"""
        # 获取主窗口引用
        main_window = self.window()
        if hasattr(main_window, 'filter_unread_sessions'):
            main_window.filter_unread_sessions()

    def filter_read_sessions(self):
        """筛选已读会话"""
        # 获取主窗口引用
        main_window = self.window()
        if hasattr(main_window, 'filter_read_sessions'):
            main_window.filter_read_sessions()

    def filter_by_time(self, period):
        """按时间筛选会话"""
        # 获取主窗口引用
        main_window = self.window()
        if hasattr(main_window, 'filter_by_time'):
            main_window.filter_by_time(period)

    def apply_custom_time_filter(self):
        """应用自定义时间范围筛选"""
        start_date = self.start_date_edit.date().toPython() if hasattr(self.start_date_edit.date(), 'toPython') else self.start_date_edit.date().toPyDate()
        end_date = self.end_date_edit.date().toPython() if hasattr(self.end_date_edit.date(), 'toPython') else self.end_date_edit.date().toPyDate()

        if start_date > end_date:
            QMessageBox.warning(self, "日期错误", "起始日期不能晚于截止日期")
            return

        # 获取主窗口引用
        main_window = self.window()
        if hasattr(main_window, 'apply_custom_time_filter'):
            main_window.apply_custom_time_filter(start_date, end_date)

    def archive_selected_sessions(self):
        """归档选中的会话"""
        # 获取主窗口引用
        main_window = self.window()
        if hasattr(main_window, 'archive_selected_sessions'):
            main_window.archive_selected_sessions()

    def export_chat_data(self):
        """导出聊天数据"""
        # 获取主窗口引用
        main_window = self.window()
        if hasattr(main_window, 'export_chat_data'):
            main_window.export_chat_data()

    def update_control_status(self, is_controlled: bool):
        """更新控制状态（用于实时同步）"""
        self.is_controlled = is_controlled

        if is_controlled:
            # 会话被接管
            self.take_control_btn.hide()
            self.manual_takeover_btn.hide()
            self.cancel_takeover_btn.show()
            self.cancel_takeover_btn.setEnabled(True)
            self.message_input.setEnabled(True)
            self.send_btn.setEnabled(True)
            self.status_label.setText("状态: 会话已被接管")
        else:
            # 会话未被接管
            self.take_control_btn.show()
            self.take_control_btn.setEnabled(True)
            self.manual_takeover_btn.hide()
            self.cancel_takeover_btn.hide()
            self.message_input.setEnabled(False)
            self.send_btn.setEnabled(False)
            self.status_label.setText("状态: 会话未接管")

    def sync_session_status(self, session_id: str):
        """同步会话状态（从服务器获取最新状态）"""
        if session_id != self.current_session_id:
            return

        # 获取主窗口引用来调用API
        main_window = self.window()
        if hasattr(main_window, 'sync_session_status'):
            main_window.sync_session_status(session_id)

    def clear_session(self):
        """清空当前会话"""
        self.current_session_id = None
        self.messages = []
        self.message_cache = {}
        self.message_ids = set()
        self.is_controlled = False

        # 清空UI显示
        self.messages_area.clear()
        self.session_info.setText("💬 请选择会话")
        self.status_label.setText("状态: 未选择")

        # 禁用控件
        self.take_control_btn.setEnabled(False)
        self.message_input.setEnabled(False)
        self.send_btn.setEnabled(False)
        self.message_input.clear()

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        logger.info("初始化主窗口")
        self.settings = QSettings("YFCompany", "YFAIChat")
        self.websocket_thread = None
        self.admin_id = None
        self.api_client = None
        self.user_manager = UserManager()
        self.archive_manager = ArchiveManager()  # 添加归档管理器
        self.loading_widget = None
        self.api_workers = []  # 管理API工作线程

        # 系统事件监听器
        self.system_monitor = SystemEventMonitor(self)
        self.system_monitor.screen_unlocked.connect(self.on_screen_unlocked)
        self.system_monitor.screen_locked.connect(self.on_screen_locked)
        logger.debug("开始设置UI界面")
        self.setup_ui()
        logger.debug("开始设置API客户端")
        self.setup_api_client()
        logger.debug("应用Apple样式")
        self.apply_apple_style()
        logger.info("主窗口初始化完成")

    def setup_ui(self):
        # 从设置中读取应用标题
        app_title = self.settings.value("app_title", "YF AI Chat - 智能客服管理系统")
        self.setWindowTitle(app_title)

        # 获取窗口尺寸
        window_width = int(os.getenv("WINDOW_WIDTH", 1400))
        window_height = int(os.getenv("WINDOW_HEIGHT", 900))

        # 使用DPI适配的窗口尺寸
        width, height = DPIHelper.get_optimal_window_size(window_width, window_height)
        self.resize(width, height)

        # 居中显示主窗口
        self.center_main_window()

        # 设置窗口图标和属性
        self.setMinimumSize(1000, 700)

        # 设置窗口图标
        try:
            # 优先尝试从打包资源中加载图标
            if getattr(sys, 'frozen', False):
                # 打包环境：尝试从临时目录加载
                import tempfile
                from pathlib import Path
                temp_dir = Path(sys._MEIPASS) if hasattr(sys, '_MEIPASS') else Path(tempfile.gettempdir())
                icon_paths = [
                    temp_dir / 'resources' / 'text_icon.ico',
                    temp_dir / 'resources' / 'text_icon.png'
                ]
            else:
                # 开发环境：从本地文件加载
                icon_paths = [
                    get_config_path('resources/text_icon.ico'),
                    get_config_path('resources/text_icon.png')
                ]

            icon_loaded = False
            for icon_path in icon_paths:
                if icon_path.exists():
                    self.setWindowIcon(QIcon(str(icon_path)))
                    logger.debug(f"窗口图标设置成功: {icon_path}")
                    icon_loaded = True
                    break

            if not icon_loaded:
                logger.warning("未找到窗口图标文件")
        except Exception as e:
            logger.warning(f"设置窗口图标失败: {e}")

        # 主容器
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        central_widget.setStyleSheet("background-color: #F5F5F5;")

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 左侧面板 - 会话列表
        left_panel_card = ModernCard()
        left_panel_layout = QVBoxLayout(left_panel_card)
        left_panel_layout.setContentsMargins(15, 15, 15, 15)
        left_panel_layout.setSpacing(15)

        # 标题区域
        title_layout = QHBoxLayout()
        sessions_label = QLabel("📋 活跃会话")
        sessions_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        sessions_label.setStyleSheet("color: #1C1C1E;")

        # 会话计数标签
        self.session_count_label = QLabel("(0)")
        self.session_count_label.setFont(QFont("Microsoft YaHei", 12))
        self.session_count_label.setStyleSheet("color: #8E8E93;")

        title_layout.addWidget(sessions_label)
        title_layout.addWidget(self.session_count_label)
        title_layout.addStretch()
        left_panel_layout.addLayout(title_layout)





        # 会话列表
        self.sessions_list = SessionListWidget()
        self.sessions_list.session_selected.connect(self.load_session)
        left_panel_layout.addWidget(self.sessions_list)

        # 控制按钮区域
        button_layout = QHBoxLayout()

        self.refresh_btn = AnimatedButton("🔄 刷新")
        self.refresh_btn.setProperty("buttonStyle", "secondary")
        self.refresh_btn.clicked.connect(self.manual_refresh_sessions)
        self.refresh_btn.setToolTip("手动刷新会话列表")

        self.auto_refresh_btn = AnimatedButton("⏸️ 暂停自动刷新")
        self.auto_refresh_btn.setProperty("buttonStyle", "secondary")
        self.auto_refresh_btn.clicked.connect(self.toggle_auto_refresh)
        self.auto_refresh_btn.setToolTip("切换自动刷新状态")

        # 归档按钮
        self.archive_view_btn = AnimatedButton("📁 查看归档")
        self.archive_view_btn.setProperty("buttonStyle", "secondary")
        self.archive_view_btn.clicked.connect(self.show_archive_dialog)
        self.archive_view_btn.setToolTip("查看归档的会话")

        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.auto_refresh_btn)
        left_panel_layout.addLayout(button_layout)

        # 归档按钮单独一行
        archive_layout = QHBoxLayout()
        archive_layout.addWidget(self.archive_view_btn)
        archive_layout.addStretch()
        left_panel_layout.addLayout(archive_layout)

        # 设置按钮
        settings_layout = QHBoxLayout()
        self.settings_btn = AnimatedButton("设置")
        self.settings_btn.setProperty("buttonStyle", "secondary")
        self.settings_btn.clicked.connect(self.show_settings)
        self.settings_btn.setToolTip("应用设置")

        settings_layout.addWidget(self.settings_btn)
        settings_layout.addStretch()
        left_panel_layout.addLayout(settings_layout)

        # 设置左侧面板宽度
        left_panel_card.setFixedWidth(350)

        # 右侧面板 - 标签页界面
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #D1D1D6;
                border-radius: 12px;
                background-color: #FFFFFF;
            }
            QTabBar::tab {
                background-color: #F2F2F7;
                color: #1C1C1E;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Arial, sans-serif;
                font-weight: 500;
            }
            QTabBar::tab:selected {
                background-color: #007AFF;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #E3F2FD;
            }
        """)

        # 聊天标签页
        self.chat_widget = ChatWidget()
        self.chat_widget.message_sent.connect(self.send_admin_message)
        self.chat_widget.control_requested.connect(self.handle_control_request)
        self.tab_widget.addTab(self.chat_widget, "💬 聊天管理")

        # 邮件标签页将在main_tab_widget中创建，这里不需要重复创建

        # 分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(left_panel_card)
        splitter.addWidget(self.tab_widget)
        splitter.setSizes([350, 1050])
        splitter.setHandleWidth(2)

        # 顶级标签页界面
        self.main_tab_widget = QTabWidget()
        self.main_tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #D1D1D6;
                border-radius: 12px;
                background-color: #FFFFFF;
            }
            QTabBar::tab {
                background-color: #F2F2F7;
                color: #1C1C1E;
                padding: 15px 30px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Arial, sans-serif;
                font-weight: 600;
                font-size: 14px;
            }
            QTabBar::tab:selected {
                background-color: #007AFF;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #E3F2FD;
            }
        """)

        # 聊天管理标签页
        self.chat_tab = self.create_chat_tab()
        self.main_tab_widget.addTab(self.chat_tab, "💬 聊天管理")

        # 邮件管理标签页
        self.email_tab = self.create_email_tab()
        self.main_tab_widget.addTab(self.email_tab, "📧 邮件管理")

        # 售后工单标签页
        self.support_ticket_tab = self.create_support_ticket_tab()
        self.main_tab_widget.addTab(self.support_ticket_tab, "🎫 售后工单")

        # 连接选项卡切换事件
        self.main_tab_widget.currentChanged.connect(self.on_main_tab_changed)

        main_layout.addWidget(self.main_tab_widget)

        # 状态栏
        self.setup_status_bar()

        # 自动刷新定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.auto_refresh_sessions)
        self.auto_refresh_enabled = True
        self.refresh_timer.start(int(os.getenv("REFRESH_INTERVAL", 10000)))  # 10秒

        # 状态同步定时器
        self.status_sync_timer = QTimer()
        self.status_sync_timer.timeout.connect(self.sync_current_session_status)
        self.status_sync_timer.start(60000)  # 60秒同步一次当前会话状态

        # 邮件自动刷新定时器
        self.email_refresh_timer = QTimer()
        self.email_refresh_timer.timeout.connect(self.auto_refresh_emails)
        self.email_auto_refresh_enabled = True
        # 从环境变量读取邮件刷新间隔，支持秒和毫秒两种单位
        email_refresh_seconds = int(os.getenv("EMAIL_REFRESH_INTERVAL_SECONDS", 0))
        if email_refresh_seconds > 0:
            # 如果设置了秒为单位的参数，转换为毫秒
            email_refresh_interval = email_refresh_seconds * 1000
        else:
            # 否则使用毫秒为单位的参数，默认5分钟(300秒)
            email_refresh_interval = int(os.getenv("EMAIL_REFRESH_INTERVAL", 300000))

        print(f"📧 邮件自动刷新间隔: {email_refresh_interval}ms ({email_refresh_interval/1000}秒)")
        self.email_refresh_timer.start(email_refresh_interval)

        # WebSocket健康检查定时器（在主线程中管理）
        self.websocket_health_timer = QTimer()
        self.websocket_health_timer.timeout.connect(self.check_websocket_health)
        self.websocket_health_timer.setInterval(30000)  # 30秒检查一次

        # 启动系统状态监听
        self.system_monitor.start_monitoring()

    def setup_status_bar(self):
        """设置现代化状态栏"""
        status_bar = self.statusBar()
        status_bar.setStyleSheet("""
            QStatusBar {
                background-color: white;
                border-top: 1px solid #E0E0E0;
                color: #666666;
                font-size: 12px;
                padding: 5px;
            }
        """)

        # 连接状态
        self.connection_status = QLabel("🔴 未连接")
        self.connection_status.setStyleSheet("color: #F44336; font-weight: 500;")

        # 会话统计
        self.session_stats = QLabel("会话: 0 | 活跃: 0")
        self.session_stats.setStyleSheet("color: #666666;")

        # 最后更新时间
        self.last_update = QLabel("最后更新: 从未")
        self.last_update.setStyleSheet("color: #999999;")

        status_bar.addWidget(self.connection_status)
        status_bar.addPermanentWidget(self.session_stats)
        status_bar.addPermanentWidget(self.last_update)

    def apply_apple_style(self):
        """应用Apple风格样式"""
        style = AppleStyleSheet.get_main_style()
        style += AppleStyleSheet.get_card_style()
        style += AppleStyleSheet.get_primary_button_style()
        style += AppleStyleSheet.get_secondary_button_style()
        self.setStyleSheet(style)

    def center_main_window(self):
        """将主窗口居中显示在桌面"""
        screen_geometry = DPIHelper.get_screen_geometry()
        if screen_geometry:
            # 计算居中位置
            x = screen_geometry.x() + (screen_geometry.width() - self.width()) // 2
            y = screen_geometry.y() + (screen_geometry.height() - self.height()) // 2
            self.move(x, y)

    def setup_api_client(self):
        """设置API客户端配置"""
        server_host = os.getenv("SERVER_HOST", "chat.22668.xyz")
        server_port = os.getenv("SERVER_PORT", "8000")
        use_https = os.getenv("USE_HTTPS", "true").lower() == "true"

        protocol = "https" if use_https else "http"
        self.api_base_url = f"{protocol}://{server_host}:{server_port}"
        self.api_token = os.getenv("ADMIN_TOKEN", "")

        self.api_headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }

        logger.info(f"API客户端配置完成 - 服务器: {self.api_base_url}")
        logger.debug(f"使用HTTPS: {use_https}, Token长度: {len(self.api_token) if self.api_token else 0}")

    def toggle_auto_refresh(self):
        """切换自动刷新状态"""
        self.auto_refresh_enabled = not self.auto_refresh_enabled

        if self.auto_refresh_enabled:
            self.refresh_timer.start()
            self.auto_refresh_btn.setText("⏸️ 暂停自动刷新")
            self.auto_refresh_btn.setToolTip("点击暂停自动刷新")
        else:
            self.refresh_timer.stop()
            self.auto_refresh_btn.setText("▶️ 开启自动刷新")
            self.auto_refresh_btn.setToolTip("点击开启自动刷新")

    def manual_refresh_sessions(self):
        """手动刷新会话"""
        self.refresh_btn.setText("🔄 刷新中...")
        self.refresh_btn.setEnabled(False)
        self.refresh_sessions()

        # 1秒后恢复按钮状态
        QTimer.singleShot(1000, self.reset_refresh_button)

    def reset_refresh_button(self):
        """重置刷新按钮状态"""
        self.refresh_btn.setText("🔄 刷新")
        self.refresh_btn.setEnabled(True)

    def auto_refresh_sessions(self):
        """自动刷新会话（后台执行）"""
        if self.auto_refresh_enabled:
            self.refresh_sessions(show_loading=False)

    def auto_refresh_emails(self):
        """自动刷新邮件（后台执行）"""
        if self.email_auto_refresh_enabled and hasattr(self, 'email_widget'):
            try:
                # 只有当邮件标签页是当前活动标签时才刷新
                if (hasattr(self, 'main_tab_widget') and
                    self.main_tab_widget.currentWidget() == self.email_widget):

                    # 刷新所有邮箱的文件夹数量
                    for mailbox_name in self.email_widget.mailboxes.keys():
                        self.email_widget.load_folder_counts_async(mailbox_name)

                    # 如果当前正在查看某个文件夹，刷新邮件列表
                    if (hasattr(self.email_widget, 'current_mailbox') and
                        hasattr(self.email_widget, 'current_folder') and
                        self.email_widget.current_mailbox and
                        self.email_widget.current_folder):

                        print(f"🔄 自动刷新邮件: {self.email_widget.current_mailbox}/{self.email_widget.current_folder}")
                        self.email_widget.load_folder_emails(
                            self.email_widget.current_mailbox,
                            self.email_widget.current_folder
                        )

            except Exception as e:
                print(f"自动刷新邮件失败: {e}")

    def toggle_email_auto_refresh(self):
        """切换邮件自动刷新状态"""
        self.email_auto_refresh_enabled = not self.email_auto_refresh_enabled

        if self.email_auto_refresh_enabled:
            self.email_refresh_timer.start()
            print("✅ 邮件自动刷新已开启")
        else:
            self.email_refresh_timer.stop()
            print("⏸️ 邮件自动刷新已暂停")

    def send_admin_message(self, session_id, message):
        """发送管理员消息"""
        logger.info(f"发送管理员消息 - 会话: {session_id[:12]}..., 内容: {message[:50]}...")

        def send_message():
            try:
                response = requests.post(
                    f"{self.api_base_url}/api/admin/session/{session_id}/message",
                    headers=self.api_headers,
                    json={"message": message, "admin_id": self.admin_id},
                    timeout=10
                )
                logger.debug(f"消息发送响应状态: {response.status_code}")
                return response.status_code == 200
            except Exception as e:
                logger.error(f"发送管理员消息失败: {e}")
                print(f"Error sending admin message: {e}")
                return False

        # 异步发送消息
        worker = AsyncWorker(send_message)
        worker.finished.connect(lambda success: self.handle_message_sent(success, session_id, message))
        worker.error.connect(lambda error: logger.error(f"消息发送工作线程错误: {error}"))
        worker.start()
        self.api_workers.append(worker)

    def handle_message_sent(self, success, session_id, message):
        """处理消息发送结果"""
        if success:
            # 消息发送成功，等待WebSocket推送消息进行显示
            # 这样确保使用服务器返回的完整消息数据（包含ID和时间戳）
            logger.info(f"管理员消息发送成功 - 会话: {session_id[:12]}..., 内容: {message[:50]}...")
            print(f"✅ 管理员消息发送成功: {message[:50]}...")

            # 可选：设置一个超时检查，如果5秒内没有收到WebSocket消息，则显示警告
            QTimer.singleShot(5000, lambda: self.check_message_delivery(session_id, message))
        else:
            logger.error(f"管理员消息发送失败 - 会话: {session_id[:12]}..., 内容: {message[:50]}...")
            QMessageBox.warning(self, "发送失败", "消息发送失败，请检查网络连接")

    def check_message_delivery(self, session_id, message):
        """检查消息是否已通过WebSocket送达"""
        # 这是一个可选的检查机制，确保消息最终显示
        # 在正常情况下，WebSocket应该会推送消息，这个检查不会触发
        if session_id == self.chat_widget.current_session_id:
            # 检查最近的消息中是否包含我们发送的内容
            recent_messages = self.chat_widget.messages[-3:] if self.chat_widget.messages else []
            message_found = any(
                msg.get('content') == message and msg.get('sender') == 'admin'
                for msg in recent_messages
            )

            if not message_found:
                logger.warning(f"消息可能未通过WebSocket送达，会话: {session_id[:12]}...")
                # 可以选择在这里手动添加消息或显示提示

    def handle_control_request(self, session_id, take_control):
        """处理会话控制请求"""
        def control_session():
            try:
                endpoint = "take" if take_control else "release"
                response = requests.post(
                    f"{self.api_base_url}/api/admin/session/{session_id}/{endpoint}",
                    headers=self.api_headers,
                    json={"admin_id": self.admin_id},
                    timeout=10
                )
                return response.status_code == 200
            except Exception as e:
                print(f"Error controlling session: {e}")
                return False

        # 异步执行控制操作
        worker = AsyncWorker(control_session)
        worker.finished.connect(lambda success: self.handle_control_result(success, take_control))
        worker.error.connect(lambda error: print(f"Control error: {error}"))
        worker.start()
        self.api_workers.append(worker)

    def handle_control_result(self, success, take_control):
        """处理控制操作结果"""
        if success:
            # 更新会话列表中的控制状态
            current_session_id = self.chat_widget.current_session_id
            if current_session_id:
                self.sessions_list.update_session_control_status(current_session_id, take_control)
        else:
            action = "接管" if take_control else "释放"
            QMessageBox.warning(self, "操作失败", f"会话{action}失败，请重试")

    def show_login(self):
        """显示登录对话框"""
        dialog = LoginDialog(None)  # 不设置父窗口，让它独立显示

        # 在桌面中央显示登录窗口
        screen_geometry = DPIHelper.get_screen_geometry()
        if screen_geometry:
            x = screen_geometry.x() + (screen_geometry.width() - dialog.width()) // 2
            y = screen_geometry.y() + (screen_geometry.height() - dialog.height()) // 2
            dialog.move(x, y)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            username, password = dialog.get_credentials()

            # 显示登录加载状态
            dialog.show_loading()

            # 异步认证
            def authenticate_async():
                return self.authenticate(username, password)

            worker = AsyncWorker(authenticate_async)
            worker.finished.connect(lambda success: self.handle_login_result(success, username, dialog))
            worker.error.connect(lambda error: self.handle_login_error(error, dialog))
            worker.start()
            self.api_workers.append(worker)

            # 等待认证结果
            return dialog.exec() == QDialog.DialogCode.Accepted
        return False

    def handle_login_result(self, success, username, dialog):
        """处理登录结果"""
        dialog.hide_loading()

        if success:
            self.admin_id = username
            dialog.accept()
            self.connect_websocket()
            self.refresh_sessions()
        else:
            dialog.reject()

    def on_screen_locked(self):
        """处理屏幕锁定事件"""
        logger.info("系统锁屏，暂停部分功能")
        # 可以选择暂停一些定时器或降低刷新频率

    def on_screen_unlocked(self):
        """处理屏幕解锁事件"""
        logger.info("系统解锁，恢复功能并同步数据")

        # 强制重连WebSocket
        if self.websocket_thread:
            logger.info("解锁后强制重连WebSocket")
            self.websocket_thread.force_reconnect()

        # 刷新会话列表
        logger.info("解锁后刷新会话列表")
        self.refresh_sessions(show_loading=False)

        # 如果当前有选中的会话，重新加载消息
        if hasattr(self, 'chat_widget') and self.chat_widget.current_session_id:
            logger.info(f"解锁后重新加载当前会话: {self.chat_widget.current_session_id[:12]}...")
            self.load_session(self.chat_widget.current_session_id)

        # 更新状态栏
        self.last_update.setText(f"最后更新: {datetime.now().strftime('%H:%M:%S')} (解锁后同步)")

    def handle_login_error(self, error, dialog):
        """处理登录错误"""
        dialog.hide_loading()
        QMessageBox.critical(self, "登录错误", f"登录过程中发生错误: {error}")
        dialog.reject()

    def authenticate(self, username, password):
        """认证用户"""
        try:
            logger.info(f"尝试认证用户: {username}")
            # 使用用户管理系统进行认证
            if self.user_manager.authenticate(username, password):
                logger.info(f"用户管理系统认证成功: {username}")
                return True

            # 如果用户管理系统认证失败，回退到环境变量认证（向后兼容）
            default_username = os.getenv("DEFAULT_ADMIN_USERNAME", "admin")
            default_password = os.getenv("DEFAULT_ADMIN_PASSWORD", "your_new_password_here")

            if username == default_username and password == default_password:
                logger.info(f"环境变量认证成功: {username}")
                return True

            logger.warning(f"认证失败: {username}")
            return False
        except Exception as e:
            logger.error(f"认证过程中发生错误: {e}", exc_info=True)
            print(f"Authentication error: {e}")
            return False

    def connect_websocket(self):
        """连接WebSocket"""
        logger.info("开始连接WebSocket")
        if self.websocket_thread:
            logger.debug("停止现有WebSocket连接")
            self.websocket_thread.stop()

        ws_protocol = "wss" if os.getenv("USE_HTTPS", "true").lower() == "true" else "ws"
        server_host = os.getenv("SERVER_HOST", "chat.22668.xyz")
        server_port = os.getenv("SERVER_PORT", "8000")
        ws_url = f"{ws_protocol}://{server_host}:{server_port}"

        logger.info(f"WebSocket连接地址: {ws_url}")
        logger.debug(f"管理员ID: {self.admin_id}")

        self.websocket_thread = WebSocketThread(ws_url, self.admin_id, self.api_token)
        self.websocket_thread.message_received.connect(self.handle_websocket_message)
        self.websocket_thread.connection_status.connect(self.handle_connection_status)
        self.websocket_thread.connection_error.connect(self.handle_connection_error)
        self.websocket_thread.health_check_needed.connect(self.check_websocket_health)
        self.websocket_thread.start()

        # 启动WebSocket健康检查定时器
        self.websocket_health_timer.start()
        logger.debug("WebSocket线程已启动，健康检查定时器已启动")

    def check_websocket_health(self):
        """检查WebSocket连接健康状态（在主线程中执行）"""
        if self.websocket_thread and self.websocket_thread.running:
            # 检查连接是否活跃
            now = datetime.now()
            time_since_activity = (now - self.websocket_thread.last_activity).total_seconds()

            # 如果超过60秒没有活动，认为连接可能有问题
            if time_since_activity > 60:
                logger.warning(f"WebSocket连接可能不活跃，上次活动: {time_since_activity:.1f}秒前")
                if self.websocket_thread.websocket:
                    # 尝试重新连接
                    self.websocket_thread.force_reconnect()

    def handle_websocket_message(self, data):
        """处理WebSocket消息"""
        message_type = data.get('type')
        logger.debug(f"收到WebSocket消息 - 类型: {message_type}")

        if message_type == 'new_message':
            session_id = data['message'].get('session_id', 'unknown')
            logger.info(f"收到新用户消息 - 会话: {session_id[:12]}...")
            self.chat_widget.add_message(data['message'])
            # 如果是当前会话的新消息，可能需要刷新会话列表
            if data['message'].get('session_id') == self.chat_widget.current_session_id:
                QTimer.singleShot(1000, lambda: self.refresh_sessions(show_loading=False))
        elif message_type == 'ai_response':
            session_id = data['message'].get('session_id', 'unknown')
            logger.info(f"收到AI回复消息 - 会话: {session_id[:12]}...")
            self.chat_widget.add_message(data['message'])
        elif message_type == 'admin_message':
            # 处理管理员消息
            session_id = data['message'].get('session_id', 'unknown')
            logger.info(f"收到管理员消息 - 会话: {session_id[:12]}...")
            self.chat_widget.add_message(data['message'])
            # 刷新会话列表以更新状态
            if data['message'].get('session_id') == self.chat_widget.current_session_id:
                QTimer.singleShot(1000, lambda: self.refresh_sessions(show_loading=False))
        elif message_type == 'session_update':
            # 会话状态更新
            logger.debug("收到会话状态更新消息")
            self.refresh_sessions(show_loading=False)
        elif message_type == 'admin_timeout':
            # 管理员控制超时
            session_id = data.get('session_id')
            admin_id = data.get('admin_id')
            logger.warning(f"管理员控制超时 - 会话: {session_id[:12] if session_id else 'unknown'}..., 管理员: {admin_id}")
            if session_id == self.chat_widget.current_session_id:
                # 当前会话的管理员控制超时，更新UI状态
                self.chat_widget.update_control_status(False)
                self.show_status_message(f"会话 {session_id[:12]}... 管理员控制已超时自动释放")
            # 刷新会话列表
            self.refresh_sessions(show_loading=False)
        elif message_type == 'order_inquiry':
            # 处理订单咨询消息
            logger.info(f"收到订单咨询通知 - 会话: {data.get('session_id', 'unknown')[:12]}...")
            self.handle_order_inquiry(data)
        elif message_type == 'customer_service_request':
            # 处理客服请求（包括表单提交）
            session_id = data.get('session_id', 'unknown')
            status = data.get('status', 'unknown')
            logger.info(f"收到客服请求通知 - 会话: {session_id[:12]}..., 状态: {status}")
            self.handle_customer_service_request(data)
        elif message_type == 'form_submission':
            # 处理表单提交通知
            session_id = data.get('session_id', 'unknown')
            category = data.get('category', 'unknown')
            logger.info(f"收到表单提交通知 - 会话: {session_id[:12]}..., 类别: {category}")
            self.handle_form_submission(data)
        else:
            logger.debug(f"收到未知类型的WebSocket消息: {message_type}")

    def handle_customer_service_request(self, data):
        """处理客服请求通知"""
        session_id = data.get('session_id')
        status = data.get('status', '')

        # 显示通知
        if status in ['form_collection', 'form_initiated']:
            self.show_status_message(f"会话 {session_id[:12]}... 开始收集表单信息")
        elif status == 'form_completed':
            self.show_status_message(f"会话 {session_id[:12]}... 表单收集完成，需要客服处理")
            # 刷新会话列表以显示新的表单提交
            self.refresh_sessions(show_loading=False)
        elif status in ['handover', 'handover_fallback']:
            self.show_status_message(f"会话 {session_id[:12]}... 需要人工接管")
            self.refresh_sessions(show_loading=False)

    def handle_form_submission(self, data):
        """处理表单提交通知"""
        session_id = data.get('session_id')
        category = data.get('category', 'unknown')
        priority = data.get('priority', 'medium')
        customer_info = data.get('customer_info', {})

        # 显示表单提交通知
        email = customer_info.get('email', 'N/A')
        order_number = customer_info.get('order_number', 'N/A')

        self.show_status_message(f"📋 新表单提交 - 会话: {session_id[:12]}..., 类别: {category}, 客户: {email}")

        # 刷新会话列表
        self.refresh_sessions(show_loading=False)

        # 如果当前没有选中会话，自动选中这个会话
        if not self.chat_widget.current_session_id:
            QTimer.singleShot(1000, lambda: self.select_session_by_id(session_id))

    def select_session_by_id(self, session_id):
        """根据session_id选中会话"""
        try:
            for i in range(self.session_list.count()):
                item = self.session_list.item(i)
                if item and item.data(Qt.ItemDataRole.UserRole) == session_id:
                    self.session_list.setCurrentItem(item)
                    self.load_session(session_id)
                    break
        except Exception as e:
            logger.error(f"选中会话失败: {e}")

    def handle_connection_status(self, connected):
        """处理连接状态变化"""
        if connected:
            logger.info("WebSocket连接成功")
            self.connection_status.setText("🟢 已连接")
            self.connection_status.setStyleSheet("color: #4CAF50; font-weight: 500;")
        else:
            logger.warning("WebSocket连接断开")
            self.connection_status.setText("🔴 连接断开")
            self.connection_status.setStyleSheet("color: #F44336; font-weight: 500;")

    def handle_connection_error(self, error_msg):
        """处理连接错误"""
        logger.error(f"WebSocket连接错误: {error_msg}")
        self.connection_status.setText("⚠️ 连接错误")
        self.connection_status.setStyleSheet("color: #FF9800; font-weight: 500;")

        # 只记录日志，不显示弹窗（左下角状态栏已经显示连接状态）
        logger.info("系统将尝试自动重连")

    def handle_order_inquiry(self, data):
        """处理订单咨询消息"""
        try:
            # 添加到订单处理组件
            if hasattr(self, 'order_widget'):
                self.order_widget.add_order_inquiry(data)

                # 显示通知
                order_info = data.get('order_info', {})
                order_number = order_info.get('order_number', '未检测到')
                platform = order_info.get('platform', 'unknown')

                self.show_status_message(f"📦 新订单咨询: {order_number} ({platform})")

                # 如果当前不在售后工单选项卡，显示提示
                if (hasattr(self, 'main_tab_widget') and
                    self.main_tab_widget.currentIndex() != 2):  # 售后工单选项卡索引

                    # 在售后工单选项卡标题上显示红点提示
                    current_text = self.main_tab_widget.tabText(2)
                    if "🔴" not in current_text:
                        self.main_tab_widget.setTabText(2, f"🎫 售后工单 🔴")

                logger.info(f"订单咨询已添加到处理队列: {order_number}")

        except Exception as e:
            logger.error(f"处理订单咨询失败: {e}")

    def clear_support_ticket_notification(self):
        """清除售后工单选项卡的通知红点"""
        if hasattr(self, 'main_tab_widget'):
            current_text = self.main_tab_widget.tabText(2)
            if "🔴" in current_text:
                self.main_tab_widget.setTabText(2, "🎫 售后工单")

    def on_main_tab_changed(self, index):
        """处理主选项卡切换事件"""
        if index == 2:  # 售后工单选项卡
            self.clear_support_ticket_notification()

    def refresh_sessions(self, show_loading=True):
        """刷新会话列表（异步）"""
        logger.debug(f"开始刷新会话列表 - 显示加载: {show_loading}")

        def fetch_sessions():
            try:
                # 包含admin_id参数以获取该客服的已读状态
                params = {"admin_id": self.admin_id} if self.admin_id else {}
                logger.debug(f"请求会话列表 - 参数: {params}")

                response = requests.get(
                    f"{self.api_base_url}/api/admin/sessions",
                    headers=self.api_headers,
                    params=params,
                    timeout=10
                )

                if response.status_code == 200:
                    data = response.json()
                    session_count = len(data['sessions'])
                    logger.info(f"成功获取会话列表 - 数量: {session_count}")
                    return data['sessions']
                else:
                    logger.error(f"获取会话列表失败 - 状态码: {response.status_code}")
                    print(f"Failed to fetch sessions: {response.status_code}")
                    return None

            except requests.RequestException as e:
                logger.error(f"请求会话列表时发生网络错误: {e}")
                print(f"Error fetching sessions: {e}")
                return None

        # 显示加载状态
        if show_loading and not self.loading_widget:
            self.show_loading()

        # 异步获取会话
        worker = AsyncWorker(fetch_sessions)
        worker.finished.connect(lambda sessions: self.handle_sessions_loaded(sessions, show_loading))
        worker.error.connect(lambda error: self.handle_sessions_error(error, show_loading))
        worker.start()
        self.api_workers.append(worker)

    def handle_sessions_loaded(self, sessions, show_loading):
        """处理会话加载完成"""
        if show_loading:
            self.hide_loading()

        if sessions is not None:
            # 优先使用服务器返回的已读状态，本地状态作为备份
            for session in sessions:
                session_id = session['id']
                # 如果服务器没有返回已读状态，使用本地备份
                if 'is_read' not in session:
                    local_read_status = self.load_read_status(session_id)
                    session['is_read'] = local_read_status

            self.sessions_list.update_sessions(sessions)

            # 更新统计信息
            total_sessions = len(sessions)
            active_sessions = len([s for s in sessions if s.get('active', True)])

            self.session_count_label.setText(f"({total_sessions})")
            self.session_stats.setText(f"会话: {total_sessions} | 活跃: {active_sessions}")
            self.last_update.setText(f"最后更新: {datetime.now().strftime('%H:%M:%S')}")
        else:
            self.session_stats.setText("会话: 加载失败")

    def handle_sessions_error(self, error, show_loading):
        """处理会话加载错误"""
        if show_loading:
            self.hide_loading()
        print(f"Sessions loading error: {error}")
        self.session_stats.setText("会话: 加载错误")

    def load_session(self, session_id):
        """加载会话消息（异步）"""
        # 标记会话为已读
        self.mark_session_as_read(session_id)

        def fetch_messages():
            try:
                response = requests.get(
                    f"{self.api_base_url}/api/admin/session/{session_id}/messages",
                    headers=self.api_headers,
                    timeout=10
                )

                if response.status_code == 200:
                    data = response.json()
                    return data['messages']
                else:
                    print(f"Failed to fetch session messages: {response.status_code}")
                    return None

            except requests.RequestException as e:
                print(f"Error fetching session messages: {e}")
                return None

        # 显示加载状态
        self.show_loading()

        # 异步获取消息
        worker = AsyncWorker(fetch_messages)
        worker.finished.connect(lambda messages: self.handle_messages_loaded(session_id, messages))
        worker.error.connect(lambda error: self.handle_messages_error(error))
        worker.start()
        self.api_workers.append(worker)

    def mark_session_as_read(self, session_id):
        """标记会话为已读"""
        if session_id in self.sessions_list.sessions:
            # 立即更新本地显示（提供即时反馈）
            self.sessions_list.sessions[session_id]['is_read'] = True
            self.sessions_list.update_session(self.sessions_list.sessions[session_id])
            print(f"会话 {session_id} 已标记为已读")

            # 发送到后端数据库（异步）
            def mark_read_on_server():
                try:
                    response = requests.post(
                        f"{self.api_base_url}/api/admin/session/{session_id}/mark_read",
                        headers=self.api_headers,
                        json={"admin_id": self.admin_id},
                        timeout=10
                    )
                    return response.status_code == 200
                except Exception as e:
                    print(f"Error marking session as read on server: {e}")
                    return False

            # 异步发送到后端
            worker = AsyncWorker(mark_read_on_server)
            worker.finished.connect(lambda success: self.handle_mark_read_result(session_id, success))
            worker.error.connect(lambda error: print(f"Mark read error: {error}"))
            worker.start()
            self.api_workers.append(worker)

            # 同时保存到本地作为备份（防止网络问题）
            self.save_read_status(session_id, True)

    def save_read_status(self, session_id, is_read):
        """保存已读状态到本地设置"""
        self.settings.setValue(f"read_status/{session_id}", is_read)

    def load_read_status(self, session_id):
        """从本地设置加载已读状态"""
        return self.settings.value(f"read_status/{session_id}", False, type=bool)

    def handle_mark_read_result(self, session_id, success):
        """处理标记已读的结果"""
        if success:
            print(f"✅ 会话 {session_id} 已在服务器标记为已读")
        else:
            print(f"⚠️ 服务器标记失败，会话 {session_id} 仅在本地标记为已读")
            # 后端标记失败时，保持本地已读状态
            # 下次刷新时会尝试从服务器获取最新状态

    def handle_messages_loaded(self, session_id, messages):
        """处理消息加载完成"""
        self.hide_loading()

        if messages is not None:
            self.chat_widget.set_session(session_id, messages)
            # 同步会话状态
            self.sync_session_status(session_id)
        else:
            QMessageBox.warning(self, "加载失败", "无法加载会话消息，请重试")

    def handle_messages_error(self, error):
        """处理消息加载错误"""
        self.hide_loading()
        print(f"Messages loading error: {error}")
        QMessageBox.critical(self, "加载错误", f"加载会话消息时发生错误:\n{error}")

    def show_loading(self):
        """显示全局加载状态"""
        if not self.loading_widget:
            self.loading_widget = LoadingWidget(self)

        # 居中显示
        self.loading_widget.move(
            self.width() // 2 - 20,
            self.height() // 2 - 20
        )
        self.loading_widget.start_animation()

    def hide_loading(self):
        """隐藏全局加载状态"""
        if self.loading_widget:
            self.loading_widget.stop_animation()

    def show_settings(self):
        """显示设置对话框"""
        dialog = SettingsDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 应用设置更改
            self.apply_settings_changes()

    def apply_settings_changes(self):
        """应用设置更改"""
        # 重新加载窗口标题
        app_title = self.settings.value("app_title", "YF AI Chat - 智能客服管理系统")
        self.setWindowTitle(app_title)

        # 重新应用样式（如果主题发生变化）
        self.apply_apple_style()

    def filter_unread_sessions(self):
        """筛选未读会话"""
        # 通过chat_widget访问按钮
        if hasattr(self.chat_widget, 'unread_btn') and self.chat_widget.unread_btn.isChecked():
            if hasattr(self.chat_widget, 'read_btn'):
                self.chat_widget.read_btn.setChecked(False)
            # 实现未读会话筛选逻辑
            self.apply_session_filter("unread")
        else:
            self.apply_session_filter("all")

    def filter_read_sessions(self):
        """筛选已读会话"""
        # 通过chat_widget访问按钮
        if hasattr(self.chat_widget, 'read_btn') and self.chat_widget.read_btn.isChecked():
            if hasattr(self.chat_widget, 'unread_btn'):
                self.chat_widget.unread_btn.setChecked(False)
            # 实现已读会话筛选逻辑
            self.apply_session_filter("read")
        else:
            self.apply_session_filter("all")

    def filter_by_time(self, period):
        """按时间筛选会话"""
        from datetime import datetime, timedelta

        now = datetime.now()
        if period == "today":
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
        elif period == "yesterday":
            start_time = (now - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            self.apply_time_filter(start_time, end_time)
            return
        elif period == "week":
            start_time = now - timedelta(days=now.weekday())
            start_time = start_time.replace(hour=0, minute=0, second=0, microsecond=0)
        elif period == "month":
            start_time = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        else:
            start_time = None

        self.apply_time_filter(start_time)

    def apply_session_filter(self, filter_type):
        """应用会话筛选"""
        # 获取所有会话项
        for i in range(self.sessions_list.count()):
            item = self.sessions_list.item(i)
            session_id = item.data(Qt.ItemDataRole.UserRole)
            session_data = self.sessions_list.sessions.get(session_id)

            if not session_data:
                continue

            # 根据筛选类型决定是否显示
            should_show = True
            if filter_type == "unread":
                should_show = not session_data.get('is_read', False)
            elif filter_type == "read":
                should_show = session_data.get('is_read', False)
            # filter_type == "all" 时显示所有会话

            item.setHidden(not should_show)

        # 更新状态消息
        if filter_type == "unread":
            self.show_status_message("显示未读会话")
        elif filter_type == "read":
            self.show_status_message("显示已读会话")
        else:
            self.show_status_message("显示所有会话")

    def apply_time_filter(self, start_time, end_time=None):
        """应用时间筛选"""
        if start_time and end_time:
            self.show_status_message(f"显示 {start_time.strftime('%Y-%m-%d')} 的会话")
        elif start_time:
            self.show_status_message(f"显示 {start_time.strftime('%Y-%m-%d')} 之后的会话")
        else:
            self.show_status_message("显示所有时间的会话")

    def archive_selected_sessions(self):
        """归档选中的会话"""
        # 获取当前选中的会话
        current_item = self.sessions_list.currentItem()
        if current_item:
            session_id = current_item.data(Qt.ItemDataRole.UserRole)
            reply = QMessageBox.question(
                self, "确认归档",
                f"确定要归档会话 {session_id[:12]}... 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.archive_session(session_id)
        else:
            QMessageBox.information(self, "提示", "请先选择要归档的会话")

    def archive_session(self, session_id):
        """归档指定会话"""
        try:
            # 调用后端API归档会话
            response = requests.post(
                f"{self.api_base_url}/api/admin/session/{session_id}/archive",
                headers=self.api_headers,
                json={"admin_id": self.admin_id},
                timeout=10
            )

            if response.status_code == 200:
                # 从会话列表中移除
                self.sessions_list.remove_session(session_id)

                # 如果当前正在查看被归档的会话，清空聊天区域
                if hasattr(self.chat_widget, 'current_session_id') and self.chat_widget.current_session_id == session_id:
                    self.chat_widget.clear_session()

                self.show_status_message(f"会话 {session_id[:12]}... 已归档")
                logger.info(f"会话已归档: {session_id}")

                # 刷新会话列表
                QTimer.singleShot(500, lambda: self.refresh_sessions(show_loading=False))
            else:
                error_msg = response.json().get('detail', '未知错误') if response.content else '网络错误'
                QMessageBox.warning(self, "归档失败", f"归档会话时出现错误: {error_msg}")

        except requests.exceptions.RequestException as e:
            logger.error(f"归档会话网络请求失败: {e}")
            QMessageBox.warning(self, "归档失败", f"网络请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"归档会话失败: {e}")
            QMessageBox.warning(self, "归档失败", f"归档会话时出现错误: {str(e)}")

    def get_session_messages(self, session_id):
        """获取会话消息"""
        try:
            if not self.api_client:
                return []

            response = self.api_client.get(f"/api/sessions/{session_id}/messages")
            if response.status_code == 200:
                return response.json().get('messages', [])
            else:
                logger.warning(f"获取会话消息失败: {response.status_code}")
                return []
        except Exception as e:
            logger.error(f"获取会话消息异常: {e}")
            return []

    def show_archive_dialog(self):
        """显示归档查看对话框"""
        try:
            dialog = ArchiveViewDialog(self, self.api_base_url, self.api_headers, self.admin_id)
            dialog.exec()
        except Exception as e:
            logger.error(f"显示归档对话框失败: {e}")
            QMessageBox.warning(self, "错误", f"无法打开归档查看器: {str(e)}")

    def export_chat_data(self):
        """导出聊天数据"""
        from PyQt6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出聊天数据",
            f"chat_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON文件 (*.json);;CSV文件 (*.csv);;所有文件 (*)"
        )

        if file_path:
            try:
                # 实现导出逻辑
                export_data = {
                    "export_time": datetime.now().isoformat(),
                    "sessions": [],  # 这里应该包含实际的会话数据
                    "total_sessions": 0
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)

                QMessageBox.information(self, "导出成功", f"聊天数据已导出到:\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"导出聊天数据时发生错误:\n{e}")

    def apply_custom_time_filter(self, start_date=None, end_date=None):
        """应用自定义时间范围筛选"""
        if start_date is None or end_date is None:
            # 如果没有传入参数，尝试从ChatWidget获取
            chat_widget = self.chat_widget
            if hasattr(chat_widget, 'start_date_edit') and hasattr(chat_widget, 'end_date_edit'):
                start_date = chat_widget.start_date_edit.date().toPython() if hasattr(chat_widget.start_date_edit.date(), 'toPython') else chat_widget.start_date_edit.date().toPyDate()
                end_date = chat_widget.end_date_edit.date().toPython() if hasattr(chat_widget.end_date_edit.date(), 'toPython') else chat_widget.end_date_edit.date().toPyDate()
            else:
                return

        if start_date > end_date:
            QMessageBox.warning(self, "日期错误", "起始日期不能晚于截止日期")
            return

        self.show_status_message(f"显示 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 的会话")

    def show_status_message(self, message):
        """显示状态消息"""
        # 可以在状态栏或其他地方显示消息
        print(f"状态: {message}")  # 临时使用print，后续可以改为状态栏显示

    def sync_session_status(self, session_id: str):
        """从服务器同步会话状态"""
        def fetch_session_status():
            try:
                response = requests.get(
                    f"{self.api_base_url}/api/admin/session/{session_id}/status",
                    headers=self.api_headers,
                    timeout=10
                )
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 404:
                    # 会话不存在，这是正常情况（可能是新会话或已清理的会话）
                    print(f"Session {session_id[:12]}... not found (404) - this is normal for new sessions")
                    return None
                else:
                    print(f"Failed to fetch session status: {response.status_code}")
                    return None
            except requests.RequestException as e:
                print(f"Error fetching session status: {e}")
                return None

        # 异步获取状态
        worker = AsyncWorker(fetch_session_status)
        worker.finished.connect(lambda status: self.handle_session_status_loaded(session_id, status))
        worker.error.connect(lambda error: print(f"Session status error: {error}"))
        worker.start()
        self.api_workers.append(worker)

    def handle_session_status_loaded(self, session_id: str, status_data):
        """处理会话状态加载完成"""
        if status_data and session_id == self.chat_widget.current_session_id:
            # 更新当前会话的控制状态
            admin_control = status_data.get('admin_control', False)
            self.chat_widget.update_control_status(admin_control)

            # 更新会话列表中的状态
            if session_id in self.sessions_list.sessions:
                self.sessions_list.sessions[session_id]['admin_control'] = admin_control
                self.sessions_list.sessions[session_id]['admin_control_at'] = status_data.get('admin_control_at')
                self.sessions_list.sessions[session_id]['admin_control_by'] = status_data.get('admin_control_by')
                self.sessions_list.update_session(self.sessions_list.sessions[session_id])

    def sync_current_session_status(self):
        """同步当前会话状态"""
        if self.chat_widget.current_session_id:
            self.sync_session_status(self.chat_widget.current_session_id)

    def closeEvent(self, event):
        """窗口关闭事件"""
        logger.info("客服端应用程序开始关闭")

        # 停止系统事件监听器
        if hasattr(self, 'system_monitor'):
            logger.debug("停止系统事件监听器")
            self.system_monitor.stop_monitoring()

        # 停止所有工作线程
        logger.debug(f"停止 {len(self.api_workers)} 个API工作线程")
        for worker in self.api_workers:
            if worker.isRunning():
                worker.quit()
                worker.wait(1000)  # 等待1秒

        # 停止WebSocket连接
        if self.websocket_thread:
            logger.debug("停止WebSocket连接")
            self.websocket_thread.stop()

        # 停止定时器
        if self.refresh_timer:
            logger.debug("停止刷新定时器")
            self.refresh_timer.stop()
        if hasattr(self, 'status_sync_timer') and self.status_sync_timer:
            logger.debug("停止状态同步定时器")
            self.status_sync_timer.stop()
        if hasattr(self, 'email_refresh_timer') and self.email_refresh_timer:
            logger.debug("停止邮件刷新定时器")
            self.email_refresh_timer.stop()
        if hasattr(self, 'websocket_health_timer') and self.websocket_health_timer:
            logger.debug("停止WebSocket健康检查定时器")
            self.websocket_health_timer.stop()

        logger.info("客服端应用程序关闭完成")
        event.accept()

    def create_chat_tab(self):
        """创建聊天管理标签页"""
        chat_tab = QWidget()
        chat_layout = QHBoxLayout(chat_tab)
        chat_layout.setContentsMargins(10, 10, 10, 10)
        chat_layout.setSpacing(10)

        # 左侧面板 - 会话列表
        left_panel_card = ModernCard()
        left_panel_layout = QVBoxLayout(left_panel_card)
        left_panel_layout.setContentsMargins(15, 15, 15, 15)
        left_panel_layout.setSpacing(15)

        # 标题区域
        title_layout = QHBoxLayout()
        sessions_label = QLabel("📋 活跃会话")
        sessions_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        sessions_label.setStyleSheet("color: #1C1C1E;")

        # 会话计数标签
        self.session_count_label = QLabel("(0)")
        self.session_count_label.setFont(QFont("Microsoft YaHei", 12))
        self.session_count_label.setStyleSheet("color: #8E8E93;")

        title_layout.addWidget(sessions_label)
        title_layout.addWidget(self.session_count_label)
        title_layout.addStretch()
        left_panel_layout.addLayout(title_layout)

        # 会话列表
        self.sessions_list = SessionListWidget()
        self.sessions_list.session_selected.connect(self.load_session)
        left_panel_layout.addWidget(self.sessions_list)

        # 控制按钮区域
        button_layout = QHBoxLayout()

        self.refresh_btn = AnimatedButton("🔄 刷新")
        self.refresh_btn.setProperty("buttonStyle", "secondary")
        self.refresh_btn.clicked.connect(self.manual_refresh_sessions)
        self.refresh_btn.setToolTip("手动刷新会话列表")

        self.auto_refresh_btn = AnimatedButton("⏸️ 暂停自动刷新")
        self.auto_refresh_btn.setProperty("buttonStyle", "secondary")
        self.auto_refresh_btn.clicked.connect(self.toggle_auto_refresh)
        self.auto_refresh_btn.setToolTip("切换自动刷新状态")

        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.auto_refresh_btn)
        left_panel_layout.addLayout(button_layout)

        # 归档按钮单独一行
        archive_layout = QHBoxLayout()
        self.archive_view_btn = AnimatedButton("📁 查看归档")
        self.archive_view_btn.setProperty("buttonStyle", "secondary")
        self.archive_view_btn.clicked.connect(self.show_archive_dialog)
        self.archive_view_btn.setToolTip("查看归档的会话")

        archive_layout.addWidget(self.archive_view_btn)
        archive_layout.addStretch()
        left_panel_layout.addLayout(archive_layout)

        # 设置按钮
        settings_layout = QHBoxLayout()
        self.settings_btn = AnimatedButton("设置")
        self.settings_btn.setProperty("buttonStyle", "secondary")
        self.settings_btn.clicked.connect(self.show_settings)
        self.settings_btn.setToolTip("应用设置")

        settings_layout.addWidget(self.settings_btn)
        settings_layout.addStretch()
        left_panel_layout.addLayout(settings_layout)

        # 设置左侧面板宽度
        left_panel_card.setFixedWidth(350)

        # 右侧聊天界面
        self.chat_widget = ChatWidget()
        self.chat_widget.message_sent.connect(self.send_admin_message)
        self.chat_widget.control_requested.connect(self.handle_control_request)

        # 分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(left_panel_card)
        splitter.addWidget(self.chat_widget)
        splitter.setSizes([350, 1050])
        splitter.setHandleWidth(2)

        chat_layout.addWidget(splitter)
        return chat_tab

    def create_email_tab(self):
        """创建邮件管理标签页"""
        # 直接返回邮件管理组件，不再需要子选项卡
        if not hasattr(self, 'email_widget'):
            self.email_widget = EmailWidget()
        return self.email_widget

    def create_support_ticket_tab(self):
        """创建售后工单标签页"""
        # 创建售后工单组件（原来的订单处理组件）
        if not hasattr(self, 'order_widget'):
            self.order_widget = OrderProcessingWidget()
        return self.order_widget

    def refresh_email_widget(self):
        """刷新邮件组件"""
        if hasattr(self, 'email_widget'):
            self.email_widget.load_email_settings()
            self.email_widget.refresh_mailbox_tree()

        if hasattr(self, 'email_tab'):
            self.email_tab.load_email_settings()
            self.email_tab.refresh_mailbox_tree()

def main():
    """主函数 - 增强的DPI支持和现代化启动"""
    # 记录应用启动
    logger.info("客服端应用程序启动")

    # 设置Qt日志过滤，减少无关错误信息
    os.environ["QT_LOGGING_RULES"] = "qt.qpa.screen.debug=false;qt.qpa.screen.warning=false"

    # 设置高DPI支持（在创建QApplication之前）
    os.environ["QT_AUTO_SCREEN_SCALE_FACTOR"] = "1"
    os.environ["QT_ENABLE_HIGHDPI_SCALING"] = "1"
    os.environ["QT_SCALE_FACTOR_ROUNDING_POLICY"] = "RoundPreferFloor"
    logger.debug("高DPI环境变量和Qt日志过滤设置完成")

    app = QApplication(sys.argv)
    app.setApplicationName("YF AI Chat Client")
    app.setOrganizationName("YF Company")
    app.setApplicationVersion("2.1.0")
    logger.info("QApplication初始化完成")

    # 设置应用程序样式
    app.setStyle("Fusion")  # 使用Fusion样式作为基础
    logger.debug("应用程序样式设置为Fusion")

    # 设置高DPI支持属性
    try:
        app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
        app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
        logger.debug("高DPI属性设置成功")
    except AttributeError:
        logger.warning("某些高DPI属性在PyQt6中不可用或已默认启用")

    # 设置字体渲染
    font = app.font()
    font.setFamily("Microsoft YaHei")
    font.setHintingPreference(QFont.HintingPreference.PreferFullHinting)
    app.setFont(font)
    logger.debug("字体设置完成: Microsoft YaHei")

    # 设置应用程序图标
    try:
        # 优先尝试从打包资源中加载图标
        if getattr(sys, 'frozen', False):
            # 打包环境：从exe同目录的resources文件夹加载
            icon_paths = [
                get_config_path('resources/text_icon.ico'),
                get_config_path('resources/text_icon.png')
            ]
        else:
            # 开发环境：从本地文件加载
            icon_paths = [
                get_config_path('resources/text_icon.ico'),
                get_config_path('resources/text_icon.png')
            ]

        icon_loaded = False
        for icon_path in icon_paths:
            if icon_path.exists():
                app.setWindowIcon(QIcon(str(icon_path)))
                logger.info(f"应用程序图标设置成功: {icon_path}")
                icon_loaded = True
                break

        if not icon_loaded:
            logger.warning("未找到应用程序图标文件")
    except Exception as e:
        logger.warning(f"设置应用程序图标失败: {e}")

    try:
        # 显示启动画面（可选）
        splash_pixmap = QPixmap(400, 300)
        splash_pixmap.fill(QColor("#2196F3"))
        # splash = QSplashScreen(splash_pixmap)
        # splash.show()
        # app.processEvents()

        logger.info("创建主窗口")
        window = MainWindow()

        if window.show_login():
            logger.info("用户登录成功，显示主窗口")
            # 隐藏启动画面
            # splash.finish(window)

            window.show()

            # 启动动画效果
            window.setWindowOpacity(0)
            fade_in = QPropertyAnimation(window, b"windowOpacity")
            fade_in.setDuration(600)
            fade_in.setStartValue(0)
            fade_in.setEndValue(1)
            fade_in.setEasingCurve(QEasingCurve.Type.OutCubic)
            fade_in.start()

            # 保持动画引用，防止被垃圾回收
            window._fade_in_animation = fade_in
            logger.debug("主窗口淡入动画启动")

            logger.info("进入应用程序主循环")
            sys.exit(app.exec())
        else:
            logger.info("用户取消登录，退出应用程序")
            sys.exit(0)

    except Exception as e:
        error_msg = f"Application error: {e}"
        logger.error(error_msg)
        logger.error("详细错误信息:", exc_info=True)
        print(error_msg)
        import traceback
        traceback.print_exc()
        QMessageBox.critical(None, "应用程序错误",
                           f"启动应用程序时发生错误:\n{e}\n\n请检查系统环境和依赖项。")
        sys.exit(1)

class OrderProcessingWidget(QWidget):
    """表单提交管理组件 - 处理售后表单提交"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.form_submissions = []  # 存储表单提交

        # 🔧 修复：使用环境变量配置API地址，而不是硬编码
        server_host = os.getenv("SERVER_HOST", "localhost")
        server_port = os.getenv("SERVER_PORT", "8000")
        use_https = os.getenv("USE_HTTPS", "false").lower() == "true"
        protocol = "https" if use_https else "http"
        self.api_base_url = f"{protocol}://{server_host}:{server_port}"

        self.api_headers = {"Authorization": f"Bearer {os.getenv('ADMIN_TOKEN', 'admin_default_token')}"}
        logger.info(f"OrderProcessingWidget API配置 - 服务器: {self.api_base_url}")

        self.setup_ui()
        self.start_polling()

    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题和统计
        header_layout = QHBoxLayout()

        title_label = QLabel("📋 表单提交管理")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #1C1C1E;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #007AFF;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #0056CC;
            }
        """)
        self.refresh_btn.clicked.connect(self.refresh_form_submissions)
        header_layout.addWidget(self.refresh_btn)

        # 统计信息
        self.stats_label = QLabel("未处理: 0 | 已处理: 0")
        self.stats_label.setStyleSheet("color: #8E8E93; font-size: 13px;")
        header_layout.addWidget(self.stats_label)

        layout.addLayout(header_layout)

        # 表单状态选项卡
        self.form_tab_widget = QTabWidget()
        self.form_tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #D1D1D6;
                border-radius: 8px;
                background-color: #FFFFFF;
            }
            QTabBar::tab {
                background-color: #F8F9FA;
                color: #495057;
                padding: 8px 16px;
                margin-right: 1px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-size: 12px;
            }
            QTabBar::tab:selected {
                background-color: #007AFF;
                color: white;
            }
        """)

        # 未处理表单
        self.pending_table = self.create_form_table()
        self.form_tab_widget.addTab(self.pending_table, "🆕 未处理")

        # 已处理表单
        self.completed_table = self.create_form_table()
        self.form_tab_widget.addTab(self.completed_table, "✅ 已处理")

        layout.addWidget(self.form_tab_widget)

        # 表单详情面板
        self.setup_form_details_panel(layout)

    def create_form_table(self):
        """创建表单表格"""
        table = QTableWidget()
        table.setColumnCount(8)
        table.setHorizontalHeaderLabels([
            "提交时间", "会话ID", "类别", "客户邮箱", "订单号", "平台", "优先级", "状态"
        ])

        # 设置表格样式
        table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #D1D1D6;
                border-radius: 8px;
                background-color: #FFFFFF;
                alternate-background-color: #F9F9F9;
                selection-background-color: #007AFF;
                selection-color: white;
                gridline-color: #E5E5E5;
                font-size: 13px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #F0F0F0;
            }
            QTableWidget::item:selected {
                background-color: #007AFF;
                color: white;
            }
            QHeaderView::section {
                background-color: #F8F9FA;
                border: none;
                padding: 8px;
                font-weight: 600;
                color: #495057;
            }
        """)

        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        table.verticalHeader().setVisible(False)

        # 设置列宽
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # 时间
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 会话ID
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)  # 订单号
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)  # 平台
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)  # 问题摘要
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)  # 紧急度
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)  # 优先级

        table.setColumnWidth(0, 120)  # 时间
        table.setColumnWidth(2, 150)  # 订单号
        table.setColumnWidth(3, 80)   # 平台
        table.setColumnWidth(5, 80)   # 紧急度
        table.setColumnWidth(6, 80)   # 优先级

        # 连接选择事件
        table.itemSelectionChanged.connect(self.on_form_selected)

        return table

    def setup_order_details_panel(self, layout):
        """设置订单详情面板"""
        details_frame = QFrame()
        details_frame.setFrameStyle(QFrame.Shape.Box)
        details_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #D1D1D6;
                border-radius: 8px;
                background-color: #FFFFFF;
                padding: 10px;
            }
        """)

        details_layout = QVBoxLayout(details_frame)

        # 详情标题
        details_title = QLabel("📋 订单详情")
        details_title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        details_title.setStyleSheet("color: #1C1C1E; margin-bottom: 10px;")
        details_layout.addWidget(details_title)

        # 订单详情文本
        self.order_details_text = QTextEdit()
        self.order_details_text.setReadOnly(True)
        self.order_details_text.setMaximumHeight(150)
        self.order_details_text.setPlainText("请选择一个订单查看详情...")
        details_layout.addWidget(self.order_details_text)

        # 操作按钮
        button_layout = QHBoxLayout()

        self.start_processing_btn = QPushButton("🔄 开始处理")
        self.start_processing_btn.setProperty("buttonStyle", "primary")
        self.start_processing_btn.clicked.connect(self.start_processing_order)
        self.start_processing_btn.setEnabled(False)
        # 设置黑色文字
        self.start_processing_btn.setStyleSheet("""
            QPushButton[buttonStyle="primary"] {
                color: #1C1C1E !important;
            }
            QPushButton[buttonStyle="primary"]:disabled {
                color: #8E8E93 !important;
            }
        """)
        button_layout.addWidget(self.start_processing_btn)

        self.complete_order_btn = QPushButton("✅ 完成处理")
        self.complete_order_btn.setProperty("buttonStyle", "primary")
        self.complete_order_btn.clicked.connect(self.complete_order)
        self.complete_order_btn.setEnabled(False)
        # 设置黑色文字
        self.complete_order_btn.setStyleSheet("""
            QPushButton[buttonStyle="primary"] {
                color: #1C1C1E !important;
            }
            QPushButton[buttonStyle="primary"]:disabled {
                color: #8E8E93 !important;
            }
        """)
        button_layout.addWidget(self.complete_order_btn)

        self.view_chat_btn = QPushButton("💬 查看对话")
        self.view_chat_btn.setProperty("buttonStyle", "secondary")
        self.view_chat_btn.clicked.connect(self.view_chat_session)
        self.view_chat_btn.setEnabled(False)
        button_layout.addWidget(self.view_chat_btn)

        button_layout.addStretch()

        details_layout.addLayout(button_layout)
        layout.addWidget(details_frame)

    def add_order_inquiry(self, order_data):
        """添加新的订单咨询"""
        self.order_inquiries.append(order_data)
        self.refresh_order_tables()
        self.update_stats()

    def refresh_order_tables(self):
        """刷新订单表格"""
        # 清空所有表格
        self.pending_table.setRowCount(0)
        self.processing_table.setRowCount(0)
        self.completed_table.setRowCount(0)

        # 按状态分类订单
        for order in self.order_inquiries:
            status = order.get('status', 'pending')
            if status == 'pending':
                self.add_order_to_table(self.pending_table, order)
            elif status == 'processing':
                self.add_order_to_table(self.processing_table, order)
            elif status == 'completed':
                self.add_order_to_table(self.completed_table, order)

    def add_order_to_table(self, table, order):
        """添加订单到指定表格"""
        row = table.rowCount()
        table.setRowCount(row + 1)

        # 时间
        timestamp = order.get('timestamp', '')
        if timestamp:
            try:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                time_str = dt.strftime('%m-%d %H:%M')
            except:
                time_str = timestamp[:16] if len(timestamp) > 16 else timestamp
        else:
            time_str = ''
        table.setItem(row, 0, QTableWidgetItem(time_str))

        # 会话ID（简化显示）
        session_id = order.get('session_id', '')
        short_session = session_id[-8:] if len(session_id) > 8 else session_id
        table.setItem(row, 1, QTableWidgetItem(short_session))

        # 订单号
        order_info = order.get('order_info', {})
        order_number = order_info.get('order_number', '未检测到')
        table.setItem(row, 2, QTableWidgetItem(order_number))

        # 平台
        platform = order_info.get('platform', 'unknown')
        platform_display = {
            'amazon': 'Amazon',
            'ebay': 'eBay',
            'walmart': 'Walmart',
            'shopify': 'Shopify',
            'unknown': '未知'
        }.get(platform, platform)
        table.setItem(row, 3, QTableWidgetItem(platform_display))

        # 问题摘要（新增）
        issue_summary = order.get('issue_summary', '售后咨询')
        table.setItem(row, 4, QTableWidgetItem(issue_summary))

        # 紧急度（新增）
        urgency = order.get('urgency', '中')
        urgency_item = QTableWidgetItem(urgency)
        if urgency == '高':
            urgency_item.setBackground(QColor('#FFE6E6'))
            urgency_item.setForeground(QColor('#D32F2F'))
        elif urgency == '中':
            urgency_item.setBackground(QColor('#FFF3E0'))
            urgency_item.setForeground(QColor('#F57C00'))
        else:
            urgency_item.setForeground(QColor('#388E3C'))
        table.setItem(row, 5, urgency_item)

        # 优先级
        priority = order_info.get('priority', 'medium')
        priority_display = {
            'high': '高',
            'medium': '中',
            'low': '低'
        }.get(priority, priority)

        priority_item = QTableWidgetItem(priority_display)
        if priority == 'high':
            priority_item.setBackground(QColor('#FFE6E6'))
        elif priority == 'medium':
            priority_item.setBackground(QColor('#FFF3E0'))
        table.setItem(row, 6, priority_item)

        # 存储完整订单数据
        table.item(row, 0).setData(Qt.ItemDataRole.UserRole, order)

    def on_order_selected(self):
        """处理订单选择事件"""
        # 获取当前选中的表格
        current_table = self.order_tab_widget.currentWidget()
        if not current_table or current_table.currentRow() < 0:
            return

        # 获取订单数据
        current_row = current_table.currentRow()
        item = current_table.item(current_row, 0)
        if not item:
            return

        order_data = item.data(Qt.ItemDataRole.UserRole)
        if not order_data:
            return

        # 显示订单详情
        self.display_order_details(order_data)

        # 更新按钮状态
        status = order_data.get('status', 'pending')
        self.start_processing_btn.setEnabled(status == 'pending')
        self.complete_order_btn.setEnabled(status == 'processing')
        self.view_chat_btn.setEnabled(True)

    def display_order_details(self, order_data):
        """显示订单详情"""
        order_info = order_data.get('order_info', {})

        # 基本信息
        details = f"""📦 订单咨询详情

🕒 时间: {order_data.get('timestamp', '未知')}
🆔 会话ID: {order_data.get('session_id', '未知')}
📋 订单号: {order_info.get('order_number', '未检测到')}
🏪 平台: {order_info.get('platform', '未知')}
📊 状态: {order_data.get('status', 'pending')}

🎯 问题分析:
📝 问题摘要: {order_data.get('issue_summary', '售后咨询')}
🔧 产品类型: {order_data.get('product_type', '未知产品')}
❗ 紧急程度: {order_data.get('urgency', '中')}
⚡ 优先级: {order_info.get('priority', '中等')}

💡 处理建议:
{order_data.get('recommended_action', '了解客户具体需求，提供相应解决方案')}

💬 客户消息:
{order_data.get('customer_message', '无消息内容')}

🔍 检测信息:
- 订单号检测: {'是' if order_info.get('extracted_info', {}).get('has_order_number') else '否'}
- 平台检测: {'是' if order_info.get('extracted_info', {}).get('has_platform') else '否'}
- 售后关键词: {'是' if order_info.get('extracted_info', {}).get('has_after_sales') else '否'}
- 检测置信度: {order_info.get('confidence', 0):.2f}
- 来源: {'Agent引导' if order_data.get('source') == 'agent_guided' else '直接提供'}
"""

        self.order_details_text.setPlainText(details)

    def start_processing_order(self):
        """开始处理订单"""
        current_table = self.order_tab_widget.currentWidget()
        if not current_table or current_table.currentRow() < 0:
            return

        current_row = current_table.currentRow()
        item = current_table.item(current_row, 0)
        order_data = item.data(Qt.ItemDataRole.UserRole)

        # 更新订单状态
        for order in self.order_inquiries:
            if (order.get('session_id') == order_data.get('session_id') and
                order.get('timestamp') == order_data.get('timestamp')):
                order['status'] = 'processing'
                break

        self.refresh_order_tables()
        self.update_stats()

        QMessageBox.information(self, "处理开始", "订单已移至处理中状态")

    def complete_order(self):
        """完成订单处理"""
        current_table = self.order_tab_widget.currentWidget()
        if not current_table or current_table.currentRow() < 0:
            return

        current_row = current_table.currentRow()
        item = current_table.item(current_row, 0)
        order_data = item.data(Qt.ItemDataRole.UserRole)

        # 更新订单状态
        for order in self.order_inquiries:
            if (order.get('session_id') == order_data.get('session_id') and
                order.get('timestamp') == order_data.get('timestamp')):
                order['status'] = 'completed'
                break

        self.refresh_order_tables()
        self.update_stats()

        QMessageBox.information(self, "处理完成", "订单已标记为完成")

    def view_chat_session(self):
        """查看聊天会话"""
        current_table = self.order_tab_widget.currentWidget()
        if not current_table or current_table.currentRow() < 0:
            return

        current_row = current_table.currentRow()
        item = current_table.item(current_row, 0)
        order_data = item.data(Qt.ItemDataRole.UserRole)

        session_id = order_data.get('session_id')
        if session_id:
            # 切换到聊天管理选项卡并选择对应会话
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'main_tab_widget'):
                main_window = main_window.parent()

            if main_window:
                # 切换到聊天管理选项卡
                main_window.main_tab_widget.setCurrentIndex(0)

                # 在会话列表中查找并选择对应会话
                if hasattr(main_window, 'session_list'):
                    for i in range(main_window.session_list.count()):
                        item = main_window.session_list.item(i)
                        if item and session_id in item.text():
                            main_window.session_list.setCurrentItem(item)
                            break

        QMessageBox.information(self, "查看会话", f"已切换到会话: {session_id}")

    def update_stats(self):
        """更新统计信息"""
        pending_count = sum(1 for order in self.order_inquiries if order.get('status', 'pending') == 'pending')
        processing_count = sum(1 for order in self.order_inquiries if order.get('status') == 'processing')
        completed_count = sum(1 for order in self.order_inquiries if order.get('status') == 'completed')

        self.stats_label.setText(f"待处理: {pending_count} | 处理中: {processing_count} | 已完成: {completed_count}")

    def start_polling(self):
        """开始轮询表单提交"""
        self.poll_timer = QTimer()
        self.poll_timer.timeout.connect(self.refresh_form_submissions)
        self.poll_timer.start(5000)  # 每5秒轮询一次

        # 立即执行一次
        self.refresh_form_submissions()

    def refresh_form_submissions(self):
        """刷新表单提交列表"""
        try:
            import requests

            # 获取未处理的表单
            response = requests.get(
                f"{self.api_base_url}/api/admin/form-submissions?status=0",
                headers=self.api_headers,
                timeout=10
            )

            if response.status_code == 200:
                pending_forms = response.json()
                self.update_form_table(self.pending_table, pending_forms)

            # 获取已处理的表单
            response = requests.get(
                f"{self.api_base_url}/api/admin/form-submissions?status=1&limit=20",
                headers=self.api_headers,
                timeout=10
            )

            if response.status_code == 200:
                completed_forms = response.json()
                self.update_form_table(self.completed_table, completed_forms)

            # 更新统计
            self.update_stats()

        except Exception as e:
            print(f"刷新表单提交失败: {e}")

    def update_form_table(self, table, forms):
        """更新表单表格"""
        table.setRowCount(len(forms))

        for row, form in enumerate(forms):
            # 提交时间
            time_item = QTableWidgetItem(form['submitted_at'][:16])  # 只显示日期和时间
            table.setItem(row, 0, time_item)

            # 会话ID（截短显示）
            session_item = QTableWidgetItem(form['session_id'][:12] + "...")
            table.setItem(row, 1, session_item)

            # 类别
            category_item = QTableWidgetItem(form['category'])
            table.setItem(row, 2, category_item)

            # 客户邮箱
            email_item = QTableWidgetItem(form.get('customer_email', 'N/A'))
            table.setItem(row, 3, email_item)

            # 订单号
            order_item = QTableWidgetItem(form.get('customer_order', 'N/A'))
            table.setItem(row, 4, order_item)

            # 平台
            platform = form.get('form_data', {}).get('platform', 'N/A')
            platform_item = QTableWidgetItem(platform)
            table.setItem(row, 5, platform_item)

            # 优先级
            priority_item = QTableWidgetItem(form['priority'])
            if form['priority'] == 'high':
                priority_item.setBackground(QColor(255, 200, 200))
            elif form['priority'] == 'medium':
                priority_item.setBackground(QColor(255, 255, 200))
            table.setItem(row, 6, priority_item)

            # 状态
            status_text = "未处理" if form['status'] == 0 else "已处理"
            status_item = QTableWidgetItem(status_text)
            if form['status'] == 0:
                status_item.setBackground(QColor(255, 200, 200))
            else:
                status_item.setBackground(QColor(200, 255, 200))
            table.setItem(row, 7, status_item)

            # 存储完整表单数据
            table.item(row, 0).setData(Qt.ItemDataRole.UserRole, form)

    def update_stats(self):
        """更新统计信息"""
        pending_count = self.pending_table.rowCount()
        completed_count = self.completed_table.rowCount()
        self.stats_label.setText(f"未处理: {pending_count} | 已处理: {completed_count}")

    def setup_form_details_panel(self, layout):
        """设置表单详情面板"""
        details_frame = QFrame()
        details_frame.setFrameStyle(QFrame.Shape.Box)
        details_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #D1D1D6;
                border-radius: 8px;
                background-color: #FFFFFF;
                padding: 10px;
            }
        """)

        details_layout = QVBoxLayout(details_frame)

        # 详情标题
        details_title = QLabel("📋 表单详情")
        details_title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        details_title.setStyleSheet("color: #1C1C1E; margin-bottom: 10px;")
        details_layout.addWidget(details_title)

        # 表单详情文本
        self.form_details_text = QTextEdit()
        self.form_details_text.setReadOnly(True)
        self.form_details_text.setMaximumHeight(200)
        self.form_details_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #E5E5E5;
                border-radius: 6px;
                padding: 8px;
                background-color: #F9F9F9;
                font-family: 'Consolas', monospace;
                font-size: 12px;
            }
        """)
        details_layout.addWidget(self.form_details_text)

        # 操作按钮
        button_layout = QHBoxLayout()

        self.mark_processed_btn = QPushButton("✅ 标记已处理")
        self.mark_processed_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #6C757D;
            }
        """)
        self.mark_processed_btn.clicked.connect(self.mark_form_processed)
        self.mark_processed_btn.setEnabled(False)
        button_layout.addWidget(self.mark_processed_btn)

        self.add_notes_btn = QPushButton("📝 添加备注")
        self.add_notes_btn.setStyleSheet("""
            QPushButton {
                background-color: #FFC107;
                color: #212529;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #E0A800;
            }
            QPushButton:disabled {
                background-color: #6C757D;
            }
        """)
        self.add_notes_btn.clicked.connect(self.add_form_notes)
        self.add_notes_btn.setEnabled(False)
        button_layout.addWidget(self.add_notes_btn)

        button_layout.addStretch()
        details_layout.addLayout(button_layout)

        layout.addWidget(details_frame)

    def on_form_selected(self):
        """处理表单选择事件"""
        # 获取当前选中的表格
        current_table = self.form_tab_widget.currentWidget()
        if not current_table or current_table.currentRow() < 0:
            self.form_details_text.clear()
            self.mark_processed_btn.setEnabled(False)
            self.add_notes_btn.setEnabled(False)
            return

        # 获取选中的表单数据
        selected_item = current_table.item(current_table.currentRow(), 0)
        if not selected_item:
            return

        form_data = selected_item.data(Qt.ItemDataRole.UserRole)
        if not form_data:
            return

        # 显示表单详情
        self.display_form_details(form_data)

        # 启用操作按钮
        self.mark_processed_btn.setEnabled(form_data['status'] == 0)
        self.add_notes_btn.setEnabled(True)

        # 存储当前选中的表单
        self.current_form = form_data

    def display_form_details(self, form_data):
        """显示表单详情"""
        details = []
        details.append(f"📋 表单ID: {form_data['id']}")
        details.append(f"🕒 提交时间: {form_data['submitted_at']}")
        details.append(f"📂 类别: {form_data['category']}")
        details.append(f"⚡ 优先级: {form_data['priority']}")
        details.append(f"📧 客户邮箱: {form_data.get('customer_email', 'N/A')}")
        details.append(f"📦 订单号: {form_data.get('customer_order', 'N/A')}")
        details.append("")
        details.append("📝 表单数据:")

        form_fields = form_data.get('form_data', {})
        for key, value in form_fields.items():
            details.append(f"  • {key}: {value}")

        if form_data.get('notes'):
            details.append("")
            details.append(f"💬 备注: {form_data['notes']}")

        self.form_details_text.setPlainText("\n".join(details))

    def mark_form_processed(self):
        """标记表单为已处理"""
        if not hasattr(self, 'current_form'):
            return

        try:
            import requests

            response = requests.put(
                f"{self.api_base_url}/api/admin/form-submissions/{self.current_form['id']}",
                headers=self.api_headers,
                json={
                    "status": 1,
                    "processed_by": "admin"  # 这里应该使用实际的管理员ID
                },
                timeout=10
            )

            if response.status_code == 200:
                QMessageBox.information(self, "成功", "表单已标记为已处理")
                self.refresh_form_submissions()
            else:
                QMessageBox.warning(self, "失败", "标记失败，请重试")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"操作失败: {str(e)}")

    def add_form_notes(self):
        """添加表单备注"""
        if not hasattr(self, 'current_form'):
            return

        notes, ok = QInputDialog.getMultiLineText(
            self, "添加备注", "请输入处理备注:",
            self.current_form.get('notes', '')
        )

        if ok:
            try:
                import requests

                response = requests.put(
                    f"{self.api_base_url}/api/admin/form-submissions/{self.current_form['id']}",
                    headers=self.api_headers,
                    json={"notes": notes},
                    timeout=10
                )

                if response.status_code == 200:
                    QMessageBox.information(self, "成功", "备注已保存")
                    self.refresh_form_submissions()
                else:
                    QMessageBox.warning(self, "失败", "保存失败，请重试")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")


class EmailWidget(QWidget):
    """邮件管理组件 - Thunderbird风格布局"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_mailbox = None
        self.current_folder = None
        self.mailboxes = {}  # 存储邮箱配置
        self.load_email_settings()  # 先加载邮箱配置
        self.setup_ui()  # 然后设置UI界面

    def decode_mutf7(self, s):
        """
        解码Modified UTF-7编码的IMAP文件夹名称
        这是IMAP协议使用的标准编码方式
        """
        if not s:
            return s

        # 如果不包含&，直接返回
        if '&' not in s:
            return s

        # 处理Modified UTF-7编码
        result = []
        i = 0
        while i < len(s):
            if s[i] == '&':
                # 找到对应的-
                j = i + 1
                while j < len(s) and s[j] != '-':
                    j += 1

                if j < len(s):  # 找到了-
                    encoded_part = s[i+1:j]
                    if encoded_part == '':
                        # &- 表示字面的&
                        result.append('&')
                    else:
                        try:
                            # 替换,为/，这是Modified UTF-7的特殊处理
                            encoded_part = encoded_part.replace(',', '/')

                            # 添加必要的padding
                            while len(encoded_part) % 4:
                                encoded_part += '='

                            # Base64解码
                            decoded_bytes = base64.b64decode(encoded_part)

                            # UTF-16BE解码
                            decoded_str = decoded_bytes.decode('utf-16be')
                            result.append(decoded_str)
                        except Exception as e:
                            # 如果解码失败，保留原始字符串
                            result.append(s[i:j+1])

                    i = j + 1
                else:
                    # 没有找到对应的-，保留原字符
                    result.append(s[i])
                    i += 1
            else:
                result.append(s[i])
                i += 1

        return ''.join(result)

    def classify_folder_by_name(self, folder_name):
        """
        根据文件夹名称智能分类
        支持多语言和常见的文件夹名称
        """
        folder_name_lower = folder_name.lower()

        # 收件箱
        if folder_name_lower in ['inbox', '收件箱', 'received']:
            return 'INBOX'

        # 发件箱
        elif any(keyword in folder_name_lower for keyword in [
            'sent', 'send', '发送', '已发送', '发件箱', 'outbox'
        ]):
            return 'Sent'

        # 草稿箱
        elif any(keyword in folder_name_lower for keyword in [
            'draft', '草稿', 'drafts'
        ]):
            return 'Drafts'

        # 垃圾邮件
        elif any(keyword in folder_name_lower for keyword in [
            'spam', 'junk', '垃圾', '垃圾邮件', 'bulk'
        ]):
            return 'Spam'

        # 已删除
        elif any(keyword in folder_name_lower for keyword in [
            'trash', 'delete', '删除', '已删除', 'bin', 'recycle'
        ]):
            return 'Trash'

        # 其他常见文件夹
        elif any(keyword in folder_name_lower for keyword in [
            'archive', '归档', '存档'
        ]):
            return 'Archive'

        elif any(keyword in folder_name_lower for keyword in [
            'template', '模板'
        ]):
            return 'Templates'

        else:
            return 'Other'

    def detect_smart_folder_mapping(self, imap):
        """
        智能检测文件夹映射
        返回标准文件夹类型到服务器文件夹名称的映射
        """
        try:
            # 获取所有文件夹
            status, folder_list = imap.list()

            smart_mapping = {
                'INBOX': None,
                'Sent': None,
                'Drafts': None,
                'Spam': None,
                'Trash': None
            }

            if status == 'OK':
                for folder_line in folder_list:
                    # 解析文件夹名称
                    folder_match = re.search(r'"([^"]*)"$', folder_line.decode())
                    if folder_match:
                        encoded_name = folder_match.group(1)

                        # 解码Modified UTF-7
                        decoded_name = self.decode_mutf7(encoded_name)

                        # 智能分类
                        folder_type = self.classify_folder_by_name(decoded_name)

                        # 只映射标准文件夹类型
                        if folder_type in smart_mapping and smart_mapping[folder_type] is None:
                            smart_mapping[folder_type] = encoded_name

            return smart_mapping

        except Exception as e:
            print(f"智能文件夹检测失败: {e}")
            return {}

    def setup_ui(self):
        """设置邮件界面 - Thunderbird风格三栏布局"""
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建主分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_splitter.setHandleWidth(2)

        # 左侧邮箱树栏
        self.setup_mailbox_tree()
        main_splitter.addWidget(self.mailbox_tree_widget)

        # 右侧内容区域
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(10, 10, 10, 10)
        right_layout.setSpacing(10)

        # 工具栏
        self.setup_toolbar(right_layout)

        # 邮件内容分割器（上下分割）
        content_splitter = QSplitter(Qt.Orientation.Vertical)

        # 邮件列表
        self.setup_email_list()
        content_splitter.addWidget(self.email_list_widget)

        # 邮件预览区域
        self.setup_email_preview()
        content_splitter.addWidget(self.email_preview_widget)

        # 设置分割器比例
        content_splitter.setSizes([300, 200])
        right_layout.addWidget(content_splitter)

        main_splitter.addWidget(right_widget)

        # 设置主分割器比例
        main_splitter.setSizes([250, 750])
        main_layout.addWidget(main_splitter)

    def setup_mailbox_tree(self):
        """设置左侧邮箱树栏"""
        self.mailbox_tree_widget = QWidget()
        tree_layout = QVBoxLayout(self.mailbox_tree_widget)
        tree_layout.setContentsMargins(10, 10, 10, 10)
        tree_layout.setSpacing(10)

        # 标题和配置按钮
        header_layout = QHBoxLayout()

        tree_title = QLabel("📧 邮箱")
        tree_title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        tree_title.setStyleSheet("color: #1C1C1E;")
        header_layout.addWidget(tree_title)

        header_layout.addStretch()

        # 邮箱配置按钮
        self.config_btn = QPushButton("设置")
        self.config_btn.setFixedSize(50, 32)
        self.config_btn.setToolTip("邮箱配置")
        self.config_btn.clicked.connect(self.show_email_config_dialog)
        self.config_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFFFFF, stop:1 #F8F9FA);
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 16px;
                color: #495057;
                font-size: 12px;
                font-weight: 500;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F8F9FA, stop:1 #E9ECEF);
                border-color: rgba(0, 122, 255, 0.3);
                color: #007AFF;
                transform: scale(1.02);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E9ECEF, stop:1 #DEE2E6);
                border-color: rgba(0, 122, 255, 0.5);
                color: #0056CC;
            }
        """)



        header_layout.addWidget(self.config_btn)

        tree_layout.addLayout(header_layout)

        # 邮箱树
        from PyQt6.QtWidgets import QTreeWidget, QTreeWidgetItem
        self.mailbox_tree = QTreeWidget()
        self.mailbox_tree.setHeaderHidden(True)
        self.mailbox_tree.setRootIsDecorated(True)
        self.mailbox_tree.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #D1D1D6;
                border-radius: 8px;
                background-color: #FFFFFF;
                alternate-background-color: #F2F2F7;
                selection-background-color: #007AFF;
                selection-color: white;
                outline: none;
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Arial, sans-serif;
                font-size: 13px;
            }
            QTreeWidget::item {
                padding: 8px 12px;
                border-bottom: 1px solid #F2F2F7;
                border-radius: 4px;
                margin: 1px;
            }
            QTreeWidget::item:hover {
                background-color: #F2F2F7;
            }
            QTreeWidget::item:selected {
                background-color: #007AFF;
                color: white;
            }
            QTreeWidget::branch:has-siblings:!adjoins-item {
                border-image: none;
                border: none;
            }
            QTreeWidget::branch:has-siblings:adjoins-item {
                border-image: none;
                border: none;
            }
            QTreeWidget::branch:!has-children:!has-siblings:adjoins-item {
                border-image: none;
                border: none;
            }
            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {
                border-image: none;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTYgNEwxMCA4TDYgMTJWNFoiIGZpbGw9IiM4RThFOTMiLz4KPHN2Zz4K);
            }
            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {
                border-image: none;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDZINFoiIGZpbGw9IiM4RThFOTMiLz4KPHN2Zz4K);
            }
        """)
        self.mailbox_tree.itemClicked.connect(self.on_tree_item_clicked)

        # 设置右键菜单
        self.mailbox_tree.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.mailbox_tree.customContextMenuRequested.connect(self.show_mailbox_context_menu)

        tree_layout.addWidget(self.mailbox_tree)

        # 初始化邮箱树
        self.refresh_mailbox_tree()

    def setup_toolbar(self, parent_layout):
        """设置工具栏"""
        toolbar_layout = QHBoxLayout()

        # 撰写邮件按钮
        self.compose_btn = AnimatedButton("✍️ 撰写邮件")
        self.compose_btn.setProperty("buttonStyle", "primary")
        self.compose_btn.clicked.connect(self.compose_email)
        self.compose_btn.setStyleSheet("""
            QPushButton[buttonStyle="primary"] {
                color: #1C1C1E !important;
            }
        """)
        toolbar_layout.addWidget(self.compose_btn)

        # 刷新按钮
        self.refresh_btn = AnimatedButton("🔄 刷新")
        self.refresh_btn.setProperty("buttonStyle", "secondary")
        self.refresh_btn.clicked.connect(self.refresh_current_folder)
        toolbar_layout.addWidget(self.refresh_btn)

        # 自动刷新按钮
        self.auto_refresh_email_btn = AnimatedButton("⏸️ 暂停自动刷新")
        self.auto_refresh_email_btn.setProperty("buttonStyle", "secondary")
        self.auto_refresh_email_btn.clicked.connect(self.toggle_email_auto_refresh_ui)
        self.auto_refresh_email_btn.setToolTip("切换邮件自动刷新状态")
        toolbar_layout.addWidget(self.auto_refresh_email_btn)

        toolbar_layout.addStretch()

        # 搜索框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 搜索邮件...")
        self.search_input.setMaximumWidth(200)
        self.search_input.textChanged.connect(self.search_emails)
        toolbar_layout.addWidget(self.search_input)

        parent_layout.addLayout(toolbar_layout)

    def setup_email_list(self):
        """设置邮件列表"""
        self.email_list_widget = QWidget()
        list_layout = QVBoxLayout(self.email_list_widget)
        list_layout.setContentsMargins(0, 0, 0, 0)
        list_layout.setSpacing(5)

        # 邮件列表标题
        self.list_title = QLabel("📬 收件箱")
        self.list_title.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        self.list_title.setStyleSheet("color: #1C1C1E; padding: 5px;")
        list_layout.addWidget(self.list_title)

        # 邮件列表表格
        self.email_table = QTableWidget()
        self.email_table.setColumnCount(4)
        self.email_table.setHorizontalHeaderLabels(["发件人", "主题", "日期", "大小"])

        # 设置表格样式
        self.email_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #D1D1D6;
                border-radius: 8px;
                background-color: #FFFFFF;
                alternate-background-color: #F9F9F9;
                selection-background-color: #007AFF;
                selection-color: white;
                gridline-color: #E5E5E5;
                font-size: 13px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #F0F0F0;
            }
            QTableWidget::item:selected {
                background-color: #007AFF;
                color: white;
            }
            QHeaderView::section {
                background-color: #F8F9FA;
                border: 1px solid #E5E5E5;
                padding: 8px;
                font-weight: bold;
                color: #495057;
            }
        """)

        # 设置表格属性
        self.email_table.setAlternatingRowColors(True)
        self.email_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.email_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.email_table.verticalHeader().setVisible(False)

        # 设置列宽
        header = self.email_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)

        self.email_table.setColumnWidth(0, 150)  # 发件人
        self.email_table.setColumnWidth(2, 120)  # 日期
        self.email_table.setColumnWidth(3, 80)   # 大小

        self.email_table.itemSelectionChanged.connect(self.on_email_selected)

        # 设置右键菜单
        self.email_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.email_table.customContextMenuRequested.connect(self.show_email_context_menu)

        list_layout.addWidget(self.email_table)

    def setup_email_preview(self):
        """设置邮件预览区域"""
        self.email_preview_widget = QWidget()
        preview_layout = QVBoxLayout(self.email_preview_widget)
        preview_layout.setContentsMargins(0, 0, 0, 0)
        preview_layout.setSpacing(5)

        # 预览标题
        preview_title = QLabel("📖 邮件预览")
        preview_title.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        preview_title.setStyleSheet("color: #1C1C1E; padding: 5px;")
        preview_layout.addWidget(preview_title)

        # 邮件详情区域
        self.email_details = QTextEdit()
        self.email_details.setReadOnly(True)
        self.email_details.setStyleSheet("""
            QTextEdit {
                border: 1px solid #D1D1D6;
                border-radius: 8px;
                background-color: #FFFFFF;
                font-size: 13px;
                line-height: 1.5;
                padding: 10px;
            }
        """)
        self.email_details.setPlainText("选择一封邮件以查看详情...")
        preview_layout.addWidget(self.email_details)

    def load_email_settings(self):
        """加载邮件设置"""
        try:
            import json
            import os

            # 使用mail.json文件保存邮箱配置
            mail_config_file = str(get_config_path('mail.json'))

            if os.path.exists(mail_config_file):
                with open(mail_config_file, 'r', encoding='utf-8') as f:
                    mail_data = json.load(f)
                    self.mailboxes = mail_data.get('mailboxes', {})
            else:
                # 如果mail.json不存在，尝试从QSettings迁移数据
                self.migrate_from_qsettings_to_json()

        except Exception as e:
            print(f"加载邮件设置失败: {e}")
            self.mailboxes = {}

    def migrate_from_qsettings_to_json(self):
        """从QSettings迁移邮箱配置到JSON文件"""
        try:
            settings = QSettings("YFCompany", "YFAIChat")
            mailbox_count = settings.value("mailbox_count", 0, type=int)

            if mailbox_count > 0:
                for i in range(mailbox_count):
                    mailbox_name = settings.value(f"mailbox_{i}_name", f"邮箱{i+1}")
                    mailbox_config = {
                        'name': mailbox_name,
                        'email': settings.value(f"mailbox_{i}_email", ""),
                        'password': settings.value(f"mailbox_{i}_password", ""),
                        'imap_server': settings.value(f"mailbox_{i}_imap_server", ""),
                        'imap_port': settings.value(f"mailbox_{i}_imap_port", 993, type=int),
                        'imap_ssl': settings.value(f"mailbox_{i}_imap_ssl", True, type=bool),
                        'smtp_server': settings.value(f"mailbox_{i}_smtp_server", ""),
                        'smtp_port': settings.value(f"mailbox_{i}_smtp_port", 587, type=int),
                        'smtp_ssl': settings.value(f"mailbox_{i}_smtp_ssl", True, type=bool),
                    }
                    self.mailboxes[mailbox_name] = mailbox_config

                # 保存到JSON文件
                self.save_email_settings()
                print("已成功迁移邮箱配置到mail.json文件")
        except Exception as e:
            print(f"迁移邮箱配置失败: {e}")
            self.mailboxes = {}

    def save_email_settings(self):
        """保存邮件设置到JSON文件"""
        try:
            import json
            import os

            mail_config_file = str(get_config_path('mail.json'))
            from datetime import datetime
            mail_data = {
                'mailboxes': self.mailboxes,
                'version': '1.0',
                'last_updated': str(datetime.now())
            }

            with open(mail_config_file, 'w', encoding='utf-8') as f:
                json.dump(mail_data, f, indent=2, ensure_ascii=False)

            return True
        except Exception as e:
            print(f"保存邮件设置失败: {e}")
            return False

    def refresh_mailbox_tree(self):
        """刷新邮箱树"""
        try:
            # 检查mailbox_tree是否仍然有效
            if not hasattr(self, 'mailbox_tree') or self.mailbox_tree is None:
                return

            from PyQt6.QtWidgets import QTreeWidgetItem
            self.mailbox_tree.clear()

            if not self.mailboxes:
                # 如果没有配置邮箱，显示提示
                no_mailbox_item = QTreeWidgetItem(["📭 未配置邮箱"])
                no_mailbox_item.setData(0, Qt.ItemDataRole.UserRole, {'type': 'no_mailbox'})
                self.mailbox_tree.addTopLevelItem(no_mailbox_item)
                return

            # 添加邮箱和文件夹
            for mailbox_name, config in self.mailboxes.items():
                # 邮箱根节点
                mailbox_item = QTreeWidgetItem([f"📧 {mailbox_name}"])
                mailbox_item.setData(0, Qt.ItemDataRole.UserRole, {
                    'type': 'mailbox',
                    'name': mailbox_name,
                    'config': config
                })
                self.mailbox_tree.addTopLevelItem(mailbox_item)

                # 添加标准文件夹
                folders = [
                    ("📥 收件箱", "INBOX"),
                    ("📤 发件箱", "Sent"),
                    ("📝 草稿箱", "Drafts"),
                    ("🗑️ 垃圾邮件", "Spam"),
                    ("🗂️ 已删除", "Trash")
                ]

                for folder_display, folder_name in folders:
                    # 异步获取文件夹邮件数量
                    folder_text = folder_display
                    folder_item = QTreeWidgetItem([folder_text])
                    folder_item.setData(0, Qt.ItemDataRole.UserRole, {
                        'type': 'folder',
                        'mailbox': mailbox_name,
                        'folder': folder_name,
                        'display_name': folder_display
                    })
                    mailbox_item.addChild(folder_item)

                # 默认展开邮箱
                mailbox_item.setExpanded(True)

                # 异步加载文件夹邮件数量
                self.load_folder_counts_async(mailbox_name)

        except RuntimeError as e:
            # 捕获"wrapped C/C++ object has been deleted"错误
            print(f"邮箱树刷新时出现错误（对象已被删除）: {e}")
        except Exception as e:
            print(f"刷新邮箱树时出现错误: {e}")

    def load_folder_counts_async(self, mailbox_name):
        """异步加载文件夹邮件数量"""
        from PyQt6.QtCore import QThread, pyqtSignal

        class FolderCountLoader(QThread):
            counts_loaded = pyqtSignal(str, dict)  # mailbox_name, folder_counts

            def __init__(self, parent_widget, mailbox_name):
                super().__init__()
                self.parent_widget = parent_widget
                self.mailbox_name = mailbox_name

            def run(self):
                try:
                    counts = self.parent_widget.get_folder_counts(self.mailbox_name)
                    self.counts_loaded.emit(self.mailbox_name, counts)
                except Exception as e:
                    print(f"获取文件夹数量失败: {e}")
                    # 发送空的计数结果
                    self.counts_loaded.emit(self.mailbox_name, {})

        self.folder_count_loader = FolderCountLoader(self, mailbox_name)
        self.folder_count_loader.counts_loaded.connect(self.update_folder_counts)
        self.folder_count_loader.start()

    def get_folder_counts(self, mailbox_name):
        """获取文件夹邮件数量"""
        config = self.mailboxes[mailbox_name]
        folder_counts = {}

        try:
            import imaplib
            import re

            # 连接IMAP服务器
            if config['imap_ssl']:
                imap = imaplib.IMAP4_SSL(config['imap_server'], config['imap_port'])
            else:
                imap = imaplib.IMAP4(config['imap_server'], config['imap_port'])

            # 登录
            imap.login(config['email'], config['password'])

            # 使用智能文件夹检测替代硬编码映射
            smart_mapping = self.detect_smart_folder_mapping(imap)

            # 获取每个智能检测到的文件夹的邮件数量
            for folder_type, server_folder in smart_mapping.items():
                if server_folder:  # 如果检测到了对应的文件夹
                    try:
                        # 选择文件夹
                        status, count_data = imap.select(server_folder)
                        if status == 'OK':
                            total_count = int(count_data[0])

                            # 获取未读邮件数量
                            status, unread_data = imap.search(None, 'UNSEEN')
                            unread_count = 0
                            if status == 'OK' and unread_data[0]:
                                unread_count = len(unread_data[0].split())

                            folder_counts[folder_type] = {
                                'total': total_count,
                                'unread': unread_count,
                                'read': total_count - unread_count
                            }

                            # 解码文件夹名称用于日志
                            decoded_name = self.decode_mutf7(server_folder)
                            print(f"📊 {folder_type} ({decoded_name}): {total_count} 封邮件 ({unread_count} 未读)")
                    except Exception as e:
                        print(f"获取文件夹 {server_folder} 邮件数量失败: {e}")
                        folder_counts[folder_type] = {'total': 0, 'unread': 0, 'read': 0}
                else:
                    # 如果没有检测到对应文件夹，设置为0
                    folder_counts[folder_type] = {'total': 0, 'unread': 0, 'read': 0}

            imap.logout()
            return folder_counts

        except Exception as e:
            print(f"获取邮件数量失败: {e}")
            return {}

    def update_folder_counts(self, mailbox_name, folder_counts):
        """更新文件夹数量显示"""
        try:
            # 查找对应的邮箱项
            for i in range(self.mailbox_tree.topLevelItemCount()):
                mailbox_item = self.mailbox_tree.topLevelItem(i)
                data = mailbox_item.data(0, Qt.ItemDataRole.UserRole)

                if data and data.get('type') == 'mailbox' and data.get('name') == mailbox_name:
                    # 更新子文件夹的显示
                    for j in range(mailbox_item.childCount()):
                        folder_item = mailbox_item.child(j)
                        folder_data = folder_item.data(0, Qt.ItemDataRole.UserRole)

                        if folder_data and folder_data.get('type') == 'folder':
                            folder_name = folder_data.get('folder')
                            display_name = folder_data.get('display_name')

                            if folder_name in folder_counts:
                                counts = folder_counts[folder_name]
                                total = counts.get('total', 0)
                                unread = counts.get('unread', 0)

                                # 格式化显示文本
                                if unread > 0:
                                    folder_text = f"{display_name} ({unread}/{total})"
                                    # 设置粗体显示有未读邮件的文件夹
                                    font = folder_item.font(0)
                                    font.setBold(True)
                                    folder_item.setFont(0, font)
                                else:
                                    folder_text = f"{display_name} ({total})"
                                    # 移除粗体
                                    font = folder_item.font(0)
                                    font.setBold(False)
                                    folder_item.setFont(0, font)

                                folder_item.setText(0, folder_text)
                    break

        except Exception as e:
            print(f"更新文件夹数量显示失败: {e}")

    def on_tree_item_clicked(self, item, column):
        """处理树项点击事件"""
        data = item.data(0, Qt.ItemDataRole.UserRole)
        if not data:
            return

        if data['type'] == 'folder':
            self.current_mailbox = data['mailbox']
            self.current_folder = data['folder']
            self.list_title.setText(data['display_name'])
            self.load_folder_emails(data['mailbox'], data['folder'])
        elif data['type'] == 'mailbox':
            # 点击邮箱时默认显示收件箱
            self.current_mailbox = data['name']
            self.current_folder = "INBOX"
            self.list_title.setText("📥 收件箱")
            self.load_folder_emails(data['name'], "INBOX")

    def load_folder_emails(self, mailbox_name, folder_name):
        """加载文件夹中的邮件"""
        # 清空当前邮件列表
        self.email_table.setRowCount(0)
        self.email_details.setPlainText("正在加载邮件...")

        # 检查邮箱配置是否存在
        if mailbox_name not in self.mailboxes:
            self.email_details.setPlainText(f"邮箱 '{mailbox_name}' 配置不存在，请先配置邮箱。")
            return

        # 显示加载状态
        self.refresh_btn.setText("🔄 加载中...")
        self.refresh_btn.setEnabled(False)

        # 异步加载邮件
        from PyQt6.QtCore import QThread, pyqtSignal

        class EmailLoader(QThread):
            emails_loaded = pyqtSignal(list)
            error_occurred = pyqtSignal(str)

            def __init__(self, parent_widget, mailbox_name, folder_name):
                super().__init__()
                self.parent_widget = parent_widget
                self.mailbox_name = mailbox_name
                self.folder_name = folder_name

            def run(self):
                try:
                    emails = self.parent_widget.fetch_emails_from_server(self.mailbox_name, self.folder_name)
                    self.emails_loaded.emit(emails)
                except Exception as e:
                    self.error_occurred.emit(str(e))

        self.email_loader = EmailLoader(self, mailbox_name, folder_name)
        self.email_loader.emails_loaded.connect(self.on_emails_loaded)
        self.email_loader.error_occurred.connect(self.on_email_load_error)
        self.email_loader.start()

    def on_emails_loaded(self, emails):
        """邮件加载完成"""
        self.refresh_btn.setText("🔄 刷新")
        self.refresh_btn.setEnabled(True)

        if emails:
            self.email_table.setRowCount(len(emails))
            # 存储完整的邮件数据用于预览
            self.current_emails = emails

            # 统计邮件数量
            total_count = len(emails)
            unread_count = sum(1 for email in emails if email.get('unread', False))
            read_count = total_count - unread_count

            for row, email_data in enumerate(emails):
                # 设置表格项
                sender_item = QTableWidgetItem(email_data.get('sender', ''))
                subject_item = QTableWidgetItem(email_data.get('subject', ''))
                date_item = QTableWidgetItem(email_data.get('date', ''))
                size_item = QTableWidgetItem(email_data.get('size', ''))

                # 如果是未读邮件，设置粗体显示
                if email_data.get('unread', False):
                    font = sender_item.font()
                    font.setBold(True)
                    sender_item.setFont(font)
                    subject_item.setFont(font)
                    date_item.setFont(font)
                    size_item.setFont(font)

                self.email_table.setItem(row, 0, sender_item)
                self.email_table.setItem(row, 1, subject_item)
                self.email_table.setItem(row, 2, date_item)
                self.email_table.setItem(row, 3, size_item)

            # 更新标题显示邮件数量
            if hasattr(self, 'current_folder') and hasattr(self, 'current_mailbox'):
                folder_display = self.get_folder_display_name(self.current_folder)
                if unread_count > 0:
                    title_text = f"{folder_display} ({unread_count}/{total_count})"
                else:
                    title_text = f"{folder_display} ({total_count})"
                self.list_title.setText(title_text)

            self.email_details.setPlainText(f"✅ 成功加载 {total_count} 封邮件 (未读: {unread_count}, 已读: {read_count})\n\n点击邮件查看详细内容")
        else:
            self.current_emails = []
            folder_display = self.get_folder_display_name(self.current_folder) if hasattr(self, 'current_folder') else "当前文件夹"
            self.list_title.setText(f"{folder_display} (0)")
            self.email_details.setPlainText(f"📭 文件夹 '{self.current_folder}' 中没有邮件")

    def get_folder_display_name(self, folder_name):
        """获取文件夹显示名称"""
        folder_names = {
            "INBOX": "📥 收件箱",
            "Sent": "📤 发件箱",
            "Drafts": "📝 草稿箱",
            "Spam": "🗑️ 垃圾邮件",
            "Trash": "🗂️ 已删除"
        }
        return folder_names.get(folder_name, f"📁 {folder_name}")

    def on_email_load_error(self, error_message):
        """邮件加载错误"""
        self.refresh_btn.setText("🔄 刷新")
        self.refresh_btn.setEnabled(True)

        # 显示错误信息和解决建议
        self.email_details.setPlainText(f"""❌ 无法连接到邮件服务器

错误详情: {error_message}

🔧 解决建议:
• 检查网络连接是否正常
• 验证邮箱配置是否正确
• 确认邮箱服务已开启IMAP访问
• 检查防火墙是否阻止连接
• 尝试使用应用专用密码（如Gmail）

点击右上角的⚙️按钮重新配置邮箱。""")

    def fetch_emails_from_server(self, mailbox_name, folder_name):
        """从邮件服务器获取邮件"""
        config = self.mailboxes[mailbox_name]

        try:
            import imaplib
            import email
            from email.header import decode_header
            import datetime
            import re

            # 连接IMAP服务器
            if config['imap_ssl']:
                imap = imaplib.IMAP4_SSL(config['imap_server'], config['imap_port'])
            else:
                imap = imaplib.IMAP4(config['imap_server'], config['imap_port'])

            # 登录
            imap.login(config['email'], config['password'])

            # 使用智能文件夹检测
            smart_mapping = self.detect_smart_folder_mapping(imap)

            # 查找匹配的文件夹
            imap_folder = smart_mapping.get(folder_name, "INBOX")
            if not imap_folder:
                imap_folder = "INBOX"  # 默认回退到收件箱

            status, count = imap.select(imap_folder)
            if status != 'OK':
                raise Exception(f"无法选择文件夹 '{imap_folder}'")

            # 搜索邮件（最近30封）
            status, messages = imap.search(None, 'ALL')
            if status != 'OK':
                return []

            email_ids = messages[0].split()
            total_emails = len(email_ids)

            # 只取最近的30封邮件
            recent_ids = email_ids[-30:] if len(email_ids) > 30 else email_ids

            emails = []
            for i, email_id in enumerate(reversed(recent_ids)):  # 最新的在前
                try:
                    print(f"📧 解析邮件 {i+1}/{len(recent_ids)}")

                    # 获取邮件头信息和标志
                    status, msg_data = imap.fetch(email_id, '(RFC822.HEADER FLAGS)')
                    if status != 'OK':
                        continue

                    # 解析标志（是否已读等）
                    flags = msg_data[1].decode() if len(msg_data) > 1 else ""
                    is_unread = '\\Seen' not in flags

                    # 解析邮件头
                    email_message = email.message_from_bytes(msg_data[0][1])

                    # 解析发件人
                    sender = self.decode_email_header(email_message.get('From', ''))
                    # 提取邮箱地址
                    sender_match = re.search(r'<([^>]+)>', sender)
                    if sender_match:
                        sender_email = sender_match.group(1)
                        sender_name = sender.replace(f'<{sender_email}>', '').strip(' "')
                        sender = f"{sender_name} <{sender_email}>" if sender_name else sender_email

                    # 解析收件人（对于发件箱）
                    to_addr = self.decode_email_header(email_message.get('To', ''))

                    # 解析主题
                    subject = self.decode_email_header(email_message.get('Subject', '(无主题)'))

                    # 解析日期
                    date_str = email_message.get('Date', '')
                    formatted_date = self.format_email_date(date_str)

                    # 获取邮件大小（估算）
                    size_str = self.estimate_email_size(email_message)

                    # 获取邮件内容预览
                    content_preview = self.get_email_preview(email_id, imap)

                    emails.append({
                        'id': email_id.decode(),
                        'sender': sender if folder_name != "Sent" else f"发送给: {to_addr}",
                        'subject': subject,
                        'date': formatted_date,
                        'size': size_str,
                        'unread': is_unread,
                        'preview': content_preview,
                        'email_message': email_message  # 保存完整邮件对象用于详细预览
                    })

                except Exception as e:
                    print(f"❌ 解析邮件 {email_id} 时出错: {e}")
                    continue

            imap.logout()
            print(f"✅ 成功获取 {len(emails)} 封邮件")
            return emails

        except imaplib.IMAP4.error as e:
            raise Exception(f"IMAP协议错误: {str(e)}")
        except Exception as e:
            raise Exception(f"连接邮件服务器失败: {str(e)}")

    def decode_email_header(self, header_value):
        """解码邮件头信息"""
        if not header_value:
            return ""

        try:
            from email.header import decode_header
            decoded_parts = decode_header(header_value)
            result = ""
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        result += part.decode(encoding)
                    else:
                        result += part.decode('utf-8', errors='ignore')
                else:
                    result += str(part)
            return result.strip()
        except Exception as e:
            print(f"解码邮件头失败: {e}")
            return str(header_value)

    def format_email_date(self, date_str):
        """格式化邮件日期"""
        try:
            if date_str:
                import email.utils
                from datetime import datetime
                parsed_date = email.utils.parsedate_to_datetime(date_str)

                # 转换为本地时间
                now = datetime.now(parsed_date.tzinfo)
                diff = now - parsed_date

                if diff.days == 0:
                    return parsed_date.strftime('%H:%M')
                elif diff.days == 1:
                    return "昨天 " + parsed_date.strftime('%H:%M')
                elif diff.days < 7:
                    return parsed_date.strftime('%m-%d %H:%M')
                else:
                    return parsed_date.strftime('%Y-%m-%d')
        except Exception as e:
            print(f"日期解析失败: {e}")

        return "未知日期"

    def estimate_email_size(self, email_message):
        """估算邮件大小"""
        try:
            # 估算邮件大小
            size = len(str(email_message))
            if size < 1024:
                return f"{size}B"
            elif size < 1024 * 1024:
                return f"{size/1024:.1f}KB"
            else:
                return f"{size/(1024*1024):.1f}MB"
        except:
            return "未知"

    def get_email_preview(self, email_id, imap):
        """获取邮件内容预览"""
        try:
            # 获取邮件正文的前200个字符作为预览
            status, msg_data = imap.fetch(email_id, '(BODY.PEEK[TEXT])')
            if status == 'OK' and msg_data[0]:
                content = msg_data[0][1]
                if content:
                    if isinstance(content, bytes):
                        preview = content.decode('utf-8', errors='ignore')[:200]
                    else:
                        preview = str(content)[:200]
                    return preview.replace('\n', ' ').replace('\r', ' ').strip()
        except:
            pass
        return ""



    def on_email_selected(self):
        """处理邮件选择事件"""
        current_row = self.email_table.currentRow()
        if current_row >= 0 and hasattr(self, 'current_emails') and current_row < len(self.current_emails):
            email_data = self.current_emails[current_row]

            # 显示详细的邮件内容
            self.display_email_details(email_data)

            # 如果是未读邮件，标记为已读
            if email_data.get('unread', False):
                self.mark_email_as_read(current_row, email_data)

    def display_email_details(self, email_data):
        """显示邮件详细内容"""
        try:
            sender = email_data.get('sender', '未知发件人')
            subject = email_data.get('subject', '(无主题)')
            date = email_data.get('date', '未知日期')
            size = email_data.get('size', '未知大小')
            preview = email_data.get('preview', '')

            # 获取完整邮件内容
            email_message = email_data.get('email_message')
            content = self.extract_email_content(email_message) if email_message else preview

            # 格式化显示
            email_details = f"""📧 邮件详情

👤 发件人: {sender}
📝 主题: {subject}
📅 日期: {date}
📊 大小: {size}

{'='*50}

{content}

{'='*50}

💡 提示: 右键点击邮件可进行回复、转发等操作"""

            self.email_details.setPlainText(email_details)

        except Exception as e:
            self.email_details.setPlainText(f"❌ 显示邮件详情时出错: {str(e)}")

    def extract_email_content(self, email_message):
        """提取邮件正文内容"""
        try:
            content = ""

            if email_message.is_multipart():
                # 多部分邮件
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition", ""))

                    # 跳过附件
                    if "attachment" in content_disposition:
                        continue

                    if content_type == "text/plain":
                        charset = part.get_content_charset() or 'utf-8'
                        try:
                            text = part.get_payload(decode=True).decode(charset, errors='ignore')
                            content += text + "\n"
                        except:
                            content += str(part.get_payload()) + "\n"
                    elif content_type == "text/html" and not content:
                        # 如果没有纯文本，尝试解析HTML
                        charset = part.get_content_charset() or 'utf-8'
                        try:
                            html = part.get_payload(decode=True).decode(charset, errors='ignore')
                            # 简单的HTML标签清理
                            import re
                            text = re.sub(r'<[^>]+>', '', html)
                            text = re.sub(r'\s+', ' ', text).strip()
                            content += text + "\n"
                        except:
                            pass
            else:
                # 单部分邮件
                content_type = email_message.get_content_type()
                if content_type == "text/plain":
                    charset = email_message.get_content_charset() or 'utf-8'
                    try:
                        content = email_message.get_payload(decode=True).decode(charset, errors='ignore')
                    except:
                        content = str(email_message.get_payload())
                elif content_type == "text/html":
                    charset = email_message.get_content_charset() or 'utf-8'
                    try:
                        html = email_message.get_payload(decode=True).decode(charset, errors='ignore')
                        # 简单的HTML标签清理
                        import re
                        text = re.sub(r'<[^>]+>', '', html)
                        text = re.sub(r'\s+', ' ', text).strip()
                        content = text
                    except:
                        content = str(email_message.get_payload())

            # 清理和格式化内容
            if content:
                content = content.strip()
                # 限制显示长度
                if len(content) > 2000:
                    content = content[:2000] + "\n\n... (内容过长，已截断)"
                return content
            else:
                return "📭 邮件内容为空或无法解析"

        except Exception as e:
            return f"❌ 解析邮件内容时出错: {str(e)}"

    def mark_email_as_read(self, row, email_data):
        """标记邮件为已读"""
        try:
            # 更新本地数据
            email_data['unread'] = False

            # 更新表格显示（移除粗体）
            for col in range(4):
                item = self.email_table.item(row, col)
                if item:
                    font = item.font()
                    font.setBold(False)
                    item.setFont(font)

            # TODO: 这里可以添加服务器端标记已读的逻辑
            print(f"📖 邮件已标记为已读: {email_data.get('subject', '')}")

        except Exception as e:
            print(f"标记邮件已读失败: {e}")

    def search_emails(self, text):
        """搜索邮件"""
        # 这里可以实现邮件搜索功能
        if text:
            print(f"搜索邮件: {text}")

    def show_email_config_dialog(self):
        """显示邮件配置对话框"""
        # 找到主窗口
        main_window = self
        while main_window.parent():
            main_window = main_window.parent()

        dialog = EmailConfigDialog(main_window)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 重新加载邮箱配置
            self.load_email_settings()
            self.refresh_mailbox_tree()

    def refresh_current_folder(self):
        """刷新当前文件夹"""
        if self.current_mailbox and self.current_folder:
            self.load_folder_emails(self.current_mailbox, self.current_folder)
        else:
            QMessageBox.information(self, "提示", "请先选择一个邮箱文件夹")

    def toggle_email_auto_refresh_ui(self):
        """切换邮件自动刷新状态（UI控制）"""
        # 获取主窗口实例
        main_window = self.get_main_window()
        if main_window:
            main_window.toggle_email_auto_refresh()

            # 更新按钮状态
            if main_window.email_auto_refresh_enabled:
                self.auto_refresh_email_btn.setText("⏸️ 暂停自动刷新")
                self.auto_refresh_email_btn.setToolTip("点击暂停邮件自动刷新")
            else:
                self.auto_refresh_email_btn.setText("▶️ 开启自动刷新")
                self.auto_refresh_email_btn.setToolTip("点击开启邮件自动刷新")

    def get_main_window(self):
        """获取主窗口实例"""
        parent = self.parent()
        while parent:
            if hasattr(parent, 'email_refresh_timer'):
                return parent
            parent = parent.parent()
        return None

    def compose_email(self):
        """撰写邮件"""
        # 找到主窗口
        main_window = self
        while main_window.parent():
            main_window = main_window.parent()

        dialog = ComposeEmailDialog(main_window)
        dialog.exec()

    def show_email_context_menu(self, position):
        """显示邮件右键菜单"""
        if self.email_table.itemAt(position) is None:
            return

        from PyQt6.QtWidgets import QMenu
        menu = QMenu(self)

        # 回复邮件
        reply_action = menu.addAction("↩️ 回复")
        reply_action.triggered.connect(self.reply_email)

        # 转发邮件
        forward_action = menu.addAction("➡️ 转发")
        forward_action.triggered.connect(self.forward_email)

        menu.addSeparator()

        # 标记为已读/未读
        mark_read_action = menu.addAction("✅ 标记为已读")
        mark_read_action.triggered.connect(self.mark_as_read)

        mark_unread_action = menu.addAction("📧 标记为未读")
        mark_unread_action.triggered.connect(self.mark_as_unread)

        menu.addSeparator()

        # 删除邮件
        delete_action = menu.addAction("🗑️ 删除")
        delete_action.triggered.connect(self.delete_email)

        # 显示菜单
        menu.exec(self.email_table.mapToGlobal(position))

    def reply_email(self):
        """回复邮件"""
        current_row = self.email_table.currentRow()
        if current_row >= 0:
            sender = self.email_table.item(current_row, 0).text()
            subject = self.email_table.item(current_row, 1).text()

            # 找到主窗口
            main_window = self
            while main_window.parent():
                main_window = main_window.parent()

            dialog = ComposeEmailDialog(main_window, reply_to=sender, subject=f"Re: {subject}")
            dialog.exec()

    def forward_email(self):
        """转发邮件"""
        current_row = self.email_table.currentRow()
        if current_row >= 0:
            subject = self.email_table.item(current_row, 1).text()

            # 找到主窗口
            main_window = self
            while main_window.parent():
                main_window = main_window.parent()

            dialog = ComposeEmailDialog(main_window, subject=f"Fwd: {subject}")
            dialog.exec()

    def mark_as_read(self):
        """标记为已读"""
        current_row = self.email_table.currentRow()
        if current_row >= 0:
            # 这里可以实现标记已读的逻辑
            print(f"标记邮件 {current_row} 为已读")

    def mark_as_unread(self):
        """标记为未读"""
        current_row = self.email_table.currentRow()
        if current_row >= 0:
            # 这里可以实现标记未读的逻辑
            print(f"标记邮件 {current_row} 为未读")

    def delete_email(self):
        """删除邮件"""
        current_row = self.email_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(
                self, "确认删除",
                "确定要删除这封邮件吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.email_table.removeRow(current_row)
                print(f"删除邮件 {current_row}")

    def show_mailbox_context_menu(self, position):
        """显示邮箱树右键菜单"""
        try:
            # 检查mailbox_tree是否仍然有效
            if not hasattr(self, 'mailbox_tree') or self.mailbox_tree is None:
                return

            item = self.mailbox_tree.itemAt(position)
            if not item:
                return

            data = item.data(0, Qt.ItemDataRole.UserRole)
            if not data or data['type'] != 'mailbox':
                return

            from PyQt6.QtWidgets import QMenu
            menu = QMenu(self)

            # 删除邮箱
            delete_action = menu.addAction("🗑️ 删除邮箱")
            delete_action.triggered.connect(lambda: self.delete_mailbox_from_tree(data['name']))

            # 刷新邮箱
            refresh_action = menu.addAction("🔄 刷新邮箱")
            refresh_action.triggered.connect(lambda: self.refresh_mailbox_folders(data['name']))

            # 显示菜单
            menu.exec(self.mailbox_tree.mapToGlobal(position))
        except RuntimeError as e:
            print(f"显示右键菜单时出现错误（对象已被删除）: {e}")
        except Exception as e:
            print(f"显示右键菜单时出现错误: {e}")

    def delete_mailbox_from_tree(self, mailbox_name):
        """从树中删除邮箱"""
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除邮箱 '{mailbox_name}' 吗？\n这将删除所有相关配置。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 从内存中删除
            if mailbox_name in self.mailboxes:
                del self.mailboxes[mailbox_name]

            # 从设置中删除
            self.delete_mailbox_from_settings(mailbox_name)

            # 刷新树
            self.refresh_mailbox_tree()

            QMessageBox.information(self, "删除成功", f"邮箱 '{mailbox_name}' 已删除")

    def delete_mailbox_from_settings(self, mailbox_name):
        """从设置中删除邮箱配置"""
        try:
            # 从邮箱配置中删除指定邮箱
            if mailbox_name in self.mailboxes:
                del self.mailboxes[mailbox_name]

                # 保存到JSON文件
                if self.save_email_settings():
                    print(f"已删除邮箱配置: {mailbox_name}")
                else:
                    print(f"删除邮箱配置失败: {mailbox_name}")
        except Exception as e:
            print(f"删除邮箱配置时出错: {e}")

    def refresh_mailbox_folders(self, mailbox_name):
        """刷新指定邮箱的文件夹"""
        if mailbox_name in self.mailboxes:
            print(f"🔄 刷新邮箱 '{mailbox_name}' 的文件夹...")
            # 重新加载文件夹数量
            self.load_folder_counts_async(mailbox_name)

            # 如果当前正在查看这个邮箱的文件夹，也刷新邮件列表
            if hasattr(self, 'current_mailbox') and self.current_mailbox == mailbox_name:
                if hasattr(self, 'current_folder') and self.current_folder:
                    self.load_folder_emails(self.current_mailbox, self.current_folder)


class ComposeEmailDialog(QDialog):
    """撰写邮件对话框"""

    def __init__(self, parent=None, reply_to="", subject="", content=""):
        super().__init__(parent)
        self.setWindowTitle("撰写邮件")
        self.setModal(True)
        self.resize(700, 500)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.FramelessWindowHint)

        # 设置窗口属性以支持透明背景和圆角
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        self.reply_to = reply_to
        self.subject = subject
        self.content = content

        self.setup_ui()
        self.center_on_parent()

    def setup_ui(self):
        """设置UI"""
        # 设置对话框背景透明
        self.setStyleSheet("QDialog { background-color: transparent; }")

        # 主容器
        main_container = QFrame(self)
        main_container.setObjectName("mainContainer")
        main_container.setStyleSheet("""
            QFrame#mainContainer {
                background-color: #FFFFFF;
                border-radius: 16px;
                border: 1px solid #D1D1D6;
            }
        """)

        # 主布局
        dialog_layout = QVBoxLayout(self)
        dialog_layout.setContentsMargins(0, 0, 0, 0)
        dialog_layout.addWidget(main_container)

        # 容器内布局
        layout = QVBoxLayout(main_container)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(20)

        # 标题栏
        title_layout = QHBoxLayout()

        title = QLabel("✍️ 撰写邮件")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title.setStyleSheet("color: #1C1C1E;")
        title_layout.addWidget(title)

        title_layout.addStretch()

        # 关闭按钮
        self.close_btn = QPushButton("✕")
        self.close_btn.setFixedSize(32, 32)
        self.close_btn.setToolTip("关闭")
        self.close_btn.clicked.connect(self.reject)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 16px;
                color: #8E8E93;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #FF3B30;
                color: white;
            }
            QPushButton:pressed {
                background-color: #D70015;
            }
        """)
        title_layout.addWidget(self.close_btn)

        layout.addLayout(title_layout)

        # 邮件表单
        form_layout = QVBoxLayout()
        form_layout.setSpacing(15)

        # 收件人
        to_layout = QHBoxLayout()
        to_label = QLabel("收件人:")
        to_label.setFixedWidth(60)
        to_label.setStyleSheet("font-weight: bold; color: #1C1C1E;")
        self.to_input = QLineEdit()
        self.to_input.setPlaceholderText("输入收件人邮箱地址...")
        if self.reply_to:
            self.to_input.setText(self.reply_to)
        to_layout.addWidget(to_label)
        to_layout.addWidget(self.to_input)
        form_layout.addLayout(to_layout)

        # 主题
        subject_layout = QHBoxLayout()
        subject_label = QLabel("主题:")
        subject_label.setFixedWidth(60)
        subject_label.setStyleSheet("font-weight: bold; color: #1C1C1E;")
        self.subject_input = QLineEdit()
        self.subject_input.setPlaceholderText("输入邮件主题...")
        if self.subject:
            self.subject_input.setText(self.subject)
        subject_layout.addWidget(subject_label)
        subject_layout.addWidget(self.subject_input)
        form_layout.addLayout(subject_layout)

        # 邮件内容
        content_label = QLabel("内容:")
        content_label.setStyleSheet("font-weight: bold; color: #1C1C1E; margin-bottom: 5px;")
        form_layout.addWidget(content_label)

        self.content_edit = QTextEdit()
        self.content_edit.setPlaceholderText("输入邮件内容...")
        self.content_edit.setMinimumHeight(200)
        if self.content:
            self.content_edit.setPlainText(self.content)
        form_layout.addWidget(self.content_edit)

        layout.addLayout(form_layout)

        # 底部按钮
        button_layout = QHBoxLayout()

        # 附件按钮
        attach_btn = AnimatedButton("📎 添加附件")
        attach_btn.setProperty("buttonStyle", "secondary")
        attach_btn.clicked.connect(self.add_attachment)
        button_layout.addWidget(attach_btn)

        button_layout.addStretch()

        # 取消按钮
        cancel_btn = AnimatedButton("取消")
        cancel_btn.setProperty("buttonStyle", "secondary")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        # 发送按钮
        send_btn = AnimatedButton("📤 发送")
        send_btn.setProperty("buttonStyle", "primary")
        send_btn.clicked.connect(self.send_email)
        send_btn.setStyleSheet("""
            QPushButton[buttonStyle="primary"] {
                color: #1C1C1E !important;
            }
        """)
        button_layout.addWidget(send_btn)

        layout.addLayout(button_layout)

        # 应用样式
        self.setStyleSheet(self.styleSheet() +
                          AppleStyleSheet.get_main_style() +
                          AppleStyleSheet.get_card_style() +
                          AppleStyleSheet.get_primary_button_style() +
                          AppleStyleSheet.get_secondary_button_style())

    def center_on_parent(self):
        """在父窗口中居中显示"""
        if self.parent():
            parent_geometry = self.parent().geometry()
            x = parent_geometry.x() + (parent_geometry.width() - self.width()) // 2
            y = parent_geometry.y() + (parent_geometry.height() - self.height()) // 2
            self.move(x, y)
        else:
            # 如果没有父窗口，在屏幕中央显示
            from PyQt6.QtWidgets import QApplication
            screen = QApplication.primaryScreen().availableGeometry()
            x = (screen.width() - self.width()) // 2
            y = (screen.height() - self.height()) // 2
            self.move(x, y)

    def add_attachment(self):
        """添加附件"""
        from PyQt6.QtWidgets import QFileDialog
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择附件", "", "所有文件 (*.*)"
        )
        if file_path:
            QMessageBox.information(self, "附件", f"已添加附件: {file_path}")

    def send_email(self):
        """发送邮件"""
        to = self.to_input.text().strip()
        subject = self.subject_input.text().strip()
        content = self.content_edit.toPlainText().strip()

        if not to:
            QMessageBox.warning(self, "发送失败", "请输入收件人邮箱地址")
            return

        if not subject:
            QMessageBox.warning(self, "发送失败", "请输入邮件主题")
            return

        if not content:
            QMessageBox.warning(self, "发送失败", "请输入邮件内容")
            return

        # 获取发件邮箱配置
        email_widget = self.get_email_widget()
        if not email_widget or not email_widget.mailboxes:
            QMessageBox.warning(self, "配置错误", "请先配置发件邮箱")
            return

        # 使用第一个配置的邮箱作为发件邮箱
        mailbox_name = list(email_widget.mailboxes.keys())[0]
        config = email_widget.mailboxes[mailbox_name]

        # 显示发送进度
        from PyQt6.QtWidgets import QProgressDialog
        progress_dialog = QProgressDialog("正在发送邮件...", "取消", 0, 0, self)
        progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
        progress_dialog.show()

        try:
            self.send_email_via_smtp(config, to, subject, content)
            progress_dialog.close()

            # 发送成功后刷新发件箱
            self.refresh_sent_folder(email_widget, mailbox_name)

            QMessageBox.information(self, "发送成功", f"✅ 邮件已成功发送给: {to}")
            self.accept()
        except Exception as e:
            progress_dialog.close()
            QMessageBox.critical(self, "发送失败", f"❌ 邮件发送失败:\n\n{str(e)}\n\n请检查网络连接和邮箱配置。")

    def refresh_sent_folder(self, email_widget, mailbox_name):
        """刷新发件箱"""
        try:
            # 刷新文件夹数量
            if hasattr(email_widget, 'load_folder_counts_async'):
                email_widget.load_folder_counts_async(mailbox_name)

            # 如果当前正在查看发件箱，刷新邮件列表
            if (hasattr(email_widget, 'current_mailbox') and
                hasattr(email_widget, 'current_folder') and
                email_widget.current_mailbox == mailbox_name and
                email_widget.current_folder == "Sent"):
                email_widget.load_folder_emails(mailbox_name, "Sent")

        except Exception as e:
            print(f"刷新发件箱失败: {e}")

    def get_email_widget(self):
        """获取EmailWidget实例"""
        parent = self.parent()
        while parent:
            if hasattr(parent, 'email_widget'):
                return parent.email_widget
            parent = parent.parent()
        return None

    def send_email_via_smtp(self, config, to, subject, content):
        """通过SMTP发送邮件"""
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        from email.header import Header

        print(f"📤 准备发送邮件: {config['email']} -> {to}")

        # 创建邮件对象
        msg = MIMEMultipart()
        msg['From'] = config['email']
        msg['To'] = to
        msg['Subject'] = Header(subject, 'utf-8')

        # 添加邮件正文
        msg.attach(MIMEText(content, 'plain', 'utf-8'))

        print(f"🔗 连接SMTP服务器: {config['smtp_server']}:{config['smtp_port']}")

        # 连接SMTP服务器
        if config['smtp_ssl'] and config['smtp_port'] == 465:
            # SSL连接
            server = smtplib.SMTP_SSL(config['smtp_server'], config['smtp_port'])
        else:
            # 普通连接或STARTTLS
            server = smtplib.SMTP(config['smtp_server'], config['smtp_port'])
            if config['smtp_ssl'] or config['smtp_port'] == 587:  # STARTTLS
                server.starttls()

        print(f"🔐 登录邮箱: {config['email']}")
        # 登录
        server.login(config['email'], config['password'])

        print("📧 发送邮件...")
        # 发送邮件
        text = msg.as_string()
        server.sendmail(config['email'], [to], text)
        server.quit()

        print(f"✅ 邮件发送成功: {config['email']} -> {to}")


class EmailConfigDialog(QDialog):
    """邮件配置对话框 - 弹窗卡片样式"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("邮箱配置")
        self.setModal(True)
        self.resize(600, 700)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.FramelessWindowHint)

        # 设置窗口属性以支持透明背景和圆角
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        self.setup_ui()
        self.load_mailboxes()

        # 在UI设置完成后居中显示
        self.center_on_parent()

    def get_email_widget(self):
        """获取EmailWidget实例"""
        if hasattr(self.parent(), 'email_widget'):
            return self.parent().email_widget
        return None

    def refresh_parent_widget(self):
        """延迟刷新父窗口组件"""
        try:
            # 通知父窗口刷新邮箱列表
            if hasattr(self.parent(), 'refresh_email_widget'):
                self.parent().refresh_email_widget()
        except Exception as e:
            print(f"刷新父窗口时出现错误: {e}")

    def setup_ui(self):
        """设置UI"""
        # 设置对话框背景透明
        self.setStyleSheet("QDialog { background-color: transparent; }")

        # 主容器
        main_container = QFrame(self)
        main_container.setObjectName("mainContainer")
        main_container.setStyleSheet("""
            QFrame#mainContainer {
                background-color: #FFFFFF;
                border-radius: 16px;
                border: 1px solid #D1D1D6;
            }
        """)

        # 主布局
        dialog_layout = QVBoxLayout(self)
        dialog_layout.setContentsMargins(0, 0, 0, 0)
        dialog_layout.addWidget(main_container)

        # 容器内布局
        layout = QVBoxLayout(main_container)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(20)

        # 标题
        title = QLabel("📧 邮箱配置")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title.setStyleSheet("color: #1C1C1E; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # 邮箱列表和配置区域
        content_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：邮箱列表
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 10, 0)

        mailbox_label = QLabel("📮 邮箱列表")
        mailbox_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 5px;")
        left_layout.addWidget(mailbox_label)

        self.mailbox_list = QListWidget()
        self.mailbox_list.setMaximumWidth(200)
        self.mailbox_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #D1D1D6;
                border-radius: 8px;
                background-color: #FFFFFF;
                font-size: 13px;
            }
            QListWidget::item {
                padding: 8px 12px;
                border-bottom: 1px solid #F2F2F7;
            }
            QListWidget::item:selected {
                background-color: #007AFF;
                color: white;
            }
        """)
        self.mailbox_list.currentItemChanged.connect(self.on_mailbox_selected)
        left_layout.addWidget(self.mailbox_list)

        # 添加/删除邮箱按钮
        mailbox_buttons = QHBoxLayout()

        self.add_mailbox_btn = QPushButton("添加")
        self.add_mailbox_btn.setFixedSize(50, 32)
        self.add_mailbox_btn.setToolTip("添加邮箱")
        self.add_mailbox_btn.clicked.connect(self.add_mailbox)
        self.add_mailbox_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFFFFF, stop:1 #F8F9FA);
                border: 1px solid rgba(0, 122, 255, 0.3);
                border-radius: 8px;
                color: #007AFF;
                font-size: 12px;
                font-weight: 500;
                padding: 4px 8px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F8F9FA, stop:1 #E9ECEF);
                border-color: rgba(0, 122, 255, 0.5);
                color: #0056CC;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E9ECEF, stop:1 #DEE2E6);
                border-color: rgba(0, 122, 255, 0.7);
                color: #003D82;
            }
        """)



        self.remove_mailbox_btn = QPushButton("删除")
        self.remove_mailbox_btn.setFixedSize(50, 32)
        self.remove_mailbox_btn.setToolTip("删除邮箱")
        self.remove_mailbox_btn.clicked.connect(self.remove_mailbox)
        self.remove_mailbox_btn.setEnabled(False)
        self.remove_mailbox_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFFFFF, stop:1 #F8F9FA);
                border: 1px solid rgba(255, 59, 48, 0.3);
                border-radius: 8px;
                color: #FF3B30;
                font-size: 12px;
                font-weight: 500;
                padding: 4px 8px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F8F9FA, stop:1 #E9ECEF);
                border-color: rgba(255, 59, 48, 0.5);
                color: #D70015;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E9ECEF, stop:1 #DEE2E6);
                border-color: rgba(255, 59, 48, 0.7);
                color: #B30000;
            }
            QPushButton:disabled {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F8F9FA, stop:1 #E9ECEF);
                border-color: rgba(199, 199, 204, 0.3);
                color: #C7C7CC;
            }
        """)



        mailbox_buttons.addWidget(self.add_mailbox_btn)
        mailbox_buttons.addWidget(self.remove_mailbox_btn)
        mailbox_buttons.addStretch()
        left_layout.addLayout(mailbox_buttons)

        # 右侧：邮箱配置表单
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(10, 0, 0, 0)

        config_label = QLabel("邮箱配置")
        config_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 5px;")
        right_layout.addWidget(config_label)

        # 配置表单
        self.setup_config_form(right_layout)

        content_splitter.addWidget(left_panel)
        content_splitter.addWidget(right_panel)
        content_splitter.setSizes([200, 400])
        layout.addWidget(content_splitter)

        # 底部按钮
        button_layout = QHBoxLayout()

        self.test_btn = AnimatedButton("🔗 测试连接")
        self.test_btn.setProperty("buttonStyle", "secondary")
        self.test_btn.clicked.connect(self.test_connection)
        self.test_btn.setEnabled(False)

        self.cancel_btn = AnimatedButton("取消")
        self.cancel_btn.setProperty("buttonStyle", "secondary")
        self.cancel_btn.clicked.connect(self.reject)

        self.save_btn = AnimatedButton("保存配置")
        self.save_btn.setProperty("buttonStyle", "primary")
        self.save_btn.clicked.connect(self.save_config)

        button_layout.addWidget(self.test_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.save_btn)
        layout.addLayout(button_layout)

        # 应用样式
        self.setStyleSheet(AppleStyleSheet.get_main_style() +
                          AppleStyleSheet.get_card_style() +
                          AppleStyleSheet.get_primary_button_style() +
                          AppleStyleSheet.get_secondary_button_style())

    def setup_config_form(self, parent_layout):
        """设置配置表单"""
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #D1D1D6;
                border-radius: 8px;
                background-color: #FFFFFF;
            }
        """)

        form_widget = QWidget()
        form_layout = QVBoxLayout(form_widget)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(15, 15, 15, 15)

        # 基本信息
        basic_group = QGroupBox("📧 基本信息")
        basic_layout = QFormLayout(basic_group)
        basic_layout.setSpacing(10)

        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("例如: 工作邮箱")
        # 连接名称输入框的文本变化信号，实时更新列表项显示
        self.name_input.textChanged.connect(lambda text: self.update_current_mailbox_name(text))
        basic_layout.addRow("邮箱名称:", self.name_input)

        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("<EMAIL>")
        basic_layout.addRow("邮箱地址:", self.email_input)

        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setPlaceholderText("邮箱密码或应用专用密码")
        basic_layout.addRow("密码:", self.password_input)

        form_layout.addWidget(basic_group)

        # IMAP配置
        imap_group = QGroupBox("📥 IMAP配置 (接收邮件)")
        imap_layout = QFormLayout(imap_group)
        imap_layout.setSpacing(10)

        self.imap_server_input = QLineEdit()
        self.imap_server_input.setPlaceholderText("例如: imap.gmail.com")
        imap_layout.addRow("IMAP服务器:", self.imap_server_input)

        self.imap_port_input = QSpinBox()
        self.imap_port_input.setRange(1, 65535)
        self.imap_port_input.setValue(993)
        imap_layout.addRow("IMAP端口:", self.imap_port_input)

        self.imap_ssl_check = QCheckBox("使用SSL/TLS")
        self.imap_ssl_check.setChecked(True)
        imap_layout.addRow("安全连接:", self.imap_ssl_check)

        form_layout.addWidget(imap_group)

        # SMTP配置
        smtp_group = QGroupBox("📤 SMTP配置 (发送邮件)")
        smtp_layout = QFormLayout(smtp_group)
        smtp_layout.setSpacing(10)

        self.smtp_server_input = QLineEdit()
        self.smtp_server_input.setPlaceholderText("例如: smtp.gmail.com")
        smtp_layout.addRow("SMTP服务器:", self.smtp_server_input)

        self.smtp_port_input = QSpinBox()
        self.smtp_port_input.setRange(1, 65535)
        self.smtp_port_input.setValue(587)
        smtp_layout.addRow("SMTP端口:", self.smtp_port_input)

        self.smtp_ssl_check = QCheckBox("使用STARTTLS")
        self.smtp_ssl_check.setChecked(True)
        smtp_layout.addRow("安全连接:", self.smtp_ssl_check)

        form_layout.addWidget(smtp_group)
        form_layout.addStretch()

        scroll_area.setWidget(form_widget)
        parent_layout.addWidget(scroll_area)

    def center_on_parent(self):
        """在父窗口中居中显示"""
        if self.parent():
            parent_geometry = self.parent().geometry()
            x = parent_geometry.x() + (parent_geometry.width() - self.width()) // 2
            y = parent_geometry.y() + (parent_geometry.height() - self.height()) // 2
            self.move(x, y)
        else:
            # 如果没有父窗口，在屏幕中央显示
            from PyQt6.QtWidgets import QApplication
            screen = QApplication.primaryScreen().availableGeometry()
            x = (screen.width() - self.width()) // 2
            y = (screen.height() - self.height()) // 2
            self.move(x, y)

    def load_mailboxes(self):
        """加载邮箱列表"""
        self.mailbox_list.clear()

        # 从EmailWidget获取邮箱配置
        email_widget = self.get_email_widget()
        if email_widget:
            mailboxes = email_widget.mailboxes
            for i, mailbox_name in enumerate(mailboxes.keys()):
                item = QListWidgetItem(mailbox_name)
                item.setData(Qt.ItemDataRole.UserRole, i)
                self.mailbox_list.addItem(item)

        # 如果有邮箱，选择第一个
        if self.mailbox_list.count() > 0:
            self.mailbox_list.setCurrentRow(0)

    def on_mailbox_selected(self, current, previous):
        """邮箱选择改变"""
        if current:
            mailbox_index = current.data(Qt.ItemDataRole.UserRole)
            self.load_mailbox_config(mailbox_index)
            self.remove_mailbox_btn.setEnabled(True)
            self.test_btn.setEnabled(True)
        else:
            self.clear_config_form()
            self.remove_mailbox_btn.setEnabled(False)
            self.test_btn.setEnabled(False)

    def load_mailbox_config(self, index):
        """加载邮箱配置"""
        # 获取当前选中的邮箱项
        current_item = self.mailbox_list.currentItem()
        if not current_item:
            return

        mailbox_name = current_item.text()

        # 先尝试从父窗口的email_widget的mailboxes中加载
        email_widget = self.get_email_widget()
        if email_widget and mailbox_name in email_widget.mailboxes:
            config = email_widget.mailboxes[mailbox_name]

            self.name_input.setText(config.get('name', ''))
            self.email_input.setText(config.get('email', ''))
            self.password_input.setText(config.get('password', ''))
            self.imap_server_input.setText(config.get('imap_server', ''))
            self.imap_port_input.setValue(config.get('imap_port', 993))
            self.imap_ssl_check.setChecked(config.get('imap_ssl', True))
            self.smtp_server_input.setText(config.get('smtp_server', ''))
            self.smtp_port_input.setValue(config.get('smtp_port', 587))
            self.smtp_ssl_check.setChecked(config.get('smtp_ssl', True))
        else:
            # 如果是新邮箱，设置默认值
            self.name_input.setText(mailbox_name)
            self.email_input.clear()
            self.password_input.clear()
            self.imap_server_input.clear()
            self.imap_port_input.setValue(993)
            self.imap_ssl_check.setChecked(True)
            self.smtp_server_input.clear()
            self.smtp_port_input.setValue(587)
            self.smtp_ssl_check.setChecked(True)

    def clear_config_form(self):
        """清空配置表单"""
        self.name_input.clear()
        self.email_input.clear()
        self.password_input.clear()
        self.imap_server_input.clear()
        self.imap_port_input.setValue(993)
        self.imap_ssl_check.setChecked(True)
        self.smtp_server_input.clear()
        self.smtp_port_input.setValue(587)
        self.smtp_ssl_check.setChecked(True)

    def add_mailbox(self):
        """添加新邮箱"""
        # 如果当前表单有内容，先保存当前邮箱的配置
        if self.mailbox_list.currentItem():
            current_item = self.mailbox_list.currentItem()
            if self.name_input.text().strip():
                current_item.setText(self.name_input.text().strip())

        # 创建新邮箱 - 不自动生成名称，让用户自己输入
        mailbox_name = "新邮箱"  # 使用通用名称，用户可以修改
        item = QListWidgetItem(mailbox_name)
        item.setData(Qt.ItemDataRole.UserRole, self.mailbox_list.count())
        self.mailbox_list.addItem(item)
        self.mailbox_list.setCurrentItem(item)

        # 清空表单供用户填写
        self.clear_config_form()
        self.name_input.setText(mailbox_name)
        self.name_input.selectAll()  # 选中文本，方便用户直接输入新名称

        # 信号已在setup_config_form中连接，无需重复连接

        # 启用删除和测试按钮
        self.remove_mailbox_btn.setEnabled(True)
        self.test_btn.setEnabled(True)

    def update_current_mailbox_name(self, text):
        """实时更新当前选中邮箱的显示名称"""
        current_item = self.mailbox_list.currentItem()
        if current_item and text.strip():
            current_item.setText(text.strip())

    def remove_mailbox(self):
        """删除选中的邮箱"""
        current_item = self.mailbox_list.currentItem()
        if not current_item:
            return

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除邮箱 '{current_item.text()}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            row = self.mailbox_list.row(current_item)
            self.mailbox_list.takeItem(row)

            # 重新编号剩余邮箱
            for i in range(self.mailbox_list.count()):
                item = self.mailbox_list.item(i)
                item.setData(Qt.ItemDataRole.UserRole, i)

    def test_connection(self):
        """测试邮件连接"""
        if not self.email_input.text() or not self.password_input.text():
            QMessageBox.warning(self, "配置不完整", "请先填写邮箱地址和密码")
            return

        if not self.imap_server_input.text() or not self.smtp_server_input.text():
            QMessageBox.warning(self, "配置不完整", "请先填写IMAP和SMTP服务器地址")
            return

        # 禁用测试按钮，防止重复点击
        self.test_btn.setEnabled(False)
        original_text = self.test_btn.text()
        self.test_btn.setText("🔄 测试中...")

        try:
            # 实际的IMAP/SMTP连接测试
            success = self.perform_email_test()

            if success:
                # 测试成功 - 更新按钮样式为绿色
                self.test_btn.setText("✅ 测试成功")
                self.test_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #34C759;
                        color: white;
                        border: 2px solid #34C759;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #30B050;
                        border-color: #30B050;
                        transform: scale(1.05);
                    }
                """)

                QMessageBox.information(self, "连接测试成功",
                    f"""✅ 邮件服务器连接测试成功！

📧 邮箱地址: {self.email_input.text()}
📥 IMAP服务器: {self.imap_server_input.text()}:{self.imap_port_input.value()}
📤 SMTP服务器: {self.smtp_server_input.text()}:{self.smtp_port_input.value()}
🔒 SSL/TLS: IMAP({'启用' if self.imap_ssl_check.isChecked() else '禁用'}), SMTP({'启用' if self.smtp_ssl_check.isChecked() else '禁用'})

配置验证通过，可以正常收发邮件。""")
            else:
                # 测试失败 - 恢复原始样式
                self.test_btn.setText(original_text)
                self.test_btn.setStyleSheet("")

        except Exception as e:
            # 测试出错 - 恢复原始样式
            self.test_btn.setText(original_text)
            self.test_btn.setStyleSheet("")
            QMessageBox.critical(self, "测试失败", f"连接测试出现错误:\n{str(e)}")

        finally:
            # 重新启用测试按钮
            self.test_btn.setEnabled(True)

    def perform_email_test(self):
        """执行实际的邮件连接测试"""
        try:
            import imaplib
            import smtplib

            # 测试IMAP连接
            if self.imap_ssl_check.isChecked():
                imap = imaplib.IMAP4_SSL(self.imap_server_input.text(), self.imap_port_input.value())
            else:
                imap = imaplib.IMAP4(self.imap_server_input.text(), self.imap_port_input.value())

            imap.login(self.email_input.text(), self.password_input.text())
            imap.logout()

            # 测试SMTP连接
            if self.smtp_ssl_check.isChecked():
                smtp = smtplib.SMTP_SSL(self.smtp_server_input.text(), self.smtp_port_input.value())
            else:
                smtp = smtplib.SMTP(self.smtp_server_input.text(), self.smtp_port_input.value())
                if self.smtp_port_input.value() == 587:  # TLS端口
                    smtp.starttls()

            smtp.login(self.email_input.text(), self.password_input.text())
            smtp.quit()

            return True

        except Exception as e:
            QMessageBox.critical(self, "连接测试失败",
                f"""❌ 邮件服务器连接失败

错误详情: {str(e)}

请检查以下配置：
• 邮箱地址和密码是否正确
• IMAP/SMTP服务器地址和端口是否正确
• SSL/TLS设置是否匹配服务器要求
• 网络连接是否正常
• 邮箱是否开启了IMAP/SMTP服务""")
            return False

    def save_config(self):
        """保存配置"""
        try:
            # 更新当前选中邮箱的配置
            current_item = self.mailbox_list.currentItem()
            if current_item:
                mailbox_name = self.name_input.text().strip() or "新邮箱"
                current_item.setText(mailbox_name)

                # 更新邮箱配置
                mailbox_config = {
                    'name': mailbox_name,
                    'email': self.email_input.text().strip(),
                    'password': self.password_input.text().strip(),
                    'imap_server': self.imap_server_input.text().strip(),
                    'imap_port': self.imap_port_input.value(),
                    'imap_ssl': self.imap_ssl_check.isChecked(),
                    'smtp_server': self.smtp_server_input.text().strip(),
                    'smtp_port': self.smtp_port_input.value(),
                    'smtp_ssl': self.smtp_ssl_check.isChecked(),
                }

                # 获取EmailWidget实例
                email_widget = self.get_email_widget()
                if email_widget:
                    # 检查是否是重命名的邮箱（原来的名称和新名称不同）
                    original_name = None
                    for i in range(self.mailbox_list.count()):
                        item = self.mailbox_list.item(i)
                        if item == current_item:
                            # 查找原始名称（如果存在于mailboxes中但与当前名称不同）
                            for name in email_widget.mailboxes.keys():
                                if name != mailbox_name and name not in [self.mailbox_list.item(j).text() for j in range(self.mailbox_list.count()) if j != i]:
                                    original_name = name
                                    break
                            break

                    # 如果是重命名，删除旧名称的配置
                    if original_name and original_name in email_widget.mailboxes:
                        del email_widget.mailboxes[original_name]

                    # 保存新的或更新的邮箱配置
                    email_widget.mailboxes[mailbox_name] = mailbox_config

            # 保存到JSON文件
            email_widget = self.get_email_widget()
            if email_widget and email_widget.save_email_settings():
                QMessageBox.information(self, "保存成功", "邮箱配置已保存到mail.json文件")

                # 先关闭对话框，然后再刷新父窗口
                self.accept()

                # 使用QTimer延迟刷新，确保对话框完全关闭后再刷新
                from PyQt6.QtCore import QTimer
                QTimer.singleShot(100, lambda: self.refresh_parent_widget())
            else:
                QMessageBox.warning(self, "保存失败", "保存邮箱配置时出现错误，请检查文件权限")

        except Exception as e:
            QMessageBox.critical(self, "保存错误", f"保存配置时发生错误:\n{str(e)}\n\n请检查配置信息是否正确")
            print(f"保存邮箱配置错误: {e}")
            import traceback
            traceback.print_exc()


class SettingsDialog(QDialog):
    """设置对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        # 从设置中获取应用标题
        settings = QSettings("YFCompany", "YFAIChat")
        app_title = settings.value("app_title", "YF AI Chat - 智能客服管理系统")
        self.setWindowTitle(f"{app_title} - 设置")
        self.setFixedSize(500, 600)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.FramelessWindowHint)

        # 设置窗口属性以支持透明背景和圆角
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        self.setup_ui()
        self.load_settings()

        # 居中显示
        self.center_on_parent()

    def setup_ui(self):
        """设置界面"""
        # 设置对话框背景透明
        self.setStyleSheet("QDialog { background-color: transparent; }")

        # 主容器
        main_container = QFrame(self)
        main_container.setObjectName("mainContainer")
        main_container.setStyleSheet("""
            QFrame#mainContainer {
                background-color: #FFFFFF;
                border-radius: 16px;
                border: 1px solid #D1D1D6;
            }
        """)

        # 主布局
        dialog_layout = QVBoxLayout(self)
        dialog_layout.setContentsMargins(0, 0, 0, 0)
        dialog_layout.addWidget(main_container)

        # 容器内布局
        layout = QVBoxLayout(main_container)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(20)

        # 标题
        title = QLabel("应用设置")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title.setStyleSheet("color: #1C1C1E; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # 设置表单
        form_layout = QFormLayout()
        form_layout.setSpacing(15)

        # 应用标题设置
        self.app_title_input = QLineEdit()
        self.app_title_input.setPlaceholderText("输入应用标题...")
        form_layout.addRow("应用标题:", self.app_title_input)

        # 主题设置
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["Apple风格", "深色模式", "经典模式"])
        form_layout.addRow("界面主题:", self.theme_combo)

        # 通知设置
        self.notification_check = QCheckBox("启用桌面通知")
        form_layout.addRow("通知设置:", self.notification_check)

        # 自动刷新间隔
        self.refresh_interval_spin = QSpinBox()
        self.refresh_interval_spin.setRange(1, 60)
        self.refresh_interval_spin.setSuffix(" 秒")
        form_layout.addRow("自动刷新间隔:", self.refresh_interval_spin)

        # 最大会话数
        self.max_sessions_spin = QSpinBox()
        self.max_sessions_spin.setRange(10, 1000)
        form_layout.addRow("最大显示会话数:", self.max_sessions_spin)

        layout.addLayout(form_layout)

        # 服务器配置信息区域
        config_group = QGroupBox("服务器配置信息")
        config_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #D1D1D6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        config_layout = QVBoxLayout(config_group)

        # 配置信息标签
        self.config_info_label = QLabel("正在加载配置信息...")
        self.config_info_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                padding: 10px;
                background-color: #F8F9FA;
                border-radius: 6px;
                font-family: 'Consolas', 'Monaco', monospace;
            }
        """)
        self.config_info_label.setWordWrap(True)
        config_layout.addWidget(self.config_info_label)

        layout.addWidget(config_group)
        layout.addStretch()

        # 按钮区域
        button_layout = QHBoxLayout()

        self.cancel_btn = AnimatedButton("取消")
        self.cancel_btn.setProperty("buttonStyle", "secondary")
        self.cancel_btn.clicked.connect(self.reject)

        self.save_btn = AnimatedButton("保存设置")
        self.save_btn.setProperty("buttonStyle", "primary")
        self.save_btn.clicked.connect(self.save_settings)

        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.save_btn)

        layout.addLayout(button_layout)

        # 应用样式
        self.setStyleSheet(AppleStyleSheet.get_main_style() +
                          AppleStyleSheet.get_card_style() +
                          AppleStyleSheet.get_primary_button_style() +
                          AppleStyleSheet.get_secondary_button_style())

    def load_settings(self):
        """加载设置"""
        settings = QSettings("YFCompany", "YFAIChat")

        self.app_title_input.setText(
            settings.value("app_title", "YF AI Chat - 智能客服管理系统")
        )

        theme = settings.value("theme", "Apple风格")
        index = self.theme_combo.findText(theme)
        if index >= 0:
            self.theme_combo.setCurrentIndex(index)

        self.notification_check.setChecked(
            settings.value("notifications_enabled", True, type=bool)
        )

        self.refresh_interval_spin.setValue(
            settings.value("refresh_interval", 5, type=int)
        )

        self.max_sessions_spin.setValue(
            settings.value("max_sessions", 100, type=int)
        )

        # 加载服务器配置信息
        self.load_server_config()

    def load_server_config(self):
        """加载服务器配置信息"""
        try:
            # 获取主窗口的API配置
            main_window = self.parent()
            if not main_window or not hasattr(main_window, 'api_base_url'):
                self.config_info_label.setText("❌ 无法获取服务器连接信息")
                return

            import requests
            response = requests.get(
                f"{main_window.api_base_url}/api/admin/config",
                headers=main_window.api_headers,
                timeout=5
            )

            if response.status_code == 200:
                config = response.json()
                config_text = f"""✅ 服务器配置信息：

🕐 管理员控制超时时间: {config.get('admin_control_timeout_minutes', 'N/A')} 分钟
⏱️ 超时检查间隔: {config.get('admin_timeout_check_interval_minutes', 'N/A')} 分钟
🌍 服务器时区: {config.get('timezone', 'N/A')}
📝 日志级别: {config.get('log_level', 'N/A')}

💡 提示: 管理员接管会话后，如果在 {config.get('admin_control_timeout_minutes', 15)} 分钟内没有回复，
系统将自动释放控制权给AI继续服务。"""

                self.config_info_label.setText(config_text)
            else:
                self.config_info_label.setText(f"❌ 获取配置失败: HTTP {response.status_code}")

        except requests.RequestException as e:
            self.config_info_label.setText(f"❌ 连接服务器失败: {str(e)}")
        except Exception as e:
            self.config_info_label.setText(f"❌ 加载配置出错: {str(e)}")

    def save_settings(self):
        """保存设置"""
        settings = QSettings("YFCompany", "YFAIChat")

        settings.setValue("app_title", self.app_title_input.text())
        settings.setValue("theme", self.theme_combo.currentText())
        settings.setValue("notifications_enabled", self.notification_check.isChecked())
        settings.setValue("refresh_interval", self.refresh_interval_spin.value())
        settings.setValue("max_sessions", self.max_sessions_spin.value())

        self.accept()

    def center_on_parent(self):
        """在父窗口中居中显示"""
        if self.parent():
            parent_geometry = self.parent().geometry()
            x = parent_geometry.x() + (parent_geometry.width() - self.width()) // 2
            y = parent_geometry.y() + (parent_geometry.height() - self.height()) // 2
            self.move(x, y)
        else:
            # 如果没有父窗口，在屏幕中央显示
            from PyQt6.QtWidgets import QApplication
            screen = QApplication.primaryScreen().availableGeometry()
            x = (screen.width() - self.width()) // 2
            y = (screen.height() - self.height()) // 2
            self.move(x, y)


if __name__ == "__main__":
    main()
