# Client Configuration
# 服务器配置 - 根据实际环境选择合适的配置

# 服务器地址配置
# 本地开发: localhost
# 局域网连接: ************* (替换为实际的局域网IP)
# 远程连接: chat.22668.xyz
SERVER_HOST=localhost

# 服务器端口
SERVER_PORT=8000

# 管理员Token (从后端.env文件中的ADMIN_TOKEN复制)
ADMIN_TOKEN=admin_secure_token_here

# SSL/HTTPS配置
# 本地和局域网连接: false (使用HTTP，更快更稳定)
# 远程连接: true (使用HTTPS，更安全)
USE_HTTPS=false

# 配置示例:
# ==========================================
# 本地开发环境:
# SERVER_HOST=localhost
# USE_HTTPS=false
#
# 局域网环境 (例如办公室内网):
# SERVER_HOST=*************
# USE_HTTPS=false
#
# 远程生产环境:
# SERVER_HOST=chat.22668.xyz
# USE_HTTPS=true
# ==========================================

# Admin Credentials (default)
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin

# UI Settings
WINDOW_WIDTH=1200
WINDOW_HEIGHT=800
REFRESH_INTERVAL=2000
