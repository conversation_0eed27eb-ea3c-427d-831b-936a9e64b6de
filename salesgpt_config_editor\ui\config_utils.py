"""
配置工具模块
为UI提供简化的配置管理功能
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, List


class UIConfigManager:
    """UI专用的配置管理器"""
    
    def __init__(self):
        self.config_data = {}
        self.config_path = None
        self.is_modified = False
    
    def load_config_file(self, file_path: str) -> bool:
        """加载配置文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.config_data = json.load(f)
            self.config_path = file_path
            self.is_modified = False
            return True
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return False
    
    def save_config_file(self, file_path: str = None) -> bool:
        """保存配置文件"""
        try:
            save_path = file_path or self.config_path
            if not save_path:
                return False
            
            # 确保目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=2)
            
            self.config_path = save_path
            self.is_modified = False
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get_config_data(self) -> Dict[str, Any]:
        """获取配置数据"""
        return self.config_data
    
    def set_config_data(self, data: Dict[str, Any]):
        """设置配置数据"""
        self.config_data = data
        self.is_modified = True
    
    def validate_config(self) -> List[str]:
        """基本配置检查（简化版）"""
        # 配置文件编辑器只需要检查JSON格式是否正确
        # 业务逻辑验证应该在后端进行
        errors = []
        if not isinstance(self.config_data, dict):
            errors.append("配置文件格式错误")
        return errors

    def get_agent_config(self) -> Dict[str, Any]:
        """获取Agent配置"""
        return self.config_data.get('agent_profile', {})

    def set_agent_config(self, config: Dict[str, Any]):
        """设置Agent配置"""
        self.config_data['agent_profile'] = config
        self.is_modified = True

    def get_product_catalog(self) -> str:
        """获取产品目录（兼容性方法）"""
        products = self.config_data.get('products', {})
        return str(products) if products else ""

    def set_product_catalog(self, catalog: str):
        """设置产品目录（兼容性方法）"""
        # 这个方法主要用于兼容性，实际产品数据在products节中
        self.is_modified = True

    def get_products_config(self) -> Dict[str, Any]:
        """获取产品配置"""
        return self.config_data.get('products', {})

    def get_sales_process_config(self) -> Dict[str, Any]:
        """获取销售流程配置"""
        return self.config_data.get('sales_process', {})

    def get_after_sales_config(self) -> Dict[str, Any]:
        """获取售后支持配置"""
        return self.config_data.get('after_sales_support', {})

    def get_escalation_rules_config(self) -> Dict[str, Any]:
        """获取升级规则配置"""
        return self.config_data.get('escalation_rules', {})

    def set_products_config(self, config: Dict[str, Any]):
        """设置产品配置"""
        self.config_data['products'] = config
        self.is_modified = True

    def set_sales_process_config(self, config: Dict[str, Any]):
        """设置销售流程配置"""
        self.config_data['sales_process'] = config
        self.is_modified = True

    def set_after_sales_config(self, config: Dict[str, Any]):
        """设置售后支持配置"""
        self.config_data['after_sales_support'] = config
        self.is_modified = True

    def set_escalation_rules_config(self, config: Dict[str, Any]):
        """设置升级规则配置"""
        self.config_data['escalation_rules'] = config
        self.is_modified = True

    def get_conversation_rules_config(self) -> Dict[str, Any]:
        """获取对话规则配置"""
        return self.config_data.get('conversation_rules', {})

    def set_conversation_rules_config(self, config: Dict[str, Any]):
        """设置对话规则配置"""
        self.config_data['conversation_rules'] = config
        self.is_modified = True

    def get_personalization_config(self) -> Dict[str, Any]:
        """获取个性化配置"""
        return self.config_data.get('personalization', {})

    def set_personalization_config(self, config: Dict[str, Any]):
        """设置个性化配置"""
        self.config_data['personalization'] = config
        self.is_modified = True




class UISettingsManager:
    """UI专用的设置管理器"""
    
    def __init__(self):
        self.settings = {
            'recent_configs': [],
            'window_geometry': None,
            'theme': 'default'
        }
        self.settings_file = Path.home() / '.salesgpt_config_editor' / 'settings.json'
        self.load_settings()
    
    def load_settings(self):
        """加载设置"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    self.settings.update(loaded_settings)
        except Exception as e:
            print(f"加载设置失败: {e}")
    
    def save_settings(self):
        """保存设置"""
        try:
            self.settings_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存设置失败: {e}")
    
    def add_recent_config(self, file_path: str):
        """添加最近使用的配置文件"""
        if file_path in self.settings['recent_configs']:
            self.settings['recent_configs'].remove(file_path)
        
        self.settings['recent_configs'].insert(0, file_path)
        self.settings['recent_configs'] = self.settings['recent_configs'][:10]
        self.save_settings()
    
    def get_recent_configs(self) -> List[str]:
        """获取最近使用的配置文件"""
        return [f for f in self.settings['recent_configs'] if os.path.exists(f)]


class UIDataConnector:
    """UI专用的配置文件读取器（简化版）"""

    def __init__(self):
        # 移除组件连接功能，专注于配置文件读取
        pass

    def connect_widgets(self, *widgets):
        """连接组件（已简化：仅用于兼容性）"""
        print(f"已连接 {len(widgets)} 个组件")

    def sync_data(self):
        """同步数据（已简化：配置文件自动同步）"""
        print("数据同步完成")

    def get_enhanced_proof_points(self, objection_type):
        """根据异议类型获取增强的证明点"""
        # 从配置文件加载证明点
        config_proof_points = self._load_proof_points_from_config(objection_type)

        if config_proof_points:
            return config_proof_points

        # 如果配置文件中没有，返回默认证明点
        return self._get_default_proof_points(objection_type)

    def _load_proof_points_from_config(self, objection_type):
        """从配置文件加载证明点"""
        try:
            import json
            import os

            # 构建配置文件路径
            config_path = os.path.join(
                os.path.dirname(__file__),
                '..', '..', 'backend', 'salesgpt_config', 'advanced_sales_config.json'
            )

            if not os.path.exists(config_path):
                print(f"配置文件不存在: {config_path}")
                return None

            # 读取配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 导航到异议处理配置
            sales_process = config.get('sales_process', {})
            stages = sales_process.get('stages', {})
            objection_handling = stages.get('objection_handling', {})
            common_objections = objection_handling.get('common_objections', {})

            # 映射异议类型到配置文件中的键
            objection_mapping = {
                'price_objection': 'price_too_high',
                'quality_doubt': 'effectiveness_doubt',
                'feature_concern': 'effectiveness_doubt',
                'timing_objection': 'need_to_think',
                'competitor_comparison': 'compare_with_others'
            }

            config_key = objection_mapping.get(objection_type, objection_type)
            objection_config = common_objections.get(config_key, {})

            # 获取证明点
            proof_points = objection_config.get('proof_points', [])
            if not proof_points:
                # 如果没有proof_points，尝试获取evidence字段
                proof_points = objection_config.get('evidence', [])
            if not proof_points:
                # 如果还没有，尝试获取competitive_advantages字段
                proof_points = objection_config.get('competitive_advantages', [])

            return proof_points if proof_points else None

        except Exception as e:
            print(f"从配置文件加载证明点失败: {e}")
            return None

    def _get_product_data(self):
        """从连接的组件获取产品数据"""
        for widget in self.connected_widgets:
            if hasattr(widget, 'get_current_product_data'):
                try:
                    return widget.get_current_product_data()
                except:
                    continue
            elif hasattr(widget, 'config_data') and 'products' in widget.config_data:
                return widget.config_data['products']
        return None

    def _generate_proof_points_from_products(self, objection_type, product_data):
        """根据产品数据生成证明点"""
        proof_points = []

        try:
            # 检查产品数据格式
            if isinstance(product_data, str):
                # 如果是字符串，尝试解析为JSON
                import json
                try:
                    product_data = json.loads(product_data)
                except:
                    # 如果解析失败，返回默认证明点
                    return self._get_default_proof_points(objection_type)

            if not isinstance(product_data, dict):
                return self._get_default_proof_points(objection_type)

            # 根据异议类型生成相应的证明点
            if objection_type == "price_objection":
                # 价格异议：强调性价比和长期价值
                for product_id, product in product_data.items():
                    basic_info = product.get('basic_info', {})
                    price = basic_info.get('price', 'N/A')
                    if price != 'N/A':
                        proof_points.append(f"{basic_info.get('name', product_id)}: Excellent value at ${price}")

                proof_points.extend([
                    "Long-term energy savings reduce operating costs",
                    "Comprehensive warranty protects your investment",
                    "Financing options available to spread costs"
                ])

            elif objection_type == "quality_doubt":
                # 质量疑虑：强调技术规格和认证
                for product_id, product in product_data.items():
                    basic_info = product.get('basic_info', {})
                    tech_specs = product.get('technical_specifications', {})

                    proof_points.append(f"{basic_info.get('name', product_id)}: {tech_specs.get('filtration_technology', 'Advanced filtration')}")

                proof_points.extend([
                    "Industry-leading certifications and awards",
                    "Rigorous quality testing and validation",
                    "Proven track record with thousands of satisfied customers"
                ])

            elif objection_type == "feature_concern":
                # 功能担忧：展示具体功能和优势
                for product_id, product in product_data.items():
                    basic_info = product.get('basic_info', {})
                    features = product.get('key_features', [])

                    if features:
                        proof_points.append(f"{basic_info.get('name', product_id)}: {', '.join(features[:3])}")

                proof_points.extend([
                    "Comprehensive feature comparison available",
                    "Real-world performance demonstrations",
                    "30-day satisfaction guarantee"
                ])

            else:
                # 其他异议类型的通用证明点
                for product_id, product in product_data.items():
                    basic_info = product.get('basic_info', {})
                    proof_points.append(f"{basic_info.get('name', product_id)}: {basic_info.get('description', 'High-quality air purification')}")

        except Exception as e:
            print(f"生成证明点时出错: {e}")
            return self._get_default_proof_points(objection_type)

        return proof_points[:5]  # 限制返回5个证明点

    def _get_default_proof_points(self, objection_type):
        """从配置文件获取默认证明点（移除硬编码）"""
        # 尝试从配置文件加载
        config_proof_points = self._load_proof_points_from_config(objection_type)
        if config_proof_points:
            return config_proof_points

        # 如果配置文件中没有，返回通用提示
        return [f"Please configure proof points for {objection_type} in the configuration file"]


# 创建全局实例
ui_config_manager = UIConfigManager()
ui_settings_manager = UISettingsManager()
ui_data_connector = UIDataConnector()
