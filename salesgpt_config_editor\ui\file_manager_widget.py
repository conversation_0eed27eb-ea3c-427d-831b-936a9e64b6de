#!/usr/bin/env python3
"""
文件管理器组件
负责配置文件的选择、历史记录管理
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QComboBox, QFileDialog,
                            QMessageBox, QGroupBox)
from PyQt6.QtCore import pyqtSignal, Qt
from PyQt6.QtGui import QFont
import os


class FileManagerWidget(QWidget):
    """文件管理器组件"""

    # 信号定义
    files_loaded = pyqtSignal(str)  # config_path (统一配置文件)

    def __init__(self, settings_manager=None):
        super().__init__()
        self.settings_manager = settings_manager
        if self.settings_manager is None:
            # 如果没有传入settings_manager，创建一个简单的替代
            self.settings_manager = type('MockSettings', (), {
                'add_recent_config': lambda *args: None,
                'get_recent_configs': lambda: [],
                'settings': {'recent_configs': []},
                'save_settings': lambda: None
            })()
        self.init_ui()
        self.load_recent_configs()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 文件选择组
        file_group = QGroupBox("📁 配置文件选择")
        file_group.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        file_layout = QVBoxLayout(file_group)
        
        # 统一配置文件路径
        config_layout = QHBoxLayout()
        config_layout.addWidget(QLabel("统一配置文件:"))
        self.config_path_edit = QLineEdit()
        self.config_path_edit.setPlaceholderText("选择 advanced_sales_config.json 文件（包含产品信息）")
        config_layout.addWidget(self.config_path_edit)

        self.config_browse_btn = QPushButton("浏览...")
        self.config_browse_btn.clicked.connect(self.browse_config_file)
        config_layout.addWidget(self.config_browse_btn)

        file_layout.addLayout(config_layout)

        # 说明信息
        info_label = QLabel("💡 提示：新版本已将产品信息合并到配置文件中，无需单独选择产品目录文件")
        info_label.setStyleSheet("color: #666; font-size: 11px; background: #f0f8ff; padding: 8px; border-radius: 4px;")
        file_layout.addWidget(info_label)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.load_btn = QPushButton("📂 加载配置")
        self.load_btn.clicked.connect(self.load_config_files)
        self.load_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        button_layout.addWidget(self.load_btn)
        
        self.auto_detect_btn = QPushButton("🔍 自动检测")
        self.auto_detect_btn.clicked.connect(self.auto_detect_files)
        button_layout.addWidget(self.auto_detect_btn)
        
        button_layout.addStretch()
        file_layout.addLayout(button_layout)
        
        layout.addWidget(file_group)
        
        # 历史记录组
        history_group = QGroupBox("📋 最近使用的配置")
        history_group.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        history_layout = QVBoxLayout(history_group)
        
        self.recent_combo = QComboBox()
        self.recent_combo.currentTextChanged.connect(self.on_recent_selected)
        history_layout.addWidget(self.recent_combo)
        
        recent_button_layout = QHBoxLayout()
        self.load_recent_btn = QPushButton("📂 加载选中的配置")
        self.load_recent_btn.clicked.connect(self.load_recent_config)
        recent_button_layout.addWidget(self.load_recent_btn)
        
        self.clear_history_btn = QPushButton("🗑️ 清空历史")
        self.clear_history_btn.clicked.connect(self.clear_history)
        recent_button_layout.addWidget(self.clear_history_btn)
        
        recent_button_layout.addStretch()
        history_layout.addLayout(recent_button_layout)
        
        layout.addWidget(history_group)
        
        layout.addStretch()
    
    def browse_config_file(self):
        """浏览配置文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "选择配置文件", 
            "", 
            "JSON文件 (*.json);;所有文件 (*)"
        )
        if file_path:
            self.config_path_edit.setText(file_path)
    

    
    def auto_detect_files(self):
        """自动检测统一配置文件"""
        # 尝试在当前目录及其子目录中查找配置文件
        current_dir = os.getcwd()
        config_file = None

        # 常见的配置文件位置
        possible_paths = [
            # 当前目录及子目录
            os.path.join(current_dir, "advanced_sales_config.json"),
            os.path.join(current_dir, "config", "advanced_sales_config.json"),
            os.path.join(current_dir, "salesgpt_config", "advanced_sales_config.json"),
            # 上级目录
            os.path.join(os.path.dirname(current_dir), "backend", "salesgpt_config", "advanced_sales_config.json"),
            os.path.join(os.path.dirname(current_dir), "salesgpt_config", "advanced_sales_config.json"),
            # 递归搜索当前目录
        ]

        # 检查预定义路径
        for path in possible_paths:
            if os.path.exists(path):
                config_file = path
                break

        # 如果预定义路径都没找到，则递归搜索
        if not config_file:
            for root, dirs, files in os.walk(current_dir):
                if "advanced_sales_config.json" in files:
                    config_file = os.path.join(root, "advanced_sales_config.json")
                    break

            # 如果当前目录没找到，搜索上级目录
            if not config_file:
                parent_dir = os.path.dirname(current_dir)
                for root, dirs, files in os.walk(parent_dir):
                    if "advanced_sales_config.json" in files:
                        config_file = os.path.join(root, "advanced_sales_config.json")
                        break

        if config_file:
            self.config_path_edit.setText(config_file)
            QMessageBox.information(self, "自动检测", f"✅ 已找到统一配置文件：\n{config_file}")
        else:
            QMessageBox.warning(self, "自动检测", "❌ 未找到 advanced_sales_config.json 配置文件，请手动选择。")
    
    def load_config_files(self):
        """加载统一配置文件"""
        import logging
        logging.info("=== 开始文件管理器配置加载过程 ===")

        config_path = self.config_path_edit.text().strip()
        logging.info(f"获取配置文件路径: {config_path}")

        if not config_path:
            logging.warning("配置文件路径为空")
            QMessageBox.warning(self, "错误", "请选择统一配置文件！")
            return

        if not os.path.exists(config_path):
            logging.error(f"配置文件不存在: {config_path}")
            QMessageBox.warning(self, "错误", f"配置文件不存在: {config_path}")
            return

        # 验证是否为有效的JSON文件
        try:
            import json
            logging.info("开始验证JSON文件格式...")
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                logging.info("JSON文件解析成功")
                # 检查是否包含产品信息
                if 'products' not in config_data:
                    logging.warning("配置文件中未找到产品信息")
                    QMessageBox.warning(self, "警告", "配置文件中未找到产品信息，可能是旧版本配置文件。")
                else:
                    logging.info(f"找到产品信息，包含 {len(config_data.get('products', []))} 个产品")
        except json.JSONDecodeError as e:
            logging.error(f"JSON格式错误: {e}")
            QMessageBox.warning(self, "错误", f"配置文件格式错误: {e}")
            return
        except Exception as e:
            logging.error(f"读取配置文件失败: {e}")
            QMessageBox.warning(self, "错误", f"读取配置文件失败: {e}")
            return

        # 添加到历史记录
        logging.info("添加到历史记录...")
        self.settings_manager.add_recent_config(config_path)
        self.load_recent_configs()
        logging.info("历史记录更新完成")

        # 发送信号
        logging.info("准备发送files_loaded信号...")
        self.files_loaded.emit(config_path)
        logging.info("files_loaded信号已发送")
    
    def load_recent_configs(self):
        """加载最近使用的配置"""
        self.recent_combo.clear()
        recent_configs = self.settings_manager.get_recent_configs()

        for config_path in recent_configs:
            # config_path 是字符串，不是字典
            config_name = os.path.basename(config_path)

            # 简化显示，统一显示为配置文件
            display_text = f"{config_name}"

            # 将配置路径作为数据存储
            self.recent_combo.addItem(display_text, config_path)
    
    def on_recent_selected(self):
        """选择历史记录项"""
        current_data = self.recent_combo.currentData()
        if current_data:
            # current_data 现在是配置文件路径字符串
            config_path = str(current_data)
            self.config_path_edit.setText(config_path)
    
    def load_recent_config(self):
        """加载选中的历史配置"""
        current_data = self.recent_combo.currentData()
        if current_data:
            # current_data 现在是配置文件路径字符串
            config_path = str(current_data)
            self.config_path_edit.setText(config_path)
            self.load_config_files()
    
    def clear_history(self):
        """清空历史记录"""
        reply = QMessageBox.question(
            self, 
            "确认", 
            "确定要清空所有历史记录吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.settings_manager.settings['recent_configs'] = []
            self.settings_manager.save_settings()
            self.load_recent_configs()
