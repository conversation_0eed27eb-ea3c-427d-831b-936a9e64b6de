# ================================
# LLM Configuration
# ================================

# LLM提供商选择 (gemini/openai/deepseek)
LLM_PROVIDER=gemini

# Gemini Configuration (推荐)
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-2.0-flash-lite

# OpenAI Configuration (备用)
# OPENAI_API_KEY=your_openai_api_key_here
# OPENAI_MODEL=gpt-3.5-turbo

# DeepSeek Configuration (备用)
# DEEPSEEK_API_KEY=your_deepseek_api_key_here
# DEEPSEEK_MODEL=deepseek-chat
# DEEPSEEK_BASE_URL=https://api.deepseek.com

# ================================
# 代理配置
# ================================

# 代理开关
PROXY_ENABLED=true

# 代理类型：socks5 或 http
PROXY_TYPE=socks5

# SOCKS5 代理配置
SOCKS5_HOST=**************
SOCKS5_PORT=33489

# HTTP 代理配置
HTTP_PROXY_HOST=**************
HTTP_PROXY_PORT=33486

# 旧版代理配置（向后兼容）
PROXY_HOST=**************
PROXY_PORT=33489

# Server Configuration
HOST=0.0.0.0
PORT=8000

# Token Configuration - 不同类型的访问令牌
WORDPRESS_TOKEN=wp_secure_token_here
ADMIN_TOKEN=admin_secure_token_here
API_TOKEN=api_secure_token_here

# Database Configuration (SQLite for simplicity)
# 数据库文件将存储在backend目录中
DATABASE_URL=sqlite+aiosqlite:///backend/chat_data.db

# Logging Configuration
# 日志级别: simple (简单模式) 或 detailed (详细模式)
LOG_LEVEL=simple
# 是否显示轮询请求日志: true (显示) 或 false (隐藏)
LOG_SHOW_POLLING=false

# CORS Configuration
ALLOWED_ORIGINS=*

# ================================
# IP黑名单配置
# ================================

# 启用/禁用IP黑名单功能
IP_BLACKLIST_ENABLED=false

# 黑名单更新间隔（小时）
IP_BLACKLIST_UPDATE_INTERVAL=6

# 黑名单源配置（JSON格式，支持多源和优先级）
IP_BLACKLIST_SOURCES={"ipsum": {"url": "https://raw.githubusercontent.com/stamparm/ipsum/master/ipsum.txt", "priority": 1, "enabled": true, "description": "High-quality malicious IP feed by stamparm", "format": "plain_ip"}, "firehol_level1": {"url": "https://raw.githubusercontent.com/firehol/blocklist-ipsets/master/firehol_level1.netset", "priority": 2, "enabled": true, "description": "FireHOL Level 1 - Most aggressive attackers", "format": "cidr"}, "spamhaus_drop": {"url": "https://www.spamhaus.org/drop/drop.txt", "priority": 3, "enabled": true, "description": "Spamhaus Don't Route Or Peer List", "format": "spamhaus"}, "emerging_threats": {"url": "https://rules.emergingthreats.net/fwrules/emerging-Block-IPs.txt", "priority": 4, "enabled": false, "description": "Emerging Threats Block IPs", "format": "plain_ip"}}

# 本地黑名单文件存储路径
IP_BLACKLIST_CACHE_DIR=blacklist_cache

# 黑名单日志级别 (debug, info, warning, error)
IP_BLACKLIST_LOG_LEVEL=info

# ================================
# 防护系统配置
# ================================

# 防护规则配置文件路径（可选，默认自动寻找）
# PROTECTION_RULES_PATH=config/protection_rules.json

# ================================
# SalesGPT 默认配置
# ================================

# 默认 Agent 配置（当配置文件加载失败时使用）
DEFAULT_AGENT_NAME=Dylan
DEFAULT_AGENT_ROLE=Personal Assistant
DEFAULT_COMPANY_NAME=KNKA Environmental Appliances
DEFAULT_COMPANY_WEBSITE=knkalife.com
DEFAULT_COMPANY_BUSINESS=

# 产品目录已整合到 salesgpt_config/advanced_sales_config.json 中
# PRODUCT_CATALOG_PATH=salesgpt_config/product_catalog.txt  # 已废弃

# ================================
# SalesGPT 环境变量覆盖（可选）
# ================================
# 这些变量可以覆盖配置文件中的设置，用于不同环境部署

# 覆盖 Agent 信息（取消注释以启用）
# OVERRIDE_AGENT_NAME=CustomAgent
# OVERRIDE_AGENT_ROLE=Sales Consultant
# OVERRIDE_COMPANY_NAME=Custom Company Name
# OVERRIDE_COMPANY_WEBSITE=custom-website.com
# OVERRIDE_COMPANY_BUSINESS=Custom business description here

# ================================
# 管理员配置
# ================================

# 管理员超时配置（分钟）
ADMIN_CONTROL_TIMEOUT_MINUTES=15
ADMIN_TIMEOUT_CHECK_INTERVAL_MINUTES=1

# ================================
# 时区配置
# ================================

# 默认时区
DEFAULT_TIMEZONE=America/Los_Angeles

# ================================
# 部署环境示例
# ================================

# 开发环境示例
# OVERRIDE_AGENT_NAME=Dylan-Dev
# OVERRIDE_COMPANY_NAME=KNKA Environmental Appliances (Dev)

# 测试环境示例
# OVERRIDE_AGENT_NAME=Dylan-Test
# OVERRIDE_COMPANY_NAME=KNKA Environmental Appliances (Test)

# 生产环境示例
# OVERRIDE_AGENT_NAME=Dylan
# OVERRIDE_COMPANY_NAME=KNKA Environmental Appliances

# ================================
# HTTPS/SSL 配置
# ================================

# HTTPS模式选择
# disabled: 纯HTTP模式（开发环境推荐）
# direct: 后端直接提供HTTPS（生产环境推荐）
# cloudflare: 使用Cloudflare Tunnel（简化部署推荐）
HTTPS_MODE=disabled

# SSL证书文件路径（仅当HTTPS_MODE=direct时需要）
# 支持相对路径（相对于backend目录）和绝对路径
SSL_CERT_FILE=ssl/certificate.pem
SSL_KEY_FILE=ssl/private.key

# 自动检测证书文件
# true: 如果指定的证书文件不存在，自动搜索常见的证书文件名
# false: 仅使用上面指定的文件路径
SSL_AUTO_DETECT=true

# Cloudflare DNS验证配置（可选，用于自动获取Let's Encrypt证书）
# 仅在需要自动证书管理时配置
# CF_API_TOKEN=your_cloudflare_api_token_here
# CF_ZONE_ID=your_cloudflare_zone_id_here

# ================================
# SSL配置示例
# ================================

# 开发环境配置（HTTP模式）
# HTTPS_MODE=disabled
# 无需配置SSL相关参数

# 测试环境配置（自签名证书）
# HTTPS_MODE=direct
# SSL_CERT_FILE=ssl/test-certificate.pem
# SSL_KEY_FILE=ssl/test-private.key
# SSL_AUTO_DETECT=true

# 生产环境配置（Let's Encrypt证书）
# HTTPS_MODE=direct
# SSL_CERT_FILE=ssl/fullchain.pem
# SSL_KEY_FILE=ssl/privkey.pem
# SSL_AUTO_DETECT=true

# Cloudflare Tunnel配置（无需证书管理）
# HTTPS_MODE=cloudflare
# 后端保持HTTP，HTTPS由Cloudflare处理
