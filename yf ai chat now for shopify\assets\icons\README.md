# 图标文件目录

这个目录用于存放聊天组件使用的图标文件。

## 📁 图标文件说明

### 当前使用的图标
- **聊天按钮**: 使用Unicode表情符号 💬
- **发送按钮**: 使用Unicode符号 ➤
- **关闭按钮**: 使用Unicode符号 ×
- **清除按钮**: 使用Unicode表情符号 🗑️

### 可选的图标文件
如果需要使用自定义图标，可以在此目录放置以下文件：

#### SVG图标 (推荐)
- `chat-icon.svg` - 聊天按钮图标
- `send-icon.svg` - 发送按钮图标
- `close-icon.svg` - 关闭按钮图标
- `clear-icon.svg` - 清除按钮图标

#### PNG图标 (备选)
- `chat-icon.png` - 聊天按钮图标 (建议32x32px)
- `send-icon.png` - 发送按钮图标 (建议16x16px)
- `close-icon.png` - 关闭按钮图标 (建议16x16px)
- `clear-icon.png` - 清除按钮图标 (建议16x16px)

## 🎨 使用自定义图标

### 1. 上传图标文件
将图标文件上传到Shopify主题的 `assets/` 目录

### 2. 修改CSS样式
在 `yf-chat-shopify.css` 中添加：

```css
/* 使用SVG图标 */
.yf-chat-widget .yf-chat-button {
    background-image: url('{{ "chat-icon.svg" | asset_url }}');
    background-size: 24px 24px;
    background-repeat: no-repeat;
    background-position: center;
    font-size: 0; /* 隐藏文字 */
}

.yf-chat-widget .yf-chat-send {
    background-image: url('{{ "send-icon.svg" | asset_url }}');
    background-size: 16px 16px;
    background-repeat: no-repeat;
    background-position: center;
    font-size: 0; /* 隐藏文字 */
}
```

### 3. 或者修改JavaScript
在 `yf-chat-shopify.js` 中修改图标：

```javascript
// 修改聊天按钮图标
widget.innerHTML = `
    <button class="yf-chat-button" id="yf-chat-button">
        <img src="{{ 'chat-icon.svg' | asset_url }}" alt="Chat" width="24" height="24">
    </button>
    ...
`;
```

## 🎯 图标设计建议

### 聊天按钮图标
- **尺寸**: 24x24px 或 32x32px
- **样式**: 简洁、现代
- **颜色**: 单色或双色，与主题协调
- **格式**: SVG (矢量) 优于 PNG

### 功能按钮图标
- **尺寸**: 16x16px 或 20x20px
- **样式**: 线条清晰，易于识别
- **颜色**: 通常使用白色或主题色
- **格式**: SVG 推荐

## 🌟 推荐的图标库

### 免费图标库
- **Feather Icons**: https://feathericons.com/
- **Heroicons**: https://heroicons.com/
- **Tabler Icons**: https://tabler-icons.io/
- **Lucide**: https://lucide.dev/

### 商业图标库
- **Font Awesome**: https://fontawesome.com/
- **Material Icons**: https://fonts.google.com/icons
- **Phosphor Icons**: https://phosphoricons.com/

## 📝 使用示例

### 示例1: 使用Feather Icons
```html
<!-- 聊天图标 -->
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
</svg>

<!-- 发送图标 -->
<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
  <line x1="22" y1="2" x2="11" y2="13"></line>
  <polygon points="22,2 15,21 11,13 3,9 22,2"></polygon>
</svg>
```

### 示例2: 使用Font Awesome
```html
<!-- 需要先加载Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<!-- 在JavaScript中使用 -->
<i class="fas fa-comments"></i>  <!-- 聊天图标 -->
<i class="fas fa-paper-plane"></i>  <!-- 发送图标 -->
<i class="fas fa-times"></i>  <!-- 关闭图标 -->
<i class="fas fa-trash"></i>  <!-- 清除图标 -->
```

## 🔧 技术注意事项

### SVG优化
- 使用SVGO工具优化SVG文件大小
- 移除不必要的元数据和注释
- 确保SVG在不同尺寸下显示清晰

### 浏览器兼容性
- SVG支持IE9+和所有现代浏览器
- PNG作为SVG的fallback选项
- 考虑高DPI屏幕的显示效果

### 性能考虑
- 小图标建议内联到CSS或HTML中
- 大图标文件使用外部引用
- 考虑使用图标字体减少HTTP请求

---
*选择合适的图标可以提升用户体验和品牌一致性！*
