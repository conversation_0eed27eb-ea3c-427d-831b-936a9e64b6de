#!/usr/bin/env python3
"""
SalesGPT 配置管理器
负责配置文件的加载、保存、验证和备份
"""

import json
import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List


class ConfigManager:
    """配置文件管理器"""
    
    def __init__(self):
        self.config_data = {}
        self.product_catalog = ""
        self.config_file_path = ""
        self.catalog_file_path = ""
        self.is_modified = False
        
    def load_config_files(self, config_path: str, catalog_path: str = "") -> bool:
        """加载配置文件（支持统一配置和旧格式）"""
        try:
            # 加载JSON配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config_data = json.load(f)

            # 如果有catalog_path且文件存在，则加载产品目录文件（兼容旧格式）
            if catalog_path and os.path.exists(catalog_path):
                with open(catalog_path, 'r', encoding='utf-8') as f:
                    self.product_catalog = f.read()
                self.catalog_file_path = catalog_path
                print("✅ 已加载旧格式配置（JSON + TXT）")
            else:
                # 统一配置格式，产品信息在JSON中
                self.product_catalog = ""  # 清空旧的产品目录
                self.catalog_file_path = ""
                print("✅ 已加载统一配置格式")

            self.config_file_path = config_path
            self.is_modified = False

            # 验证配置结构
            if 'products' in self.config_data:
                products_count = len(self.config_data['products'])
                print(f"✅ 发现 {products_count} 个产品配置")
            else:
                print("⚠️ 配置文件中未找到产品信息")

            return True

        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            return False
    
    def save_config_files(self) -> bool:
        """保存配置文件（支持统一配置和旧格式）"""
        try:
            print(f"💾 开始保存配置文件到: {self.config_file_path}")

            # 检查销售流程数据
            if 'sales_process' in self.config_data:
                sales_process = self.config_data['sales_process']
                if 'stages' in sales_process:
                    print(f"💾 配置管理器中的销售流程包含 {len(sales_process['stages'])} 个阶段")
                    for stage_key, stage_data in sales_process['stages'].items():
                        print(f"  配置管理器阶段 {stage_key}: {stage_data.get('name', 'No Name')} - {stage_data.get('objective', 'No Objective')[:30]}...")

            # 创建备份
            self.create_backup()

            # 保存JSON配置文件
            with open(self.config_file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=2)

            print(f"✅ 配置文件已保存到: {self.config_file_path}")

            # 如果有catalog_file_path，则保存产品目录文件（兼容旧格式）
            if self.catalog_file_path and self.product_catalog:
                with open(self.catalog_file_path, 'w', encoding='utf-8') as f:
                    f.write(self.product_catalog)
                print("✅ 已保存旧格式配置（JSON + TXT）")
            else:
                print("✅ 已保存统一配置格式")

            self.is_modified = False
            return True

        except Exception as e:
            print(f"❌ 保存配置文件失败: {e}")
            return False
    
    def create_backup(self):
        """创建配置文件备份（支持统一配置和旧格式）"""
        if not self.config_file_path:
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 备份JSON配置文件
        if os.path.exists(self.config_file_path):
            config_backup = f"{self.config_file_path}.backup_{timestamp}"
            shutil.copy2(self.config_file_path, config_backup)

        # 如果有catalog文件，则备份（兼容旧格式）
        if self.catalog_file_path and os.path.exists(self.catalog_file_path):
            catalog_backup = f"{self.catalog_file_path}.backup_{timestamp}"
            shutil.copy2(self.catalog_file_path, catalog_backup)
    
    def validate_config(self) -> List[str]:
        """验证配置文件"""
        errors = []
        
        # 验证必需的配置项
        required_sections = ['agent_profile', 'sales_process', 'purchase_channels']
        for section in required_sections:
            if section not in self.config_data:
                errors.append(f"缺少必需的配置节: {section}")
        
        # 验证Agent配置
        if 'agent_profile' in self.config_data:
            agent = self.config_data['agent_profile']
            required_fields = ['name', 'role', 'company', 'website']
            for field in required_fields:
                if not agent.get(field):
                    errors.append(f"Agent配置缺少必需字段: {field}")
        
        return errors
    
    def get_agent_config(self) -> Dict[str, Any]:
        """获取Agent配置"""
        return self.config_data.get('agent_profile', {})
    
    def set_agent_config(self, config: Dict[str, Any]):
        """设置Agent配置"""
        self.config_data['agent_profile'] = config
        self.is_modified = True
    
    def get_sales_process_config(self) -> Dict[str, Any]:
        """获取销售流程配置"""
        return self.config_data.get('sales_process', {})
    
    def set_sales_process_config(self, config: Dict[str, Any]):
        """设置销售流程配置"""
        self.config_data['sales_process'] = config
        self.is_modified = True
    
    def get_purchase_channels_config(self) -> Dict[str, Any]:
        """获取购买渠道配置"""
        return self.config_data.get('purchase_channels', {})
    
    def set_purchase_channels_config(self, config: Dict[str, Any]):
        """设置购买渠道配置"""
        self.config_data['purchase_channels'] = config
        self.is_modified = True
    
    def get_after_sales_config(self) -> Dict[str, Any]:
        """获取售后支持配置"""
        return self.config_data.get('after_sales_support', {})
    
    def set_after_sales_config(self, config: Dict[str, Any]):
        """设置售后支持配置"""
        self.config_data['after_sales_support'] = config
        self.is_modified = True
    
    def get_product_catalog(self) -> str:
        """获取产品目录（兼容旧版本）"""
        return self.product_catalog

    def set_product_catalog(self, catalog: str):
        """设置产品目录（兼容旧版本）"""
        self.product_catalog = catalog
        self.is_modified = True

    def get_products_config(self) -> Dict[str, Any]:
        """获取产品配置"""
        return self.config_data.get('products', {})

    def set_products_config(self, products: Dict[str, Any]):
        """设置产品配置"""
        self.config_data['products'] = products
        self.is_modified = True
    
    def get_escalation_rules_config(self) -> Dict[str, Any]:
        """获取升级规则配置"""
        return self.config_data.get('escalation_rules', {})

    def set_escalation_rules_config(self, config: Dict[str, Any]):
        """设置升级规则配置"""
        self.config_data['escalation_rules'] = config
        self.is_modified = True

    def get_conversation_rules_config(self) -> Dict[str, Any]:
        """获取对话规则配置"""
        return self.config_data.get('conversation_rules', {})

    def set_conversation_rules_config(self, config: Dict[str, Any]):
        """设置对话规则配置"""
        self.config_data['conversation_rules'] = config
        self.is_modified = True

    def get_personalization_config(self) -> Dict[str, Any]:
        """获取个性化配置"""
        return self.config_data.get('personalization', {})

    def set_personalization_config(self, config: Dict[str, Any]):
        """设置个性化配置"""
        self.config_data['personalization'] = config
        self.is_modified = True

    def get_available_variables(self) -> Dict[str, str]:
        """获取可用的变量列表"""
        variables = {}

        # Agent相关变量
        agent = self.get_agent_config()
        if agent:
            variables.update({
                '{agent_name}': agent.get('name', ''),
                '{company}': agent.get('company', ''),
                '{website}': agent.get('website', ''),
                '{role}': agent.get('role', '')
            })

        # 产品相关变量（从配置文件动态获取）
        products_config = self.get_products_config()
        if products_config:
            # 从实际产品配置中获取变量
            product_ids = list(products_config.keys())
            if product_ids:
                first_product = products_config[product_ids[0]]
                basic_info = first_product.get('basic_info', {})
                variables.update({
                    '{product_name}': f'产品名称（可选：{", ".join(product_ids)}）',
                    '{product_id}': '产品ID（从配置文件获取）',
                    '{asin}': 'Amazon产品标识（从配置文件获取）',
                    '{item_id}': '其他平台产品标识（从配置文件获取）',
                })
        else:
            # 如果没有产品配置，使用通用变量
            variables.update({
                '{product_name}': '产品名称（从产品配置选择）',
                '{product_id}': '产品ID',
                '{asin}': 'Amazon产品标识',
                '{item_id}': '其他平台产品标识',
            })

        # 通用内容变量
        variables.update({
            '{installation_steps}': '安装步骤',
            '{operation_guide}': '操作指南',
            '{maintenance_guide}': '维护指南',
            '{warranty_details}': '保修详情'
        })

        return variables


class SettingsManager:
    """应用设置管理器"""
    
    def __init__(self):
        self.settings_file = "config_editor_settings.json"
        self.settings = self.load_settings()
    
    def load_settings(self) -> Dict[str, Any]:
        """加载应用设置"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载设置失败: {e}")
        
        # 返回默认设置
        return {
            'recent_configs': [],
            'window_geometry': None,
            'last_config_path': '',
            'last_catalog_path': ''
        }
    
    def save_settings(self):
        """保存应用设置"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存设置失败: {e}")
    
    def add_recent_config(self, config_path: str, catalog_path: str = ""):
        """添加最近使用的配置（兼容新旧格式）"""
        recent_item = {
            'config_path': config_path,
            'catalog_path': catalog_path,  # 保持兼容性，但可能为空
            'timestamp': datetime.now().isoformat(),
            'is_unified': catalog_path == ""  # 标记是否为统一配置
        }

        # 移除重复项
        recent_configs = self.settings.get('recent_configs', [])
        recent_configs = [item for item in recent_configs
                         if item.get('config_path') != config_path]

        # 添加到开头
        recent_configs.insert(0, recent_item)

        # 限制数量
        self.settings['recent_configs'] = recent_configs[:10]
        self.save_settings()
    
    def get_recent_configs(self) -> List[Dict[str, str]]:
        """获取最近使用的配置"""
        return self.settings.get('recent_configs', [])
