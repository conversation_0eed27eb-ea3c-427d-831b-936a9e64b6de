#!/usr/bin/env python3
"""
数据导出模块
负责将数据导出为CSV等格式
"""

import pandas as pd
from pathlib import Path
from typing import Optional
import json

class ExportManager:
    def __init__(self):
        pass
    
    def export_to_csv(self, data: pd.DataFrame, file_path: str, 
                     encoding: str = 'utf-8', separator: str = ',', 
                     include_headers: bool = True) -> bool:
        """导出数据到CSV文件"""
        try:
            data.to_csv(
                file_path,
                encoding=encoding,
                sep=separator,
                index=False,
                header=include_headers
            )
            return True
        except Exception as e:
            raise Exception(f"导出CSV失败: {e}")
    
    def export_to_excel(self, data: pd.DataFrame, file_path: str) -> bool:
        """导出数据到Excel文件"""
        try:
            data.to_excel(file_path, index=False)
            return True
        except Exception as e:
            raise Exception(f"导出Excel失败: {e}")
    
    def export_to_json(self, data: pd.DataFrame, file_path: str, 
                      encoding: str = 'utf-8') -> bool:
        """导出数据到JSON文件"""
        try:
            # 处理特殊数据类型
            data_dict = data.to_dict('records')
            
            # 处理日期时间类型
            for record in data_dict:
                for key, value in record.items():
                    if pd.isna(value):
                        record[key] = None
                    elif isinstance(value, pd.Timestamp):
                        record[key] = value.isoformat()
            
            with open(file_path, 'w', encoding=encoding) as f:
                json.dump(data_dict, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            raise Exception(f"导出JSON失败: {e}")
    
    def get_export_preview(self, data: pd.DataFrame, max_rows: int = 5) -> str:
        """获取导出预览"""
        if data.empty:
            return "无数据可预览"
        
        preview_data = data.head(max_rows)
        return preview_data.to_string(index=False)
    
    def format_data_for_export(self, data: pd.DataFrame) -> pd.DataFrame:
        """格式化数据以便导出"""
        formatted_data = data.copy()
        
        # 处理JSON列（如果存在）
        for column in formatted_data.columns:
            if formatted_data[column].dtype == 'object':
                # 尝试检测JSON字符串并格式化
                sample_value = formatted_data[column].dropna().iloc[0] if not formatted_data[column].dropna().empty else None
                if sample_value and isinstance(sample_value, str):
                    try:
                        json.loads(sample_value)
                        # 如果是JSON字符串，保持原样或者可以选择格式化
                        pass
                    except (json.JSONDecodeError, TypeError):
                        pass
        
        # 处理时间戳列
        for column in formatted_data.columns:
            if 'created_at' in column.lower() or 'updated_at' in column.lower() or 'at' in column.lower():
                try:
                    formatted_data[column] = pd.to_datetime(formatted_data[column]).dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    pass
        
        return formatted_data

# 全局导出管理器实例
export_manager = ExportManager()
