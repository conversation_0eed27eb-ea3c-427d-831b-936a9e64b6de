#!/usr/bin/env python3
"""
SalesGPT 配置测试脚本
测试配置文件和环境变量覆盖功能
"""

import os
import sys
import json
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 加载 .env 文件
try:
    from dotenv import load_dotenv
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✅ 已加载环境变量文件: {env_path}")
    else:
        print(f"⚠️  环境变量文件不存在: {env_path}")
except ImportError:
    print("⚠️  python-dotenv 未安装，尝试手动加载环境变量...")
    # 手动加载 .env 文件
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        with open(env_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
        print(f"✅ 手动加载环境变量文件: {env_path}")
    else:
        print(f"⚠️  环境变量文件不存在: {env_path}")

def test_config_loading():
    """测试配置加载功能"""
    print("🔍 SalesGPT 配置测试")
    print("=" * 50)
    
    # 测试配置文件是否存在
    config_dir = Path(__file__).parent / "salesgpt_config"
    
    print("📁 检查配置文件...")
    
    # 检查 advanced_sales_config.json
    config_file = config_dir / "advanced_sales_config.json"
    if config_file.exists():
        print(f"✅ 找到配置文件: {config_file}")
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            agent_profile = config.get('agent_profile', {})
            print(f"   Agent名称: {agent_profile.get('name', 'N/A')}")
            print(f"   Agent角色: {agent_profile.get('role', 'N/A')}")
            print(f"   公司名称: {agent_profile.get('company', 'N/A')}")
            print(f"   公司网站: {agent_profile.get('website', 'N/A')}")
            
        except Exception as e:
            print(f"❌ 配置文件格式错误: {e}")
    else:
        print(f"❌ 配置文件不存在: {config_file}")
    
    # 检查产品目录
    catalog_file = config_dir / "product_catalog.txt"
    if catalog_file.exists():
        print(f"✅ 找到产品目录: {catalog_file}")
        with open(catalog_file, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            print(f"   产品目录行数: {len(lines)}")
            print(f"   文件大小: {len(content)} 字符")
    else:
        print(f"❌ 产品目录不存在: {catalog_file}")
    
    print("\n🌍 检查环境变量...")
    
    # 检查基础环境变量
    env_vars = [
        "GEMINI_API_KEY",
        "GEMINI_MODEL",
        "PROXY_ENABLED",
        "DEFAULT_AGENT_NAME",
        "DEFAULT_COMPANY_NAME",
        "PRODUCT_CATALOG_PATH"
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            # 隐藏敏感信息
            if "API_KEY" in var and len(value) > 10:
                display_value = value[:10] + "..." + value[-4:]
            else:
                display_value = value
            print(f"✅ {var}: {display_value}")
        else:
            print(f"⚠️  {var}: 未设置")
    
    # 检查覆盖环境变量
    print("\n🔄 检查环境变量覆盖...")
    override_vars = [
        "OVERRIDE_AGENT_NAME",
        "OVERRIDE_COMPANY_NAME", 
        "OVERRIDE_COMPANY_WEBSITE",
        "OVERRIDE_COMPANY_BUSINESS"
    ]
    
    has_overrides = False
    for var in override_vars:
        value = os.getenv(var)
        if value:
            print(f"🔄 {var}: {value}")
            has_overrides = True
    
    if not has_overrides:
        print("ℹ️  未设置环境变量覆盖（这是正常的）")

def test_salesgpt_service():
    """测试 SalesGPT 服务初始化"""
    print("\n🤖 测试 SalesGPT 服务初始化...")
    print("=" * 50)
    
    try:
        from services.salesgpt_service import SalesGPTService
        
        # 创建服务实例
        service = SalesGPTService()
        
        print(f"✅ SalesGPT 服务初始化成功")
        print(f"   Agent名称: {service.salesperson_name}")
        print(f"   Agent角色: {service.salesperson_role}")
        print(f"   公司名称: {service.company_name}")
        
        if hasattr(service, 'company_website'):
            print(f"   公司网站: {service.company_website}")
        
        print(f"   产品目录路径: {service.product_catalog_path}")
        print(f"   产品目录存在: {os.path.exists(service.product_catalog_path)}")
        
        # 检查销售阶段
        if hasattr(service, 'sales_stages'):
            print(f"   销售阶段数量: {len(service.sales_stages)}")
            print(f"   销售阶段: {list(service.sales_stages.keys())}")
        
        # 检查配置来源
        print("\n📋 配置来源分析:")
        
        # 检查是否使用了环境变量覆盖
        overrides_used = []
        if os.getenv('OVERRIDE_AGENT_NAME'):
            overrides_used.append('Agent名称')
        if os.getenv('OVERRIDE_COMPANY_NAME'):
            overrides_used.append('公司名称')
        if os.getenv('OVERRIDE_COMPANY_WEBSITE'):
            overrides_used.append('公司网站')
        
        if overrides_used:
            print(f"🔄 使用环境变量覆盖: {', '.join(overrides_used)}")
        else:
            print("📁 使用配置文件设置")
        
    except Exception as e:
        print(f"❌ SalesGPT 服务初始化失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 SalesGPT 配置完整性测试")
    print("=" * 60)
    
    test_config_loading()
    test_salesgpt_service()
    
    print("\n✨ 测试完成!")
    print("\n💡 提示:")
    print("   - 如需切换到其他网站/产品，请参考 salesgpt_config/README.md")
    print("   - 环境变量覆盖可以在不修改配置文件的情况下部署到不同环境")
    print("   - 生产环境建议使用环境变量覆盖方式")
    print("   - 完整的配置和部署指南请查看 salesgpt_config/README.md")

if __name__ == "__main__":
    main()
