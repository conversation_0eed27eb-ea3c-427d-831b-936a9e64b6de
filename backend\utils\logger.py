import logging
import os
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum

class LogLevel(Enum):
    """日志级别枚举"""
    SIMPLE = "simple"      # 简单模式：只显示关键信息
    DETAILED = "detailed"  # 详细模式：显示所有调试信息

class ChatLogger:
    """聊天系统专用日志器"""

    def __init__(self):
        self.log_level = LogLevel(os.getenv("LOG_LEVEL", "simple"))
        self.show_polling = os.getenv("LOG_SHOW_POLLING", "false").lower() == "true"
        self.setup_logger()
    
    def setup_logger(self):
        """设置日志器"""
        # 创建日志器
        self.logger = logging.getLogger("yf_ai_chat")
        self.logger.setLevel(logging.DEBUG)
        
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        
        # 创建格式器
        if self.log_level == LogLevel.DETAILED:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        else:
            formatter = logging.Formatter('%(message)s')
        
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def _format_time(self) -> str:
        """格式化当前时间"""
        return datetime.now().strftime('%H:%M:%S')
    
    def startup(self, message: str):
        """系统启动日志"""
        if self.log_level == LogLevel.SIMPLE:
            print(f"🚀 {message}")
        else:
            self.logger.info(f"🚀 [STARTUP] {message}")
    
    def shutdown(self, message: str):
        """系统关闭日志"""
        if self.log_level == LogLevel.SIMPLE:
            print(f"🔄 {message}")
        else:
            self.logger.info(f"🔄 [SHUTDOWN] {message}")
    
    def chat_request(self, session_id: str, user_message: str, token_type: str = "unknown"):
        """聊天请求日志"""
        if self.log_level == LogLevel.SIMPLE:
            print(f"会话ID: {session_id[:12]}... 用户: {user_message[:60]}{'...' if len(user_message) > 60 else ''}")
        else:
            print(f"\n🗨️  [CHAT REQUEST] 新的聊天请求")
            print(f"   📝 Session ID: {session_id}")
            print(f"   👤 用户消息: {user_message}")
            print(f"   🔑 Token类型: {token_type}")
            print(f"   🕐 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    def ai_response(self, response: str, session_id: str):
        """AI回复日志"""
        if self.log_level == LogLevel.SIMPLE:
            print(f"AI回复: {response[:60]}{'...' if len(response) > 60 else ''}")
        else:
            print(f"   ✅ LLM回复成功: {response[:100]}{'...' if len(response) > 100 else ''}")
            print(f"   📏 回复长度: {len(response)} 字符")

    def admin_message(self, session_id: str, admin_id: str, message: str):
        """管理员消息日志"""
        if self.log_level == LogLevel.SIMPLE:
            print(f"客服回复: {message[:60]}{'...' if len(message) > 60 else ''}")
        else:
            print(f"\n📨 [ADMIN MESSAGE] 管理员发送消息")
            print(f"   📝 Session ID: {session_id}")
            print(f"   👨‍💼 Admin ID: {admin_id}")
            print(f"   💬 消息内容: {message}")
            print(f"   🕐 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    def session_control(self, session_id: str, admin_id: str, action: str):
        """会话控制日志"""
        action_map = {
            "take": "接管",
            "release": "释放"
        }
        action_text = action_map.get(action, action)

        if self.log_level == LogLevel.SIMPLE:
            print(f"🔧 [{self._format_time()}] 会话{action_text}: {session_id[:12]}...")
        else:
            print(f"🔧 会话 {session_id} 被管理员 {admin_id} {action_text}控制")

    def error(self, message: str, error: Exception = None):
        """错误日志"""
        if self.log_level == LogLevel.SIMPLE:
            print(f"❌ [{self._format_time()}] 错误: {message}")
        else:
            print(f"❌ [ERROR] {message}")
            if error:
                import traceback
                print(f"   📋 错误详情:")
                traceback.print_exc()

    def success(self, message: str):
        """成功日志"""
        if self.log_level == LogLevel.SIMPLE:
            print(f"✅ {message}")
        else:
            self.logger.info(f"✅ [SUCCESS] {message}")

    def warning(self, message: str):
        """警告日志"""
        if self.log_level == LogLevel.SIMPLE:
            print(f"⚠️ {message}")
        else:
            self.logger.warning(f"⚠️ [WARNING] {message}")

    def debug(self, message: str):
        """调试日志 - 只在详细模式下显示"""
        if self.log_level == LogLevel.DETAILED:
            self.logger.debug(f"🔍 [DEBUG] {message}")

    def llm_request(self, provider: str, message: str, session_id: str):
        """LLM请求日志"""
        if self.log_level == LogLevel.DETAILED:
            print(f"   🧠 正在调用LLM服务...")
            print(f"      Provider: {provider}")
            print(f"      Message: {message[:50]}...")
            print(f"      Session: {session_id}")

    def proxy_status(self, enabled: bool, proxy_url: str = None):
        """代理状态日志"""
        if self.log_level == LogLevel.DETAILED:
            if enabled and proxy_url:
                print(f"   🌐 代理状态: 启用 ({proxy_url})")
            else:
                print(f"   🌐 代理状态: 禁用")

    def database_operation(self, operation: str, details: str = ""):
        """数据库操作日志"""
        if self.log_level == LogLevel.DETAILED:
            print(f"   💾 数据库操作: {operation} {details}")

    def websocket_event(self, event: str, details: str = ""):
        """WebSocket事件日志"""
        if self.log_level == LogLevel.DETAILED:
            print(f"   📡 WebSocket事件: {event} {details}")

    def chat_complete(self, session_id: str):
        """聊天完成日志 - 简单模式下不显示"""
        if self.log_level == LogLevel.DETAILED:
            print(f"   ✨ [CHAT COMPLETE] 聊天请求处理完成\n")

    def polling_request(self, session_id: str, token_type: str = "unknown"):
        """轮询请求日志 - 只在启用轮询日志时显示"""
        if self.show_polling:
            if self.log_level == LogLevel.SIMPLE:
                print(f"🔄 [{self._format_time()}] 轮询: {session_id[:12]}...")
            else:
                print(f"🔄 [POLLING] 轮询请求: {session_id}, token: {token_type}")

    def is_polling_enabled(self) -> bool:
        """检查是否启用轮询日志"""
        return self.show_polling

# 创建全局日志器实例
logger = ChatLogger()

# 便捷函数
def set_log_level(level: str):
    """设置日志级别"""
    global logger
    logger.log_level = LogLevel(level)
    logger.setup_logger()

def get_log_level() -> str:
    """获取当前日志级别"""
    return logger.log_level.value
