#!/usr/bin/env python3
"""
统一产品管理组件
合并产品目录、产品数据和购买渠道为一个统一的产品管理界面
每个产品都有完整的信息：基本信息、购买渠道、竞品对比、认证证书、客户数据
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QTextEdit, QComboBox, QGroupBox,
                            QFormLayout, QScrollArea, QListWidget, QListWidgetItem,
                            QPushButton, QInputDialog, QMessageBox, QCheckBox,
                            QTreeWidget, QTreeWidgetItem, QSplitter, QTabWidget,
                            QSpinBox, QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt6.QtCore import pyqtSignal, Qt
from PyQt6.QtGui import QFont


class ProductManagementWidget(QWidget):
    """统一产品管理组件"""
    
    config_changed = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.current_product = None
        self.products_data = {}
        self.init_ui()
        self.load_default_products()
    
    def init_ui(self):
        """初始化界面"""
        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 主容器
        main_widget = QWidget()
        layout = QVBoxLayout(main_widget)
        
        # 说明信息
        help_label = QLabel("""
💡 产品管理说明:
• 统一管理所有产品的完整信息：基本信息、购买渠道、竞品对比、认证证书、客户数据
• 每个产品都有独立的ASIN、链接和配置，支持多平台销售
• 所有产品信息将用于AI销售助手的异议处理和产品推荐
        """)
        help_label.setStyleSheet("color: #666; font-size: 11px; background: #f5f5f5; padding: 10px; border-radius: 5px;")
        layout.addWidget(help_label)
        help_label.setMaximumHeight(100)  # 限制最大高度，使其更紧凑
        
        # 创建主分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：产品列表
        products_group = QGroupBox("📦 产品列表")
        products_layout = QVBoxLayout(products_group)
        
        self.products_list = QListWidget()
        self.products_list.currentItemChanged.connect(self.on_product_selected)
        products_layout.addWidget(self.products_list)
        
        # 产品操作按钮
        product_btn_layout = QHBoxLayout()
        self.add_product_btn = QPushButton("➕ 添加产品")
        self.add_product_btn.clicked.connect(self.add_product)
        product_btn_layout.addWidget(self.add_product_btn)
        
        self.remove_product_btn = QPushButton("➖ 删除产品")
        self.remove_product_btn.clicked.connect(self.remove_product)
        product_btn_layout.addWidget(self.remove_product_btn)
        
        product_btn_layout.addStretch()
        products_layout.addLayout(product_btn_layout)
        
        main_splitter.addWidget(products_group)
        
        # 右侧：产品详情标签页
        self.product_tabs = QTabWidget()
        
        # 基本信息标签页
        basic_tab = self.create_basic_info_tab()
        self.product_tabs.addTab(basic_tab, "📋 基本信息")
        
        # 购买渠道标签页
        channels_tab = self.create_purchase_channels_tab()
        self.product_tabs.addTab(channels_tab, "🔗 购买渠道")
        
        # 竞品对比标签页
        comparison_tab = self.create_competitor_comparison_tab()
        self.product_tabs.addTab(comparison_tab, "🏆 竞品对比")
        
        # 认证证书标签页
        certifications_tab = self.create_certifications_tab()
        self.product_tabs.addTab(certifications_tab, "🏅 认证证书")
        
        # 客户数据标签页
        customer_tab = self.create_customer_data_tab()
        self.product_tabs.addTab(customer_tab, "⭐ 客户数据")
        
        main_splitter.addWidget(self.product_tabs)
        main_splitter.setSizes([300, 900])
        
        layout.addWidget(main_splitter)
        
        # 设置滚动区域
        scroll.setWidget(main_widget)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(scroll)
    
    def create_basic_info_tab(self):
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 基本产品信息
        basic_group = QGroupBox("📋 基本产品信息")
        basic_layout = QFormLayout(basic_group)
        
        self.product_id_edit = QLineEdit()
        self.product_id_edit.textChanged.connect(self.on_product_data_changed)
        basic_layout.addRow("产品ID:", self.product_id_edit)
        
        self.product_name_edit = QLineEdit()
        self.product_name_edit.textChanged.connect(self.on_product_data_changed)
        basic_layout.addRow("产品名称:", self.product_name_edit)
        
        self.product_category_combo = QComboBox()
        self.product_category_combo.setEditable(True)  # 允许手动输入
        self.product_category_combo.addItems(["Air Purifier", "Dehumidifier", "Humidifier", "Air Quality Monitor", "HVAC System", "Air Conditioner"])
        self.product_category_combo.currentTextChanged.connect(self.on_product_data_changed)
        basic_layout.addRow("产品类别:", self.product_category_combo)
        
        self.product_price_edit = QLineEdit()
        self.product_price_edit.textChanged.connect(self.on_product_data_changed)
        basic_layout.addRow("价格:", self.product_price_edit)
        
        self.product_description_edit = QTextEdit()
        self.product_description_edit.setMaximumHeight(80)
        self.product_description_edit.textChanged.connect(self.on_product_data_changed)
        basic_layout.addRow("产品描述:", self.product_description_edit)
        
        layout.addWidget(basic_group)
        
        # 技术规格
        tech_group = QGroupBox("⚙️ 技术规格")
        tech_layout = QFormLayout(tech_group)
        
        self.cadr_rating_edit = QLineEdit()
        self.cadr_rating_edit.textChanged.connect(self.on_product_data_changed)
        tech_layout.addRow("CADR评级:", self.cadr_rating_edit)
        
        self.coverage_area_edit = QLineEdit()
        self.coverage_area_edit.textChanged.connect(self.on_product_data_changed)
        tech_layout.addRow("覆盖面积:", self.coverage_area_edit)
        
        self.filter_type_edit = QLineEdit()
        self.filter_type_edit.textChanged.connect(self.on_product_data_changed)
        tech_layout.addRow("过滤类型:", self.filter_type_edit)
        
        self.noise_level_edit = QLineEdit()
        self.noise_level_edit.textChanged.connect(self.on_product_data_changed)
        tech_layout.addRow("噪音水平:", self.noise_level_edit)
        
        self.power_consumption_edit = QLineEdit()
        self.power_consumption_edit.textChanged.connect(self.on_product_data_changed)
        tech_layout.addRow("功耗:", self.power_consumption_edit)
        
        self.dimensions_edit = QLineEdit()
        self.dimensions_edit.textChanged.connect(self.on_product_data_changed)
        tech_layout.addRow("尺寸:", self.dimensions_edit)
        
        self.weight_edit = QLineEdit()
        self.weight_edit.textChanged.connect(self.on_product_data_changed)
        tech_layout.addRow("重量:", self.weight_edit)
        
        self.warranty_edit = QLineEdit()
        self.warranty_edit.textChanged.connect(self.on_product_data_changed)
        tech_layout.addRow("保修期:", self.warranty_edit)
        
        layout.addWidget(tech_group)
        
        return widget
    
    def create_purchase_channels_tab(self):
        """创建购买渠道标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 购买渠道表格
        channels_group = QGroupBox("🔗 购买渠道配置")
        channels_layout = QVBoxLayout(channels_group)
        
        # 渠道表格
        self.channels_table = QTableWidget()
        self.channels_table.setColumnCount(5)
        self.channels_table.setHorizontalHeaderLabels(["平台", "产品ID/ASIN", "产品链接", "价格", "特殊说明"])
        self.channels_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        channels_layout.addWidget(self.channels_table)
        
        # 渠道操作按钮
        channels_btn_layout = QHBoxLayout()
        self.add_channel_btn = QPushButton("➕ 添加渠道")
        self.add_channel_btn.clicked.connect(self.add_purchase_channel)
        channels_btn_layout.addWidget(self.add_channel_btn)
        
        self.remove_channel_btn = QPushButton("➖ 删除渠道")
        self.remove_channel_btn.clicked.connect(self.remove_purchase_channel)
        channels_btn_layout.addWidget(self.remove_channel_btn)
        
        self.load_default_channels_btn = QPushButton("🔄 加载默认渠道")
        self.load_default_channels_btn.clicked.connect(self.load_default_channels_for_product)
        channels_btn_layout.addWidget(self.load_default_channels_btn)
        
        channels_btn_layout.addStretch()
        channels_layout.addLayout(channels_btn_layout)
        
        layout.addWidget(channels_group)
        
        return widget
    
    def create_competitor_comparison_tab(self):
        """创建竞品对比标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 竞品对比表格
        comparison_group = QGroupBox("🏆 竞品对比")
        comparison_layout = QVBoxLayout(comparison_group)
        
        # 对比表格
        self.comparison_table = QTableWidget()
        self.comparison_table.setColumnCount(4)
        self.comparison_table.setHorizontalHeaderLabels(["规格", "我们的产品", "竞品A", "竞品B"])
        self.comparison_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        comparison_layout.addWidget(self.comparison_table)
        
        # 对比操作按钮
        comparison_btn_layout = QHBoxLayout()
        self.add_comparison_btn = QPushButton("➕ 添加对比项")
        self.add_comparison_btn.clicked.connect(self.add_comparison_row)
        comparison_btn_layout.addWidget(self.add_comparison_btn)
        
        self.remove_comparison_btn = QPushButton("➖ 删除对比项")
        self.remove_comparison_btn.clicked.connect(self.remove_comparison_row)
        comparison_btn_layout.addWidget(self.remove_comparison_btn)
        
        comparison_btn_layout.addStretch()
        comparison_layout.addLayout(comparison_btn_layout)
        
        layout.addWidget(comparison_group)
        
        return widget
    
    def create_certifications_tab(self):
        """创建认证证书标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 认证列表
        cert_group = QGroupBox("🏅 产品认证")
        cert_layout = QVBoxLayout(cert_group)
        
        self.certifications_list = QListWidget()
        cert_layout.addWidget(self.certifications_list)
        
        cert_btn_layout = QHBoxLayout()
        self.add_cert_btn = QPushButton("➕ 添加认证")
        self.add_cert_btn.clicked.connect(self.add_certification)
        cert_btn_layout.addWidget(self.add_cert_btn)
        
        self.remove_cert_btn = QPushButton("➖ 删除认证")
        self.remove_cert_btn.clicked.connect(self.remove_certification)
        cert_btn_layout.addWidget(self.remove_cert_btn)
        
        cert_btn_layout.addStretch()
        cert_layout.addLayout(cert_btn_layout)
        
        layout.addWidget(cert_group)
        
        return widget
    
    def create_customer_data_tab(self):
        """创建客户数据标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 评价统计
        rating_group = QGroupBox("⭐ 客户评价统计")
        rating_layout = QFormLayout(rating_group)
        
        self.customer_rating_edit = QLineEdit()
        self.customer_rating_edit.textChanged.connect(self.on_product_data_changed)
        rating_layout.addRow("平均评分:", self.customer_rating_edit)
        
        self.review_count_edit = QLineEdit()
        self.review_count_edit.textChanged.connect(self.on_product_data_changed)
        rating_layout.addRow("评价数量:", self.review_count_edit)
        
        self.satisfaction_rate_edit = QLineEdit()
        self.satisfaction_rate_edit.textChanged.connect(self.on_product_data_changed)
        rating_layout.addRow("满意度:", self.satisfaction_rate_edit)
        
        layout.addWidget(rating_group)
        
        # 客户好评要点
        praise_group = QGroupBox("👍 客户好评要点")
        praise_layout = QVBoxLayout(praise_group)
        
        self.praise_points_list = QListWidget()
        praise_layout.addWidget(self.praise_points_list)
        
        praise_btn_layout = QHBoxLayout()
        self.add_praise_btn = QPushButton("➕ 添加好评点")
        self.add_praise_btn.clicked.connect(self.add_praise_point)
        praise_btn_layout.addWidget(self.add_praise_btn)
        
        self.remove_praise_btn = QPushButton("➖ 删除好评点")
        self.remove_praise_btn.clicked.connect(self.remove_praise_point)
        praise_btn_layout.addWidget(self.remove_praise_btn)
        
        praise_btn_layout.addStretch()
        praise_layout.addLayout(praise_btn_layout)
        
        layout.addWidget(praise_group)
        
        return widget

    def load_default_products(self):
        """加载默认产品（从配置文件读取）"""
        # 产品数据现在从统一配置文件 advanced_sales_config.json 中读取
        # 如果没有配置文件，显示空的产品列表
        default_products = {}

        # 加载产品到列表
        for product_id, product_data in default_products.items():
            self.products_data[product_id] = product_data
            item = QListWidgetItem(f"{product_id} - {product_data['name']}")
            item.setData(Qt.ItemDataRole.UserRole, product_id)
            self.products_list.addItem(item)

        # 默认选择第一个产品
        if self.products_list.count() > 0:
            self.products_list.setCurrentRow(0)

    def on_product_selected(self, current, previous):
        """选择产品时的处理"""
        if current:
            product_id = current.data(Qt.ItemDataRole.UserRole)
            if product_id in self.products_data:
                self.current_product = product_id
                self.load_product_data(self.products_data[product_id])

    def load_product_data(self, product_data):
        """加载产品数据到界面"""
        # 临时断开信号连接，避免在加载时触发变更事件
        self._disconnect_signals()

        try:
            # 加载基本信息
            self.product_id_edit.setText(product_data.get('id', ''))
            self.product_name_edit.setText(product_data.get('name', ''))
            self.product_category_combo.setCurrentText(product_data.get('category', 'Air Purifier'))
            self.product_price_edit.setText(product_data.get('price', ''))
            self.product_description_edit.setPlainText(product_data.get('description', ''))

            # 加载技术规格
            self.cadr_rating_edit.setText(product_data.get('cadr_rating', ''))
            self.coverage_area_edit.setText(product_data.get('coverage_area', ''))
            self.filter_type_edit.setText(product_data.get('filter_type', ''))
            self.noise_level_edit.setText(product_data.get('noise_level', ''))
            self.power_consumption_edit.setText(product_data.get('power_consumption', ''))
            self.dimensions_edit.setText(product_data.get('dimensions', ''))
            self.weight_edit.setText(product_data.get('weight', ''))
            self.warranty_edit.setText(product_data.get('warranty', ''))

            # 加载客户数据
            self.customer_rating_edit.setText(product_data.get('rating', ''))
            self.review_count_edit.setText(product_data.get('review_count', ''))
            self.satisfaction_rate_edit.setText(product_data.get('satisfaction_rate', ''))

            # 加载购买渠道
            self.load_channels_data(product_data.get('channels', []))

            # 加载竞品对比
            self.load_comparison_data(product_data.get('comparison', []))

            # 加载认证
            self.load_certifications_data(product_data.get('certifications', []))

            # 加载好评要点
            self.load_praise_points_data(product_data.get('praise_points', []))
        finally:
            # 重新连接信号
            self._connect_signals()

    def _disconnect_signals(self):
        """断开信号连接"""
        try:
            self.product_id_edit.textChanged.disconnect()
            self.product_name_edit.textChanged.disconnect()
            self.product_category_combo.currentTextChanged.disconnect()
            self.product_price_edit.textChanged.disconnect()
            self.product_description_edit.textChanged.disconnect()
            self.cadr_rating_edit.textChanged.disconnect()
            self.coverage_area_edit.textChanged.disconnect()
            self.filter_type_edit.textChanged.disconnect()
            self.noise_level_edit.textChanged.disconnect()
            self.power_consumption_edit.textChanged.disconnect()
            self.dimensions_edit.textChanged.disconnect()
            self.weight_edit.textChanged.disconnect()
            self.warranty_edit.textChanged.disconnect()
            self.customer_rating_edit.textChanged.disconnect()
            self.review_count_edit.textChanged.disconnect()
            self.satisfaction_rate_edit.textChanged.disconnect()
        except:
            pass  # 忽略断开连接时的错误

    def _connect_signals(self):
        """连接信号"""
        self.product_id_edit.textChanged.connect(self.on_product_data_changed)
        self.product_name_edit.textChanged.connect(self.on_product_data_changed)
        self.product_category_combo.currentTextChanged.connect(self.on_product_data_changed)
        self.product_price_edit.textChanged.connect(self.on_product_data_changed)
        self.product_description_edit.textChanged.connect(self.on_product_data_changed)
        self.cadr_rating_edit.textChanged.connect(self.on_product_data_changed)
        self.coverage_area_edit.textChanged.connect(self.on_product_data_changed)
        self.filter_type_edit.textChanged.connect(self.on_product_data_changed)
        self.noise_level_edit.textChanged.connect(self.on_product_data_changed)
        self.power_consumption_edit.textChanged.connect(self.on_product_data_changed)
        self.dimensions_edit.textChanged.connect(self.on_product_data_changed)
        self.weight_edit.textChanged.connect(self.on_product_data_changed)
        self.warranty_edit.textChanged.connect(self.on_product_data_changed)
        self.customer_rating_edit.textChanged.connect(self.on_product_data_changed)
        self.review_count_edit.textChanged.connect(self.on_product_data_changed)
        self.satisfaction_rate_edit.textChanged.connect(self.on_product_data_changed)

    def load_channels_data(self, channels_data):
        """加载购买渠道数据"""
        self.channels_table.setRowCount(len(channels_data))

        for row, channel in enumerate(channels_data):
            if isinstance(channel, list) and len(channel) >= 5:
                for col, value in enumerate(channel[:5]):
                    item = QTableWidgetItem(str(value))
                    self.channels_table.setItem(row, col, item)

    def load_comparison_data(self, comparison_data):
        """加载竞品对比数据"""
        self.comparison_table.setRowCount(len(comparison_data))

        for row, data in enumerate(comparison_data):
            if isinstance(data, list) and len(data) >= 4:
                for col, value in enumerate(data[:4]):
                    item = QTableWidgetItem(str(value))
                    self.comparison_table.setItem(row, col, item)

    def load_certifications_data(self, certifications):
        """加载认证数据"""
        self.certifications_list.clear()
        for cert in certifications:
            self.certifications_list.addItem(cert)

    def load_praise_points_data(self, praise_points):
        """加载好评要点数据"""
        self.praise_points_list.clear()
        for point in praise_points:
            self.praise_points_list.addItem(point)

    def on_product_data_changed(self):
        """产品数据改变时的处理"""
        if self.current_product:
            self.save_current_product_data()
            self.config_changed.emit()

    def save_current_product_data(self):
        """保存当前产品数据"""
        if not self.current_product:
            return

        # 保存基本信息
        product_data = {
            'id': self.product_id_edit.text(),
            'name': self.product_name_edit.text(),
            'category': self.product_category_combo.currentText(),
            'price': self.product_price_edit.text(),
            'description': self.product_description_edit.toPlainText(),
            'cadr_rating': self.cadr_rating_edit.text(),
            'coverage_area': self.coverage_area_edit.text(),
            'filter_type': self.filter_type_edit.text(),
            'noise_level': self.noise_level_edit.text(),
            'power_consumption': self.power_consumption_edit.text(),
            'dimensions': self.dimensions_edit.text(),
            'weight': self.weight_edit.text(),
            'warranty': self.warranty_edit.text(),
            'rating': self.customer_rating_edit.text(),
            'review_count': self.review_count_edit.text(),
            'satisfaction_rate': self.satisfaction_rate_edit.text(),
            'channels': self.get_channels_data(),
            'comparison': self.get_comparison_data(),
            'certifications': self.get_certifications_data(),
            'praise_points': self.get_praise_points_data()
        }

        self.products_data[self.current_product] = product_data

    def get_channels_data(self):
        """获取购买渠道数据"""
        channels = []
        for row in range(self.channels_table.rowCount()):
            channel = []
            for col in range(self.channels_table.columnCount()):
                item = self.channels_table.item(row, col)
                channel.append(item.text() if item else "")
            channels.append(channel)
        return channels

    def get_comparison_data(self):
        """获取竞品对比数据"""
        comparisons = []
        for row in range(self.comparison_table.rowCount()):
            comparison = []
            for col in range(self.comparison_table.columnCount()):
                item = self.comparison_table.item(row, col)
                comparison.append(item.text() if item else "")
            comparisons.append(comparison)
        return comparisons

    def get_certifications_data(self):
        """获取认证数据"""
        certifications = []
        for i in range(self.certifications_list.count()):
            certifications.append(self.certifications_list.item(i).text())
        return certifications

    def get_praise_points_data(self):
        """获取好评要点数据"""
        praise_points = []
        for i in range(self.praise_points_list.count()):
            praise_points.append(self.praise_points_list.item(i).text())
        return praise_points

    def load_products_data(self, products_data):
        """加载产品数据（支持新的配置格式）"""
        print(f"🔍 开始加载产品数据，数据类型: {type(products_data)}")
        if products_data:
            print(f"📊 产品数据包含 {len(products_data)} 个产品: {list(products_data.keys())}")
            # 如果是新的配置格式（包含basic_info等子结构）
            if isinstance(products_data, dict) and any('basic_info' in v for v in products_data.values() if isinstance(v, dict)):
                print("🔄 检测到新配置格式，开始转换...")
                # 转换新格式到内部格式
                converted_data = {}
                for product_id, product_config in products_data.items():
                    if isinstance(product_config, dict):
                        basic_info = product_config.get('basic_info', {})
                        tech_specs = product_config.get('technical_specs', {})
                        customer_data = product_config.get('customer_data', {})
                        channels = product_config.get('purchase_channels', {})
                        competitor_comparison = product_config.get('competitor_comparison', [])
                        certifications = product_config.get('certifications', [])

                        # 转换购买渠道格式
                        channels_list = []
                        for channel_key, channel_data in channels.items():
                            if isinstance(channel_data, dict):
                                channels_list.append([
                                    channel_data.get('name', channel_key),
                                    channel_data.get('asin', channel_data.get('product_id', '')),
                                    channel_data.get('url', ''),
                                    channel_data.get('price', ''),
                                    ', '.join(channel_data.get('benefits', []))
                                ])

                        # 转换竞品对比格式
                        comparison_list = []
                        for comp in competitor_comparison:
                            if isinstance(comp, dict):
                                comparison_list.append([
                                    comp.get('feature', ''),
                                    comp.get('our_product', ''),
                                    comp.get('competitor_a', ''),
                                    comp.get('competitor_b', '')
                                ])

                        converted_data[product_id] = {
                            'id': basic_info.get('id', product_id),
                            'name': basic_info.get('name', ''),
                            'category': basic_info.get('category', 'Air Purifier'),
                            'price': basic_info.get('price', ''),
                            'description': basic_info.get('description', ''),
                            'cadr_rating': tech_specs.get('efficiency', ''),
                            'coverage_area': tech_specs.get('coverage_area', ''),
                            'filter_type': tech_specs.get('filter_type', ''),
                            'noise_level': tech_specs.get('noise_level', ''),
                            'power_consumption': tech_specs.get('power_consumption', ''),
                            'dimensions': tech_specs.get('dimensions', ''),
                            'weight': tech_specs.get('weight', ''),
                            'warranty': tech_specs.get('warranty', ''),
                            'rating': customer_data.get('rating', ''),
                            'review_count': customer_data.get('review_count', ''),
                            'satisfaction_rate': customer_data.get('satisfaction_rate', ''),
                            'channels': channels_list,
                            'comparison': comparison_list,
                            'certifications': certifications,
                            'praise_points': customer_data.get('praise_points', [])
                        }

                self.products_data = converted_data
                print(f"✅ 新格式转换完成，转换了 {len(converted_data)} 个产品")
            else:
                # 旧格式或已转换格式
                print("📋 使用现有格式，无需转换")
                self.products_data = products_data

            # 重新加载产品列表
            self.products_list.clear()
            for product_id, product_data in self.products_data.items():
                name = product_data.get('name', 'Unknown')
                item = QListWidgetItem(f"{product_id} - {name}")
                item.setData(Qt.ItemDataRole.UserRole, product_id)
                self.products_list.addItem(item)

            # 默认选择第一个产品
            if self.products_list.count() > 0:
                self.products_list.setCurrentRow(0)
        else:
            print("⚠️ 没有产品数据可加载")

    # 添加/删除方法
    def add_product(self):
        """添加产品"""
        text, ok = QInputDialog.getText(self, "添加产品", "请输入产品ID:")
        if ok and text:
            if text not in self.products_data:
                # 创建新产品数据
                new_product = {
                    "id": text,
                    "name": f"New Product {text}",
                    "category": "Air Purifier",
                    "price": "$0",
                    "description": "",
                    "channels": [],
                    "comparison": [],
                    "certifications": [],
                    "praise_points": []
                }

                self.products_data[text] = new_product
                item = QListWidgetItem(f"{text} - {new_product['name']}")
                item.setData(Qt.ItemDataRole.UserRole, text)
                self.products_list.addItem(item)
                self.products_list.setCurrentItem(item)
                self.config_changed.emit()
            else:
                QMessageBox.warning(self, "警告", "产品ID已存在！")

    def remove_product(self):
        """删除产品"""
        current_item = self.products_list.currentItem()
        if current_item:
            product_id = current_item.data(Qt.ItemDataRole.UserRole)
            reply = QMessageBox.question(self, "确认删除", f"确定要删除产品 {product_id} 吗？")
            if reply == QMessageBox.StandardButton.Yes:
                del self.products_data[product_id]
                self.products_list.takeItem(self.products_list.row(current_item))
                self.config_changed.emit()

    def add_purchase_channel(self):
        """添加购买渠道"""
        row = self.channels_table.rowCount()
        self.channels_table.insertRow(row)

        # 设置默认值
        default_values = ["新平台", "产品ID", "产品链接", "价格", "特殊说明"]
        for col, value in enumerate(default_values):
            item = QTableWidgetItem(value)
            self.channels_table.setItem(row, col, item)

        self.config_changed.emit()

    def remove_purchase_channel(self):
        """删除购买渠道"""
        current_row = self.channels_table.currentRow()
        if current_row >= 0:
            self.channels_table.removeRow(current_row)
            self.config_changed.emit()

    def load_default_channels_for_product(self):
        """为当前产品加载渠道（从配置文件读取）"""
        # 移除硬编码，渠道数据现在从配置文件加载
        # 如果需要默认渠道，应该在配置文件中定义
        pass

    def add_comparison_row(self):
        """添加竞品对比行"""
        row = self.comparison_table.rowCount()
        self.comparison_table.insertRow(row)

        # 设置默认值
        default_values = ["新规格", "我们的产品", "竞品A", "竞品B"]
        for col, value in enumerate(default_values):
            item = QTableWidgetItem(value)
            self.comparison_table.setItem(row, col, item)

        self.config_changed.emit()

    def remove_comparison_row(self):
        """删除竞品对比行"""
        current_row = self.comparison_table.currentRow()
        if current_row >= 0:
            self.comparison_table.removeRow(current_row)
            self.config_changed.emit()

    def add_certification(self):
        """添加认证"""
        text, ok = QInputDialog.getText(self, "添加认证", "请输入认证名称:")
        if ok and text:
            self.certifications_list.addItem(text)
            self.config_changed.emit()

    def remove_certification(self):
        """删除认证"""
        current_item = self.certifications_list.currentItem()
        if current_item:
            self.certifications_list.takeItem(self.certifications_list.row(current_item))
            self.config_changed.emit()

    def add_praise_point(self):
        """添加好评点"""
        text, ok = QInputDialog.getText(self, "添加好评点", "请输入好评要点:")
        if ok and text:
            self.praise_points_list.addItem(text)
            self.config_changed.emit()

    def remove_praise_point(self):
        """删除好评点"""
        current_item = self.praise_points_list.currentItem()
        if current_item:
            self.praise_points_list.takeItem(self.praise_points_list.row(current_item))
            self.config_changed.emit()

    def get_config_data(self):
        """获取配置数据"""
        # 保存当前编辑的产品数据
        if self.current_product:
            self.save_current_product_data()

        return self.products_data

    def get_config(self):
        """获取当前配置（与get_config_data相同，保持接口一致性）"""
        return self.get_config_data()

    def get_current_product_data(self):
        """获取当前选中的产品数据"""
        current_item = self.products_list.currentItem()
        if current_item:
            product_id = current_item.data(Qt.ItemDataRole.UserRole)
            if product_id in self.products_data:
                # 先保存当前编辑的数据
                self.save_current_product_data()
                return self.products_data[product_id]
        return {}
