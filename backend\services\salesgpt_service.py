"""
配置驱动的SalesGPT服务
集成长期记忆、表单收集和智能销售流程
"""

import os
import json
import logging
import csv
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from .conversation_rules_service import conversation_rules_service
from .escalation_service import escalation_service
from datetime import datetime
import asyncio
try:
    from ..utils.path_utils import get_history_directory
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from utils.path_utils import get_history_directory

# Setup logger first
logger = logging.getLogger(__name__)

# LangChain imports for custom SalesGPT implementation
try:
    from langchain_google_genai import ChatGoogleGenerativeAI
    from langchain_openai import ChatOpenAI
    from langchain.schema import HumanMessage, AIMessage, SystemMessage
    LANGCHAIN_AVAILABLE = True
except ImportError as e:
    logger.warning(f"LangChain not available: {e}")
    LANGCHAIN_AVAILABLE = False

# Local imports
from .database import DatabaseService
from .memory_service import get_memory_service
from .form_service import get_form_service

class SalesGPTService:
    """配置驱动的SalesGPT服务类"""

    def __init__(self, db_service=None):
        logger.debug("🔍 SalesGPTService.__init__ 开始")
        self.db_service = db_service  # 接受外部传入的数据库服务实例
        self.customer_service = None  # 将在需要时初始化
        self.conversation_stages = {}   # 存储每个会话的销售阶段

        logger.debug("🔍 初始化记忆服务")
        # 传递数据库服务实例给记忆服务，避免重复初始化
        self.memory_service = get_memory_service(db_service)

        logger.debug("🔍 初始化表单服务")
        self.form_service = get_form_service()

        self.sales_config = {}
        logger.debug("🔍 加载环境变量配置")
        self.load_config()  # 先加载环境变量配置

        logger.debug("🔍 加载销售配置")
        self.load_sales_config()

        logger.debug("🔍 设置LLM")
        self.setup_llm()

        logger.debug("🔍 生成产品目录")
        self.generate_product_catalog_from_config()

        # 🔧 设置conversation_rules_service的引用，用于CSV导出
        conversation_rules_service.set_salesgpt_service(self)

        logger.debug("🔍 SalesGPTService.__init__ 完成")

    def load_sales_config(self):
        """加载销售配置文件，支持环境变量覆盖"""
        try:
            import os
            config_path = os.path.join(os.path.dirname(__file__), '..', 'salesgpt_config', 'advanced_sales_config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                self.sales_config = json.load(f)

            # 从配置文件读取Agent信息
            agent_profile = self.sales_config.get('agent_profile', {})

            # 支持环境变量覆盖配置文件设置
            self.salesperson_name = os.getenv('OVERRIDE_AGENT_NAME') or agent_profile.get('name', self.default_agent_name)
            self.salesperson_role = os.getenv('OVERRIDE_AGENT_ROLE') or agent_profile.get('role', self.default_agent_role)
            self.company_name = os.getenv('OVERRIDE_COMPANY_NAME') or agent_profile.get('company', self.default_company_name)
            self.company_website = os.getenv('OVERRIDE_COMPANY_WEBSITE') or agent_profile.get('website', self.default_company_website)

            # 业务描述支持环境变量覆盖
            env_business = os.getenv('OVERRIDE_COMPANY_BUSINESS')
            if env_business:
                self.company_business = env_business
            else:
                self.company_business = agent_profile.get('business_description', '')
                if not self.company_business and self.default_company_business:
                    self.company_business = self.default_company_business

            # 从配置文件更新销售阶段
            sales_process = self.sales_config.get('sales_process', {})
            if 'stages' in sales_process:
                self.sales_stages = {}
                for stage_name, stage_config in sales_process['stages'].items():
                    # 跳过注释字段
                    if stage_name.startswith('_comment'):
                        continue

                    # 确保 stage_config 是字典类型
                    if isinstance(stage_config, dict):
                        self.sales_stages[stage_name] = stage_config.get('objective', f"{stage_name} stage")
                    else:
                        # 如果不是字典，直接使用值作为描述
                        self.sales_stages[stage_name] = str(stage_config)

            logger.info(f"销售配置加载成功 - Agent: {self.salesperson_name}, 公司: {self.company_name}")

            # 记录是否使用了环境变量覆盖
            overrides = []
            if os.getenv('OVERRIDE_AGENT_NAME'): overrides.append('Agent名称')
            if os.getenv('OVERRIDE_COMPANY_NAME'): overrides.append('公司名称')
            if os.getenv('OVERRIDE_COMPANY_WEBSITE'): overrides.append('网站')
            if os.getenv('OVERRIDE_COMPANY_BUSINESS'): overrides.append('业务描述')

            if overrides:
                logger.info(f"环境变量覆盖生效: {', '.join(overrides)}")

        except Exception as e:
            logger.error(f"加载销售配置失败: {e}")
            # 使用默认配置
            self._load_default_config()

    def _load_default_config(self):
        """加载默认配置 - 从环境变量读取，完全消除硬编码"""
        # 从环境变量读取默认配置，如果未设置则使用最基本的默认值
        self.salesperson_name = self.default_agent_name
        self.salesperson_role = self.default_agent_role
        self.company_name = self.default_company_name

        # 构建公司业务描述
        if self.default_company_business:
            self.company_business = self.default_company_business
        else:
            # 如果环境变量未设置，构建基本的业务描述
            self.company_business = f"""{self.company_name} is a company that provides quality products and services.
            We are committed to customer satisfaction and excellence in our field.
            Visit our website at {self.default_company_website} for more information."""

        # 默认销售阶段
        self.sales_stages = {
            "introduction": f"Introduction: Start the conversation by introducing yourself and {self.company_name}.",
            "qualification": "Qualification: Qualify the prospect by confirming if they are interested in our products or services.",
            "value_proposition": f"Value proposition: Briefly explain how {self.company_name} products can benefit the prospect.",
            "needs_analysis": "Needs analysis: Ask questions to uncover the prospect's specific needs and pain points.",
            "solution_presentation": f"Solution presentation: Present {self.company_name} products that best fit the prospect's needs.",
            "objection_handling": "Objection handling: Address any concerns or objections the prospect might have.",
            "close": "Close: Ask for the sale or next steps.",
            "end": "End: The conversation is over."
        }

        logger.info(f"使用默认配置 - Agent: {self.salesperson_name}, 公司: {self.company_name}, 网站: {self.default_company_website}")

    def load_config(self):
        """加载环境变量配置"""
        # LLM提供商配置
        self.llm_provider = os.getenv("LLM_PROVIDER", "gemini").lower()

        # Gemini配置
        self.gemini_api_key = os.getenv("GEMINI_API_KEY")
        self.gemini_model = os.getenv("GEMINI_MODEL", "gemini-2.0-flash-lite")

        # OpenAI配置
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.openai_model = os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")

        # DeepSeek配置
        self.deepseek_api_key = os.getenv("DEEPSEEK_API_KEY")
        self.deepseek_model = os.getenv("DEEPSEEK_MODEL", "deepseek-chat")
        self.deepseek_base_url = os.getenv("DEEPSEEK_BASE_URL", "https://api.deepseek.com")

        # 代理配置
        self.proxy_enabled = os.getenv("PROXY_ENABLED", "false").lower() == "true"

        # 构建代理URL - 完全从.env文件读取，根据PROXY_TYPE选择配置
        if self.proxy_enabled:
            proxy_type = os.getenv("PROXY_TYPE")

            if not proxy_type:
                logger.warning("代理已启用但PROXY_TYPE未配置，禁用代理")
                self.proxy_enabled = False
                self.proxy_url = None
            elif proxy_type.lower() == "socks5":
                # 使用SOCKS5代理配置
                proxy_host = os.getenv("SOCKS5_HOST")
                proxy_port = os.getenv("SOCKS5_PORT")

                if not all([proxy_host, proxy_port]):
                    logger.warning("SOCKS5代理配置不完整，禁用代理")
                    logger.warning(f"SOCKS5_HOST: {proxy_host}, SOCKS5_PORT: {proxy_port}")
                    self.proxy_enabled = False
                    self.proxy_url = None
                else:
                    self.proxy_url = f"socks5h://{proxy_host}:{proxy_port}"
                    logger.info(f"SOCKS5代理配置成功: {self.proxy_url}")

            elif proxy_type.lower() == "http":
                # 使用HTTP代理配置
                proxy_host = os.getenv("HTTP_PROXY_HOST")
                proxy_port = os.getenv("HTTP_PROXY_PORT")

                if not all([proxy_host, proxy_port]):
                    logger.warning("HTTP代理配置不完整，禁用代理")
                    logger.warning(f"HTTP_PROXY_HOST: {proxy_host}, HTTP_PROXY_PORT: {proxy_port}")
                    self.proxy_enabled = False
                    self.proxy_url = None
                else:
                    self.proxy_url = f"http://{proxy_host}:{proxy_port}"
                    logger.info(f"HTTP代理配置成功: {self.proxy_url}")
            else:
                logger.warning(f"不支持的代理类型: {proxy_type}，禁用代理")
                self.proxy_enabled = False
                self.proxy_url = None
        else:
            self.proxy_url = None
            logger.info("代理已禁用")

        # 🔧 移除产品目录文件路径依赖 - 现在从配置文件动态生成
        logger.info("产品目录将从配置文件动态生成")

        # 加载默认配置环境变量
        self.default_agent_name = os.getenv("DEFAULT_AGENT_NAME", "Dylan")
        self.default_agent_role = os.getenv("DEFAULT_AGENT_ROLE", "Personal Assistant")
        self.default_company_name = os.getenv("DEFAULT_COMPANY_NAME", "KNKA Environmental Appliances")
        self.default_company_website = os.getenv("DEFAULT_COMPANY_WEBSITE", "knkalife.com")
        self.default_company_business = os.getenv("DEFAULT_COMPANY_BUSINESS", "")
        
    def generate_product_catalog_from_config(self):
        """从配置文件动态生成产品目录"""
        try:
            products = self.sales_config.get('products', {})
            if not products:
                self.product_catalog = "Product catalog not available."
                logger.warning("No products found in configuration")
                return

            # 生成产品目录文本
            catalog_lines = [
                "KNKA Environmental Appliances Product Catalog",
                "",
                "Company Information:",
                "KNKA (Keep Natural Keep Advancing) - Founded in 2005",
                f"Website: {self.company_website}",
                "Mission: To enhance everyday living through intelligent air solutions that are both effective and beautifully designed.",
                "Vision: To make every space a place where you can truly breathe.",
                "",
                "Core Values:",
                "- Purity: We prioritize clean air and healthy environments",
                "- Simplicity: Our products are easy to use and elegantly designed",
                "- Innovation: We continuously explore new technologies for better performance",
                "- Sustainability: We care about the planet and promote energy-efficient solutions",
                ""
            ]

            # 按产品类型分组
            dehumidifiers = []
            air_purifiers = []

            for product_id, product_data in products.items():
                product_type = product_data.get('type', '').lower()
                basic_info = product_data.get('basic_info', {})

                product_line = f"{product_id} - {basic_info.get('name', product_id)} - ${basic_info.get('price', 'N/A')}"
                if basic_info.get('description'):
                    product_line += f"\n   {basic_info.get('description')}"

                if 'dehumidifier' in product_type:
                    dehumidifiers.append(product_line)
                elif 'purifier' in product_type:
                    air_purifiers.append(product_line)
                else:
                    # 默认归类为空气净化器
                    air_purifiers.append(product_line)

            # 添加除湿器部分
            if dehumidifiers:
                catalog_lines.extend([
                    "DEHUMIDIFIERS:",
                    ""
                ])
                for i, product in enumerate(dehumidifiers, 1):
                    catalog_lines.append(f"{i}. {product}")
                catalog_lines.append("")

            # 添加空气净化器部分
            if air_purifiers:
                catalog_lines.extend([
                    "AIR PURIFIERS:",
                    ""
                ])
                for i, product in enumerate(air_purifiers, 1):
                    catalog_lines.append(f"{i}. {product}")

            self.product_catalog = "\n".join(catalog_lines)
            logger.info(f"Product catalog generated successfully from config ({len(products)} products)")

        except Exception as e:
            logger.error(f"Failed to generate product catalog from config: {e}")
            self.product_catalog = "Product catalog not available."

    def setup_llm(self):
        """根据LLM_PROVIDER配置设置LLM"""
        try:
            if not LANGCHAIN_AVAILABLE:
                logger.warning("LangChain not available, SalesGPT will use fallback responses")
                self.llm = None
                return

            # 延迟初始化LLM，避免启动时阻塞
            self.llm = None

            # 根据配置选择LLM提供商
            if self.llm_provider == "gemini":
                self.llm_config = {
                    "model": self.gemini_model,
                    "google_api_key": self.gemini_api_key,
                    "temperature": 0.7,
                    "max_tokens": 1000,
                    "convert_system_message_to_human": True,  # 修复Gemini SystemMessage问题
                }
                logger.info(f"SalesGPT service initialized with Gemini model: {self.gemini_model}")

            elif self.llm_provider == "openai":
                self.llm_config = {
                    "model": self.openai_model,
                    "openai_api_key": self.openai_api_key,
                    "temperature": 0.7,
                    "max_tokens": 1000,
                }
                logger.info(f"SalesGPT service initialized with OpenAI model: {self.openai_model}")

            elif self.llm_provider == "deepseek":
                self.llm_config = {
                    "model": self.deepseek_model,
                    "openai_api_key": self.deepseek_api_key,
                    "openai_api_base": self.deepseek_base_url,
                    "temperature": 0.7,
                    "max_tokens": 1000,
                }
                logger.info(f"SalesGPT service initialized with DeepSeek model: {self.deepseek_model}")

            else:
                logger.error(f"Unsupported LLM provider: {self.llm_provider}")
                raise ValueError(f"Unsupported LLM provider: {self.llm_provider}")

            if self.proxy_enabled and self.proxy_url:
                logger.info(f"Proxy enabled: {self.proxy_url}")

        except Exception as e:
            logger.error(f"Failed to initialize SalesGPT service: {e}")
            raise

    def get_llm(self):
        """延迟初始化并获取LLM实例"""
        if not LANGCHAIN_AVAILABLE:
            return None

        if self.llm is None:
            try:
                # 根据配置的LLM提供商初始化相应的LLM
                if self.llm_provider == "gemini":
                    logger.info("初始化Gemini LLM（代理将在调用时设置）")
                    self.llm = ChatGoogleGenerativeAI(**self.llm_config)
                    logger.info(f"Gemini LLM initialized with model: {self.gemini_model}")

                elif self.llm_provider == "openai":
                    logger.info("初始化OpenAI LLM（代理将在调用时设置）")
                    self.llm = ChatOpenAI(**self.llm_config)
                    logger.info(f"OpenAI LLM initialized with model: {self.openai_model}")

                elif self.llm_provider == "deepseek":
                    logger.info("初始化DeepSeek LLM（代理将在调用时设置）")
                    self.llm = ChatOpenAI(**self.llm_config)  # DeepSeek使用OpenAI兼容接口
                    logger.info(f"DeepSeek LLM initialized with model: {self.deepseek_model}")

                else:
                    raise ValueError(f"Unsupported LLM provider: {self.llm_provider}")

            except Exception as e:
                logger.error(f"Failed to initialize {self.llm_provider} LLM: {e}")
                raise
        return self.llm

    async def _call_llm_with_proxy(self, llm, messages):
        """使用httpx代理调用LLM（临时环境变量方法）"""
        original_env = {}
        proxy_vars = ['http_proxy', 'HTTP_PROXY', 'https_proxy', 'HTTPS_PROXY']

        try:
            # 如果启用代理，临时设置环境变量
            if self.proxy_enabled and self.proxy_url:
                proxy_type = os.getenv("PROXY_TYPE")

                # 保存原始环境变量
                for var in proxy_vars:
                    original_env[var] = os.environ.get(var)

                if proxy_type and proxy_type.lower() == "socks5":
                    # 使用SOCKS5代理配置
                    proxy_host = os.getenv("SOCKS5_HOST")
                    proxy_port = os.getenv("SOCKS5_PORT")
                    proxy_url = f"socks5h://{proxy_host}:{proxy_port}"
                    logger.debug(f"🔍 临时设置SOCKS5代理: {proxy_url}")
                elif proxy_type and proxy_type.lower() == "http":
                    # 使用HTTP代理配置
                    proxy_host = os.getenv("HTTP_PROXY_HOST")
                    proxy_port = os.getenv("HTTP_PROXY_PORT")
                    proxy_url = f"http://{proxy_host}:{proxy_port}"
                    logger.debug(f"🔍 临时设置HTTP代理: {proxy_url}")
                else:
                    # 回退到已配置的proxy_url
                    proxy_url = self.proxy_url
                    logger.debug(f"🔍 使用已配置的代理: {proxy_url}")

                # 设置代理环境变量
                for var in proxy_vars:
                    os.environ[var] = proxy_url

            # 调用LLM
            response = await llm.ainvoke(messages)
            return response

        except Exception as e:
            logger.error(f"🔍 代理调用失败: {e}")
            # 回退到直连
            response = await llm.ainvoke(messages)
            return response

        finally:
            # 恢复原始环境变量
            if self.proxy_enabled and self.proxy_url:
                for var in proxy_vars:
                    if original_env.get(var) is not None:
                        os.environ[var] = original_env[var]
                    elif var in os.environ:
                        del os.environ[var]
                logger.debug("🔍 代理环境变量已恢复")
    
    def get_conversation_stage(self, session_id: str) -> str:
        """获取当前对话阶段"""
        return self.conversation_stages.get(session_id, "introduction")

    def set_conversation_stage(self, session_id: str, stage: str):
        """设置对话阶段"""
        self.conversation_stages[session_id] = stage

    # 对话历史现在由memory_service管理，从数据库读取
    
    async def process_message(self, session_id: str, message: str, user_ip: str = None) -> Dict[str, Any]:
        """处理用户消息 - 主要接口，保持与原有API兼容"""
        try:
            # 记录用户消息
            logger.info(f"[{user_ip}] User message: {message}")
            logger.debug("🔍 SalesGPT.process_message 开始处理")

            # 0. 🔧 新增：检查对话规则（在所有其他处理之前）
            logger.debug("🔍 检查对话规则")
            rules_ok, rules_message = await self.check_conversation_rules(message, session_id)
            if not rules_ok:
                logger.info(f"Conversation rules violation for session {session_id}: {rules_message}")
                # 保存规则违反的对话记录
                await self.save_conversation(session_id, message, rules_message, user_ip)
                return {
                    "response": rules_message,
                    "session_id": session_id,
                    "timestamp": datetime.now().isoformat(),
                    "status": "rules_violation"
                }

            # 🔧 移除自动升级检查 - 客服介入完全由客服端手动控制
            # 不再有自动"转接人工客服"的逻辑，只有表单收集和手动客服介入

            # 1. 检查是否在表单流程中
            logger.debug("🔍 检查表单流程状态")
            if self.form_service.is_form_active(session_id):
                logger.debug("🔍 处理表单输入")
                response = self.form_service.process_form_input(session_id, message)
                await self.save_conversation(session_id, message, response, user_ip)
                return {
                    "response": response,
                    "session_id": session_id,
                    "timestamp": datetime.now().isoformat(),
                    "status": "form_collection"
                }

            # 2. 检查是否需要启动表单收集
            logger.debug("🔍 检查是否需要启动表单收集")
            should_form, category = self.form_service.should_trigger_form(message)
            if should_form:
                logger.debug("🔍 启动表单收集")
                response = self.form_service.initiate_form_collection(session_id, category)
                await self.save_conversation(session_id, message, response, user_ip)
                return {
                    "response": response,
                    "session_id": session_id,
                    "timestamp": datetime.now().isoformat(),
                    "status": "form_initiated"
                }

            # 3. 正常的销售流程处理
            logger.debug("🔍 开始SalesGPT响应处理")
            try:
                response = await self.get_salesgpt_response(session_id, message)
                logger.debug(f"🔍 SalesGPT响应完成: {response[:50] if response else 'None'}...")
            except Exception as e:
                logger.error(f"🔍 SalesGPT响应处理失败: {e}")
                import traceback
                logger.error(f"🔍 错误堆栈: {traceback.format_exc()}")
                raise

            # 4. 更新用户记忆
            logger.debug("🔍 更新用户记忆")
            try:
                self.memory_service.update_user_interaction(session_id, message, response)
                logger.debug("🔍 用户记忆更新完成")
            except Exception as e:
                logger.error(f"🔍 用户记忆更新失败: {e}")

            # 保存对话到数据库
            logger.debug("🔍 保存对话到数据库")
            try:
                await self.save_conversation(session_id, message, response, user_ip)
                logger.debug("🔍 对话保存完成")
            except Exception as e:
                logger.error(f"🔍 对话保存失败: {e}")

            # 记录AI响应
            logger.info(f"[{user_ip}] AI response: {response}")
            logger.debug("🔍 SalesGPT.process_message 处理完成")

            return {
                "response": response,
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "status": "success"
            }

        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return {
                "response": "I apologize, but I'm experiencing technical difficulties. Please try again later.",
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "status": "error",
                "error": str(e)
            }
    
    async def get_salesgpt_response(self, session_id: str, message: str) -> str:
        """获取SalesGPT响应"""
        try:
            logger.debug("🔍 get_salesgpt_response 开始")
            # 如果LangChain不可用，使用简单的回退响应
            if not LANGCHAIN_AVAILABLE:
                logger.debug("🔍 LangChain不可用，使用回退响应")
                return self.get_fallback_response(message)

            # 获取当前阶段
            logger.debug("🔍 获取当前阶段")
            try:
                current_stage = self.get_conversation_stage(session_id)
                logger.debug(f"🔍 当前阶段: {current_stage}")
            except Exception as e:
                logger.error(f"🔍 获取当前阶段失败: {e}")
                raise

            # 获取个性化上下文（从记忆系统）
            logger.debug("🔍 获取个性化上下文")
            try:
                personalization_context = await self.memory_service.get_personalization_context(session_id)
                logger.debug(f"🔍 个性化上下文长度: {len(personalization_context) if personalization_context else 0}")
            except Exception as e:
                logger.error(f"🔍 获取个性化上下文失败: {e}")
                raise

            # 获取最近对话历史（从数据库）
            logger.debug("🔍 获取最近对话历史")
            try:
                recent_history = await self.memory_service.get_recent_conversation_context(session_id, turns=10)
                logger.debug(f"🔍 对话历史条数: {len(recent_history) if recent_history else 0}")
            except Exception as e:
                logger.error(f"🔍 获取最近对话历史失败: {e}")
                raise

            # 构建系统提示（包含个性化信息）
            logger.debug("🔍 构建系统提示")
            try:
                system_prompt = self.build_system_prompt(current_stage, personalization_context)
                logger.debug(f"🔍 系统提示长度: {len(system_prompt) if system_prompt else 0}")
            except Exception as e:
                logger.error(f"🔍 构建系统提示失败: {e}")
                raise

            # 构建消息列表
            messages = [SystemMessage(content=system_prompt)]

            # 添加历史对话，确保消息序列正确
            last_message_type = "system"  # 跟踪最后一条消息类型

            for h in recent_history:
                if h["role"] == "user":
                    messages.append(HumanMessage(content=h["content"]))
                    last_message_type = "human"
                elif h["role"] == "ai":
                    # 确保AI消息前面有Human消息
                    if last_message_type == "system":
                        # 如果SystemMessage后面直接是AI消息，插入一个占位的Human消息
                        messages.append(HumanMessage(content="[Previous conversation context]"))
                    messages.append(AIMessage(content=h["content"]))
                    last_message_type = "ai"

            # 添加当前用户消息
            messages.append(HumanMessage(content=message))

            # 获取LLM响应（使用代理方法）
            logger.debug("🔍 开始调用Gemini LLM")
            llm = self.get_llm()
            logger.debug("🔍 LLM实例获取成功，开始API调用")

            # 使用临时代理方法调用LLM
            response = await self._call_llm_with_proxy(llm, messages)
            response_text = response.content
            logger.debug(f"🔍 Gemini API调用成功，响应长度: {len(response_text)}")

            # 分析并更新销售阶段
            new_stage = self.analyze_conversation_stage(message, response_text, current_stage)
            if new_stage != current_stage:
                self.set_conversation_stage(session_id, new_stage)
                logger.info(f"Session {session_id} stage changed: {current_stage} -> {new_stage}")

            return response_text

        except Exception as e:
            logger.error(f"SalesGPT response error: {e}")
            return "I apologize for the confusion. Could you please rephrase your question?"
    
    async def should_handover_to_human(self, message: str, session_id: str) -> bool:
        """判断是否需要转接人工客服 - 已禁用自动转接"""
        # 根据需求，移除自动转接逻辑
        # 人工客服介入完全由客服端手动控制
        return False
    
    async def initiate_human_handover(self, session_id: str, message: str, user_ip: str) -> Dict[str, Any]:
        """启动人工客服接管"""
        try:
            # 延迟导入客服系统以避免循环依赖
            if not self.customer_service:
                from .customer_service import customer_service
                self.customer_service = customer_service

            # 调用现有的客服系统
            handover_result = await self.customer_service.initiate_handover(
                session_id=session_id,
                user_message=message,
                user_ip=user_ip,
                context="SalesGPT handover"
            )
            
            return {
                "response": "I'm connecting you with our customer service team. Please wait a moment.",
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "status": "handover",
                "handover_id": handover_result.get("handover_id")
            }
            
        except Exception as e:
            logger.error(f"Human handover error: {e}")
            return {
                "response": "I'll connect you with our customer service team. Please use our contact form on the website for immediate assistance.",
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "status": "handover_fallback"
            }
    
    async def save_conversation(self, session_id: str, user_message: str, ai_response: str, user_ip: str = None):
        """保存对话到数据库"""
        try:
            # 延迟导入数据库服务以避免循环导入
            if not self.db_service:
                from .database import DatabaseService
                self.db_service = DatabaseService()

            # 注意：用户消息已经在main.py中保存，这里只保存AI响应
            await self.db_service.save_message(
                session_id=session_id,
                content=ai_response,
                sender="ai"
            )

        except Exception as e:
            logger.error(f"Failed to save conversation: {e}")
    
    def get_fallback_response(self, message: str) -> str:
        """当LangChain不可用时的回退响应"""
        message_lower = message.lower()

        if any(word in message_lower for word in ["hello", "hi", "hey", "你好"]):
            return f"Hello! I'm {self.salesperson_name}, your personal assistant at {self.company_name}. How can I help you with air quality solutions today?"
        elif any(word in message_lower for word in ["product", "dehumidifier", "air purifier", "产品"]):
            return "We offer premium dehumidifiers and air purifiers. Would you like to know more about our specific products?"
        elif any(word in message_lower for word in ["price", "cost", "价格"]):
            return "Our products are competitively priced for their quality. Would you like me to connect you with our sales team for detailed pricing?"
        else:
            return "Thank you for your interest in KNKA Environmental Appliances. How can I assist you with air quality solutions today?"

    def build_system_prompt(self, current_stage: str, personalization_context: str = "") -> str:
        """构建系统提示（支持个性化和配置驱动）"""
        # 获取当前阶段的配置
        stage_config = self._get_stage_config(current_stage)
        stage_description = stage_config.get('objective', f"{current_stage} stage")

        # 获取购买渠道信息
        purchase_channels = self._get_purchase_channels_text()

        # 构建网站信息
        website_info = f"Visit our website at {self.company_website}" if hasattr(self, 'company_website') and self.company_website else ""

        # 获取个性化设置
        personality = self.sales_config.get('agent_profile', {}).get('personality', {})
        style = personality.get('style', 'professional, helpful')
        tone = personality.get('tone', 'friendly')
        approach = personality.get('approach', 'solution-oriented')

        # 构建基础提示（融入个性化设置）
        base_prompt = f"""You are {self.salesperson_name}, a {self.salesperson_role} at {self.company_name}.

{self.company_business}
{website_info}

PERSONALITY & COMMUNICATION STYLE:
- Style: {style}
- Tone: {tone}
- Approach: {approach}
- Always maintain this personality throughout the conversation

Your current conversation stage is: {stage_description}

PRODUCT CATALOG:
{self.product_catalog}

{purchase_channels}

{personalization_context}

CONVERSATION GUIDELINES:
1. Always be helpful, professional, and friendly
2. Focus on understanding customer needs based on their preferences
3. Recommend appropriate products based on their requirements
4. For basic after-sales questions (installation, operation, maintenance), provide helpful guidance
5. For complex after-sales issues (technical problems, returns, refunds), guide customers to provide detailed information
6. Use natural conversation flow, don't be overly salesy
7. Ask relevant questions to understand their specific needs
8. Provide specific product details when requested
9. When customers show purchase intent, provide relevant purchase channel links

SALES STAGES PROGRESSION:
{self._get_sales_stages_text()}

Remember: You are {self.salesperson_name}, the personal assistant for {self.company_name}. Be natural, helpful, and personalized!"""

        return base_prompt

    def _get_stage_config(self, stage_name: str) -> Dict[str, Any]:
        """获取销售阶段配置"""
        sales_process = self.sales_config.get('sales_process', {})
        stages = sales_process.get('stages', {})
        return stages.get(stage_name, {})

    def _get_purchase_channels_text(self) -> str:
        """获取购买渠道文本"""
        channels = self.sales_config.get('purchase_channels', {})
        if not channels:
            return ""

        channel_text = "PURCHASE CHANNELS:\n"
        for channel_id, channel_info in channels.items():
            name = channel_info.get('name', channel_id)
            benefits = channel_info.get('benefits', [])
            channel_text += f"- {name}: {', '.join(benefits)}\n"

        return channel_text

    def _get_sales_stages_text(self) -> str:
        """获取销售阶段文本"""
        stages_text = ""
        for stage_name, stage_description in self.sales_stages.items():
            stages_text += f"- {stage_name.title()}: {stage_description}\n"
        return stages_text

    def analyze_conversation_stage(self, user_message: str, ai_response: str, current_stage: str) -> str:
        """分析并确定下一个对话阶段"""
        user_lower = user_message.lower()
        response_lower = ai_response.lower()

        # 简单的阶段转换逻辑
        if current_stage == "introduction":
            if any(word in user_lower for word in ["yes", "interested", "need", "want", "looking"]):
                return "qualification"
        elif current_stage == "qualification":
            if any(word in user_lower for word in ["air", "humidity", "dust", "allergen", "clean"]):
                return "needs_analysis"
        elif current_stage == "needs_analysis":
            if any(word in response_lower for word in ["recommend", "suggest", "product"]):
                return "solution_presentation"
        elif current_stage == "solution_presentation":
            if any(word in user_lower for word in ["but", "however", "concern", "worry", "expensive"]):
                return "objection_handling"
            elif any(word in user_lower for word in ["buy", "purchase", "order", "price", "cost"]):
                return "close"
        elif current_stage == "objection_handling":
            if any(word in user_lower for word in ["ok", "understand", "makes sense"]):
                return "close"
        elif current_stage == "close":
            if any(word in user_lower for word in ["thanks", "goodbye", "bye", "later"]):
                return "end"

        return current_stage

    def reset_conversation(self, session_id: str):
        """重置对话"""
        if session_id in self.conversation_stages:
            del self.conversation_stages[session_id]
        # 清除记忆缓存
        user_id = self.memory_service.get_user_id_from_session(session_id)
        if user_id in self.memory_service.preference_cache:
            del self.memory_service.preference_cache[user_id]
        logger.info(f"Reset conversation for session: {session_id}")
    
    async def get_session_summary(self, session_id: str) -> Dict[str, Any]:
        """获取会话摘要"""
        try:
            if not self.db_service:
                from .database import DatabaseService
                self.db_service = DatabaseService()

            messages = await self.db_service.get_session_messages(session_id)
            stage = self.get_conversation_stage(session_id)

            return {
                "session_id": session_id,
                "conversation_stage": stage,
                "message_count": len(messages),
                "last_activity": messages[-1].created_at if messages else None
            }

        except Exception as e:
            logger.error(f"Failed to get session summary: {e}")
            return {"session_id": session_id, "error": str(e)}

    async def check_conversation_rules(self, message: str, session_id: str) -> Tuple[bool, str]:
        """检查对话规则"""
        try:
            # 检查话题相关性
            is_relevant, redirect_message = conversation_rules_service.check_topic_relevance(message, session_id)
            if not is_relevant:
                return False, redirect_message

            # 检查对话限制
            within_limits, limit_message = await conversation_rules_service.check_conversation_limits(session_id)
            if not within_limits:
                return False, limit_message

            return True, ""
        except Exception as e:
            logger.error(f"检查对话规则失败: {e}")
            return True, ""  # 出错时允许继续对话

    def check_escalation_rules(self, message: str, session_id: str, conversation_history: List[Dict] = None) -> Tuple[bool, str, Dict]:
        """检查升级规则"""
        try:
            # 检查是否需要自动升级
            should_escalate, reason, action = escalation_service.check_auto_escalation(message, session_id, conversation_history)

            if should_escalate:
                # 启动人工接管
                handover_data = escalation_service.initiate_handover(session_id, reason, conversation_history)
                return True, reason, handover_data

            return False, "", {}
        except Exception as e:
            logger.error(f"检查升级规则失败: {e}")
            return False, "", {}

    def manual_escalation(self, session_id: str, reason: str = "manual_request", conversation_history: List[Dict] = None) -> Dict[str, Any]:
        """手动升级到人工客服"""
        try:
            handover_data = escalation_service.initiate_handover(session_id, reason, conversation_history)
            logger.info(f"手动升级成功 - 会话: {session_id}")
            return handover_data
        except Exception as e:
            logger.error(f"手动升级失败: {e}")
            return {"error": str(e)}

    def get_conversation_rules_status(self) -> Dict[str, Any]:
        """获取对话规则状态"""
        return {
            "stay_on_topic": conversation_rules_service.config.get('stay_on_topic', True),
            "max_turns": conversation_rules_service.config.get('max_conversation_turns', 50),
            "session_timeout": conversation_rules_service.config.get('session_timeout_minutes', 30),
            "memory_enabled": conversation_rules_service.should_include_context()
        }

    def get_escalation_rules_status(self) -> Dict[str, Any]:
        """获取升级规则状态"""
        return {
            "auto_trigger": escalation_service.is_auto_trigger_enabled(),
            "manual_only": escalation_service.is_manual_only(),
            "context_transfer": escalation_service.is_context_transfer_enabled(),
            "escalation_message": escalation_service.get_escalation_message()
        }

    async def export_session_to_csv(self, session_id: str, reason: str = "user_cleared") -> str:
        """导出会话记录到CSV文件"""
        try:
            # 🔧 使用path_utils确保正确的路径处理（支持打包环境）
            history_dir = get_history_directory()
            logger.info(f"History directory ensured: {history_dir}")

            # 生成文件名：年月日时分秒_session_id_reason.csv
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            # 清理session_id中的特殊字符，避免文件名问题
            clean_session_id = session_id.replace('/', '_').replace('\\', '_')
            filename = f"{timestamp}_{clean_session_id}_{reason}.csv"
            filepath = history_dir / filename

            # 获取完整的会话记录
            messages = await self.db_service.get_conversation_history(session_id)
            logger.info(f"Retrieved {len(messages)} messages for session {session_id}")

            # 写入CSV文件
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['timestamp', 'sender', 'message', 'session_id', 'user_ip']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                # 写入表头
                writer.writeheader()

                # 写入消息记录
                for msg in messages:
                    # 处理消息内容，移除换行符避免CSV格式问题
                    clean_message = str(msg.get('message', '')).replace('\n', ' ').replace('\r', ' ')

                    writer.writerow({
                        'timestamp': msg.get('timestamp', ''),
                        'sender': msg.get('sender', ''),  # 'user', 'ai', 'customer_service'
                        'message': clean_message,
                        'session_id': session_id,
                        'user_ip': msg.get('user_ip', '')
                    })

            logger.info(f"Session {session_id} exported to {filepath} ({len(messages)} messages)")
            return filepath

        except Exception as e:
            logger.error(f"Error exporting session {session_id} to CSV: {e}")
            return ""

    async def clear_session_from_database(self, session_id: str):
        """从数据库中清空指定session的所有记录"""
        try:
            # 删除该session的所有消息记录
            deleted_count = await self.db_service.delete_session_messages(session_id)
            logger.info(f"Cleared {deleted_count} messages for session {session_id} from database")

            # 清理内存中的session数据
            if hasattr(conversation_rules_service, 'session_data'):
                if session_id in conversation_rules_service.session_data:
                    del conversation_rules_service.session_data[session_id]
                    logger.info(f"Cleared session {session_id} from memory")

        except Exception as e:
            logger.error(f"Error clearing session {session_id} from database: {e}")

# 全局实例 - 延迟初始化
salesgpt_service = None

def get_salesgpt_service(db_service=None):
    """获取SalesGPT服务实例（延迟初始化）"""
    global salesgpt_service
    if salesgpt_service is None:
        salesgpt_service = SalesGPTService(db_service)
    return salesgpt_service
