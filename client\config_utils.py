#!/usr/bin/env python3
"""
配置文件路径管理工具
确保exe能够在其所在目录读取和创建配置文件
"""

import os
import sys
import json
import shutil
from pathlib import Path
from typing import Optional


def get_exe_directory() -> Path:
    """
    获取exe所在目录
    - 开发环境：返回client目录
    - 打包环境：返回exe所在目录
    """
    if getattr(sys, 'frozen', False):
        # 打包后的环境
        exe_path = Path(sys.executable)
        return exe_path.parent
    else:
        # 开发环境
        script_path = Path(__file__)
        return script_path.parent


def get_config_path(filename: str) -> Path:
    """
    获取配置文件的完整路径
    
    Args:
        filename: 配置文件名（如 '.env', 'mail.json', 'users.json'）
    
    Returns:
        配置文件的完整路径
    """
    exe_dir = get_exe_directory()
    return exe_dir / filename


def ensure_env_file() -> bool:
    """
    确保.env文件存在，如果不存在则创建默认配置
    
    Returns:
        True if file exists or was created successfully
    """
    env_path = get_config_path('.env')
    
    if env_path.exists():
        return True
    
    try:
        # 创建默认.env配置
        default_env_content = """# Client Configuration
# 服务器配置 - 支持本地和远程连接
SERVER_HOST=localhost
SERVER_PORT=8000

# 管理员Token (需要从后端获取)
ADMIN_TOKEN=your_admin_token_here

# SSL/HTTPS配置
# 本地局域网连接建议设置为false，远程连接建议设置为true
USE_HTTPS=false

# 常用配置示例:
# 本地开发: SERVER_HOST=localhost, USE_HTTPS=false
# 局域网连接: SERVER_HOST=*************, USE_HTTPS=false
# 远程连接: SERVER_HOST=chat.22668.xyz, USE_HTTPS=true

# Admin Credentials (default - 向后兼容)
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin

# Timezone Configuration
# 客户端时区设置 (支持标准时区名称)
CLIENT_TIMEZONE=America/Los_Angeles
CLIENT_TIMEZONE_NAME=洛杉矶时间
# 备用时区偏移量 (当时区库不可用时使用，格式: +/-HH:MM)
CLIENT_FALLBACK_OFFSET=-08:00

# UI Settings
WINDOW_WIDTH=1200
WINDOW_HEIGHT=800
REFRESH_INTERVAL=2000

# Email Auto Refresh Settings
# 邮件自动刷新间隔 (单位: 毫秒)
# 300000 = 5分钟, 180000 = 3分钟, 600000 = 10分钟
EMAIL_REFRESH_INTERVAL=300000

# 是否启用邮件自动刷新 (true/false)
EMAIL_AUTO_REFRESH_ENABLED=true
"""
        
        with open(env_path, 'w', encoding='utf-8') as f:
            f.write(default_env_content)
        
        print(f"✅ 已创建默认.env配置文件: {env_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建.env文件失败: {e}")
        return False


def ensure_mail_json() -> bool:
    """
    确保mail.json文件存在，如果不存在则创建默认配置
    
    Returns:
        True if file exists or was created successfully
    """
    mail_path = get_config_path('mail.json')
    
    if mail_path.exists():
        return True
    
    try:
        # 创建默认mail.json配置
        default_mail_config = {
            "mailboxes": {},
            "version": "1.0",
            "last_updated": "2024-01-01T00:00:00"
        }
        
        with open(mail_path, 'w', encoding='utf-8') as f:
            json.dump(default_mail_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 已创建默认mail.json配置文件: {mail_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建mail.json文件失败: {e}")
        return False


def ensure_users_json() -> bool:
    """
    确保users.json文件存在，如果不存在则创建默认配置
    
    Returns:
        True if file exists or was created successfully
    """
    users_path = get_config_path('users.json')
    
    if users_path.exists():
        return True
    
    try:
        # 导入用户管理器来获取默认配置
        from user_manager import UserManager
        
        # 创建临时用户管理器实例来获取默认用户配置
        temp_manager = UserManager.__new__(UserManager)
        default_users = temp_manager.get_default_users()
        
        with open(users_path, 'w', encoding='utf-8') as f:
            json.dump(default_users, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 已创建默认users.json配置文件: {users_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建users.json文件失败: {e}")
        return False


def ensure_all_config_files() -> bool:
    """
    确保所有配置文件都存在
    
    Returns:
        True if all files exist or were created successfully
    """
    results = []
    
    print("🔧 检查配置文件...")
    
    # 检查并创建.env文件
    results.append(ensure_env_file())
    
    # 检查并创建mail.json文件
    results.append(ensure_mail_json())
    
    # 检查并创建users.json文件
    results.append(ensure_users_json())
    
    success = all(results)
    
    if success:
        print("✅ 所有配置文件检查完成")
    else:
        print("❌ 部分配置文件创建失败")
    
    return success


def get_runtime_info() -> dict:
    """
    获取运行时环境信息，用于调试
    
    Returns:
        包含运行时信息的字典
    """
    exe_dir = get_exe_directory()
    
    info = {
        "is_frozen": getattr(sys, 'frozen', False),
        "exe_directory": str(exe_dir),
        "script_path": str(Path(__file__)) if not getattr(sys, 'frozen', False) else "N/A",
        "executable_path": sys.executable,
        "config_files": {
            ".env": str(get_config_path('.env')),
            "mail.json": str(get_config_path('mail.json')),
            "users.json": str(get_config_path('users.json'))
        }
    }
    
    return info


if __name__ == "__main__":
    # 测试配置文件管理功能
    print("🔧 配置文件管理工具测试")
    print("=" * 50)
    
    # 显示运行时信息
    info = get_runtime_info()
    for key, value in info.items():
        if isinstance(value, dict):
            print(f"{key}:")
            for sub_key, sub_value in value.items():
                print(f"  {sub_key}: {sub_value}")
        else:
            print(f"{key}: {value}")
    
    print("\n🔧 确保配置文件存在...")
    print("=" * 50)
    
    # 确保所有配置文件存在
    ensure_all_config_files()
