"""
简化的配置管理器
用于GUI配置编辑器的基本配置管理功能
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, List


class SimpleConfigManager:
    """简化的配置管理器"""
    
    def __init__(self):
        self.config_data = {}
        self.config_path = None
        self.is_modified = False
    
    def load_config_file(self, file_path: str) -> bool:
        """加载配置文件"""
        import logging
        logging.info(f"=== SimpleConfigManager开始加载配置文件: {file_path} ===")

        try:
            logging.info("打开文件...")
            with open(file_path, 'r', encoding='utf-8') as f:
                logging.info("读取文件内容...")
                self.config_data = json.load(f)
                logging.info(f"JSON解析成功，配置数据大小: {len(str(self.config_data))} 字符")

            self.config_path = file_path
            self.is_modified = False
            logging.info("配置文件加载成功")
            return True
        except Exception as e:
            error_msg = f"加载配置文件失败: {e}"
            logging.error(error_msg)
            print(error_msg)
            return False
    
    def save_config_file(self, file_path: str = None) -> bool:
        """保存配置文件"""
        try:
            save_path = file_path or self.config_path
            if not save_path:
                return False
            
            # 确保目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=2)
            
            self.config_path = save_path
            self.is_modified = False
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get_config_data(self) -> Dict[str, Any]:
        """获取配置数据"""
        return self.config_data
    
    def set_config_data(self, data: Dict[str, Any]):
        """设置配置数据"""
        self.config_data = data
        self.is_modified = True
    
    def update_config(self, key: str, value: Any):
        """更新配置项"""
        keys = key.split('.')
        current = self.config_data
        
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        current[keys[-1]] = value
        self.is_modified = True
    
    def get_config_value(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        current = self.config_data
        
        try:
            for k in keys:
                current = current[k]
            return current
        except (KeyError, TypeError):
            return default
    
    def validate_config(self) -> List[str]:
        """验证配置"""
        errors = []
        
        # 基本结构验证
        required_sections = ['agent_profile', 'products', 'sales_process']
        for section in required_sections:
            if section not in self.config_data:
                errors.append(f"缺少必需的配置节: {section}")
        
        return errors


class SimpleSettingsManager:
    """简化的设置管理器"""
    
    def __init__(self):
        self.settings = {
            'recent_configs': [],
            'window_geometry': None,
            'theme': 'default'
        }
        self.settings_file = Path.home() / '.salesgpt_config_editor' / 'settings.json'
        self.load_settings()
    
    def load_settings(self):
        """加载设置"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    self.settings.update(loaded_settings)
        except Exception as e:
            print(f"加载设置失败: {e}")
    
    def save_settings(self):
        """保存设置"""
        try:
            self.settings_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存设置失败: {e}")
    
    def add_recent_config(self, file_path: str):
        """添加最近使用的配置文件"""
        if file_path in self.settings['recent_configs']:
            self.settings['recent_configs'].remove(file_path)
        
        self.settings['recent_configs'].insert(0, file_path)
        
        # 只保留最近10个
        self.settings['recent_configs'] = self.settings['recent_configs'][:10]
        self.save_settings()
    
    def get_recent_configs(self) -> List[str]:
        """获取最近使用的配置文件"""
        return [f for f in self.settings['recent_configs'] if os.path.exists(f)]
    
    def set_window_geometry(self, geometry):
        """设置窗口几何信息"""
        self.settings['window_geometry'] = geometry
        self.save_settings()
    
    def get_window_geometry(self):
        """获取窗口几何信息"""
        return self.settings.get('window_geometry')


# DataConnector已移除：GUI专注于配置文件编辑
# 不需要动态数据连接功能

# 创建全局实例
config_manager = SimpleConfigManager()
settings_manager = SimpleSettingsManager()
