#!/usr/bin/env python3
"""
文件日志系统
将详细日志写入文件，终端只显示错误信息
"""
import os
import logging
import time
import sys
from datetime import datetime
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from pathlib import Path

# 全局变量，用于跟踪日志初始化，避免重复日志
_logger_init_file = None

def _should_print_init_log(log_dir):
    """检查是否应该打印初始化日志"""
    global _logger_init_file

    try:
        if _logger_init_file is None:
            _logger_init_file = Path(log_dir) / ".logger_init"

        if _logger_init_file.exists():
            # 检查文件修改时间
            last_init_time = _logger_init_file.stat().st_mtime
            current_time = time.time()
            # 如果距离上次初始化不到3秒，则不打印
            if current_time - last_init_time < 3:
                return False

        # 更新初始化文件时间戳
        _logger_init_file.touch()
        return True
    except Exception:
        # 如果文件操作失败，默认打印
        return True

class FileLogger:
    def __init__(self, name="backend", log_dir="."):
        """
        初始化文件日志系统
        
        Args:
            name: 日志名称
            log_dir: 日志文件目录
        """
        self.name = name
        self.log_dir = log_dir
        
        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
        
        # 设置日志文件路径 - 使用logs子目录
        logs_dir = os.path.join(log_dir, "logs")
        os.makedirs(logs_dir, exist_ok=True)
        log_file = os.path.join(logs_dir, f"{name}.log")

        # 创建logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)

        # 清除现有的处理器
        self.logger.handlers.clear()

        # 文件处理器 - 按天轮转
        file_handler = TimedRotatingFileHandler(
            log_file,
            when='midnight',        # 每天午夜轮转
            interval=1,            # 间隔1天
            backupCount=30,        # 保留30天的日志
            encoding='utf-8'
        )
        # 设置文件名后缀格式：backend.log.2025-06-23
        file_handler.suffix = '%Y-%m-%d'
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器 - 显示重要信息和错误
        console_handler = logging.StreamHandler()
        # 设置控制台输出编码为 UTF-8
        if hasattr(console_handler.stream, 'reconfigure'):
            try:
                console_handler.stream.reconfigure(encoding='utf-8')
            except:
                pass
        # 从环境变量读取控制台日志级别，默认为 INFO
        console_level = os.getenv("CONSOLE_LOG_LEVEL", "INFO").upper()
        console_handler.setLevel(getattr(logging, console_level, logging.INFO))
        
        # 设置格式
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 自定义控制台格式器，根据日志级别显示不同图标
        class ColoredConsoleFormatter(logging.Formatter):
            def format(self, record):
                if record.levelno >= logging.ERROR:
                    prefix = "ERROR"
                elif record.levelno >= logging.WARNING:
                    prefix = "WARN"
                elif record.levelno >= logging.INFO:
                    prefix = "INFO"
                else:
                    prefix = "DEBUG"

                formatted_time = self.formatTime(record, '%H:%M:%S')
                return f"{prefix} [{formatted_time}] {record.levelname}: {record.getMessage()}"

        console_formatter = ColoredConsoleFormatter()
        
        file_handler.setFormatter(file_formatter)
        console_handler.setFormatter(console_formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

        # 智能记录启动信息，避免重复日志
        if _should_print_init_log(log_dir):
            self.info(f"日志系统初始化完成 - 日志文件: {log_file}")
    
    def debug(self, message):
        """调试信息"""
        self.logger.debug(message)
    
    def info(self, message):
        """一般信息"""
        self.logger.info(message)
    
    def warning(self, message):
        """警告信息"""
        self.logger.warning(message)
    
    def error(self, message):
        """错误信息 - 会显示在终端"""
        self.logger.error(message)
    
    def critical(self, message):
        """严重错误 - 会显示在终端"""
        self.logger.critical(message)
    
    def startup(self, message):
        """启动信息 - 写入文件，终端显示简化版本"""
        self.logger.info(f"[STARTUP] {message}")
        print(f"STARTUP: {message}")
    
    def success(self, message):
        """成功信息 - 写入文件，终端显示简化版本"""
        self.logger.info(f"[SUCCESS] {message}")
        print(f"SUCCESS: {message}")
    
    def shutdown(self, message):
        """关闭信息 - 写入文件，终端显示简化版本"""
        self.logger.info(f"[SHUTDOWN] {message}")
        print(f"SHUTDOWN: {message}")
    
    def chat_user(self, session_id, message, client_ip=None):
        """用户消息"""
        short_session = session_id[:8] + "..." if len(session_id) > 8 else session_id
        short_message = message[:80] + "..." if len(message) > 80 else message
        ip_info = f" | IP: {client_ip}" if client_ip else ""

        # 强制写入文件 - 直接操作文件处理器
        log_message = f"[CHAT] 会话ID: {session_id}{ip_info} | 用户: {message}"
        self._force_log_to_file(log_message, "INFO")

        # 强制输出到终端 - 使用多种方法确保显示
        ip_display = f"[{client_ip}]" if client_ip else ""
        self._force_terminal_output(f"USER [{short_session}] {ip_display} 用户: {short_message}", "blue")

    def chat_ai(self, session_id, message):
        """AI回复"""
        short_session = session_id[:8] + "..." if len(session_id) > 8 else session_id
        short_message = message[:80] + "..." if len(message) > 80 else message

        # 强制写入文件 - 直接操作文件处理器
        log_message = f"[CHAT] 会话ID: {session_id} | AI回复: {message}"
        self._force_log_to_file(log_message, "INFO")

        # 强制输出到终端 - 使用多种方法确保显示
        self._force_terminal_output(f"🤖 [{short_session}] AI: {short_message}", "green")

    def chat_admin(self, session_id, message, admin_id=None):
        """管理员消息"""
        short_session = session_id[:8] + "..." if len(session_id) > 8 else session_id
        short_message = message[:80] + "..." if len(message) > 80 else message
        admin_info = f"({admin_id})" if admin_id else ""

        # 强制写入文件 - 直接操作文件处理器
        log_message = f"[CHAT] 会话ID: {session_id} | 管理员{admin_info}: {message}"
        self._force_log_to_file(log_message, "INFO")

        # 强制输出到终端 - 使用多种方法确保显示
        self._force_terminal_output(f"ADMIN [{short_session}] 管理员{admin_info}: {short_message}", "yellow")

    def _force_log_to_file(self, message, level="INFO"):
        """强制写入日志到文件，绕过可能的uvicorn干扰"""
        try:
            # 方法1：尝试使用现有的文件处理器
            file_handler = None
            for handler in self.logger.handlers:
                if isinstance(handler, (TimedRotatingFileHandler, logging.FileHandler)):
                    file_handler = handler
                    break

            if file_handler and hasattr(file_handler, 'stream'):
                # 创建日志记录
                from datetime import datetime
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                log_line = f"{timestamp} - {self.name} - {level} - {message}\n"

                # 直接写入文件
                file_handler.stream.write(log_line)
                file_handler.stream.flush()
                return
        except Exception:
            pass

        try:
            # 方法2：直接写入文件（作为备用方案）
            logs_dir = os.path.join(self.log_dir, "logs")
            log_file = os.path.join(logs_dir, f"{self.name}.log")

            from datetime import datetime
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_line = f"{timestamp} - {self.name} - {level} - {message}\n"

            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(log_line)
                f.flush()
        except Exception:
            # 如果所有方法都失败，回退到普通日志
            try:
                self.logger.info(message)
            except:
                pass

    def _force_terminal_output(self, message, color="white"):
        """强制输出到终端，优先使用stderr避免被uvicorn拦截"""
        # 颜色代码映射
        colors = {
            "blue": "\033[94m",
            "green": "\033[92m",
            "yellow": "\033[93m",
            "red": "\033[91m",
            "white": "\033[0m"
        }

        color_code = colors.get(color, "\033[0m")
        reset_code = "\033[0m"
        colored_message = f"{color_code}{message}{reset_code}"

        # 优先使用 stderr，因为它通常不被 uvicorn 拦截
        try:
            sys.stderr.write(colored_message + "\n")
            sys.stderr.flush()
        except Exception:
            # 如果 stderr 失败，回退到 stdout
            try:
                sys.stdout.write(colored_message + "\n")
                sys.stdout.flush()
            except Exception:
                # 最后回退到 print
                try:
                    print(colored_message)
                except Exception:
                    pass
    
    def api_request(self, method, path, status_code, duration=None):
        """API请求日志"""
        duration_str = f" ({duration:.2f}ms)" if duration else ""
        self.logger.info(f"[API] {method} {path} - {status_code}{duration_str}")
    
    def websocket_event(self, event, session_id=None, details=None):
        """WebSocket事件"""
        session_info = f" 会话: {session_id}" if session_id else ""
        detail_info = f" - {details}" if details else ""
        self.logger.info(f"[WS] {event}{session_info}{detail_info}")
    
    def database_operation(self, operation, table=None, count=None):
        """数据库操作"""
        table_info = f" 表: {table}" if table else ""
        count_info = f" 影响行数: {count}" if count is not None else ""
        self.logger.info(f"[DB] {operation}{table_info}{count_info}")
    
    def llm_request(self, provider, model, tokens_used=None, duration=None):
        """LLM请求日志"""
        token_info = f" 令牌: {tokens_used}" if tokens_used else ""
        duration_info = f" 耗时: {duration:.2f}s" if duration else ""
        self.logger.info(f"[LLM] {provider}/{model}{token_info}{duration_info}")
    
    def admin_action(self, admin_id, action, session_id=None, details=None):
        """管理员操作"""
        session_info = f" 会话: {session_id[:8]}..." if session_id and len(session_id) > 8 else f" 会话: {session_id}" if session_id else ""
        detail_info = f" - {details}" if details else ""
        self.logger.info(f"[ADMIN] {admin_id}: {action}{session_info}{detail_info}")
        # 在终端显示管理员操作
        print(f"\033[95mADMIN_OP: {admin_id} {action}{session_info}\033[0m")

    def clean_old_logs(self, days=30):
        """清理指定天数之前的日志文件"""
        try:
            logs_dir = os.path.join(self.log_dir, "logs")
            if not os.path.exists(logs_dir):
                return

            cutoff_time = time.time() - (days * 24 * 60 * 60)
            cleaned_count = 0

            for filename in os.listdir(logs_dir):
                if filename.startswith(f"{self.name}.log.") and not filename.endswith('.gz'):
                    file_path = os.path.join(logs_dir, filename)
                    if os.path.getmtime(file_path) < cutoff_time:
                        os.remove(file_path)
                        cleaned_count += 1
                        self.info(f"清理旧日志文件: {filename}")

            if cleaned_count > 0:
                self.info(f"日志清理完成，删除了 {cleaned_count} 个旧日志文件")
        except Exception as e:
            self.error(f"日志清理失败: {e}")

    def get_log_info(self):
        """获取日志文件信息"""
        try:
            logs_dir = os.path.join(self.log_dir, "logs")
            if not os.path.exists(logs_dir):
                return {"total_files": 0, "total_size": 0}

            total_files = 0
            total_size = 0

            for filename in os.listdir(logs_dir):
                if filename.startswith(f"{self.name}.log"):
                    file_path = os.path.join(logs_dir, filename)
                    total_files += 1
                    total_size += os.path.getsize(file_path)

            return {
                "total_files": total_files,
                "total_size": total_size,
                "total_size_mb": round(total_size / 1024 / 1024, 2),
                "logs_dir": logs_dir
            }
        except Exception as e:
            self.error(f"获取日志信息失败: {e}")
            return {"error": str(e)}

# 创建全局日志实例 - 日志保存在backend目录的logs子目录
from .path_utils import get_backend_directory
backend_dir = get_backend_directory()
backend_logger = FileLogger("backend", str(backend_dir))
