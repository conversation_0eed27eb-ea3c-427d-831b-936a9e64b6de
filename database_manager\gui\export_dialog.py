#!/usr/bin/env python3
"""
数据导出对话框
用于配置和执行数据导出
"""

import os
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QComboBox, QGroupBox,
                            QFileDialog, QMessageBox, QCheckBox, QTextEdit,
                            QSpinBox, QProgressBar, QRadioButton, QButtonGroup)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

from core.database import db_manager
from core.export import export_manager
from core.config import config_manager

class ExportWorker(QThread):
    """导出工作线程"""
    progress_updated = pyqtSignal(int)
    export_completed = pyqtSignal(bool, str)
    
    def __init__(self, export_config):
        super().__init__()
        self.export_config = export_config
    
    def run(self):
        """执行导出"""
        try:
            table_name = self.export_config['table']
            file_path = self.export_config['file_path']
            file_format = self.export_config['format']
            where_clause = self.export_config.get('where_clause', '')
            limit = self.export_config.get('limit', 0)
            
            # 获取数据
            self.progress_updated.emit(20)
            
            if limit > 0:
                data = db_manager.get_table_data(table_name, limit, 0, where_clause)
            else:
                data = db_manager.get_table_data(table_name, 0, 0, where_clause)
            
            self.progress_updated.emit(50)
            
            # 格式化数据
            formatted_data = export_manager.format_data_for_export(data)
            
            self.progress_updated.emit(70)
            
            # 导出数据
            if file_format == 'csv':
                settings = config_manager.get_export_settings()
                export_manager.export_to_csv(
                    formatted_data, file_path,
                    encoding=settings['default_encoding'],
                    separator=settings['default_separator'],
                    include_headers=settings['include_headers']
                )
            elif file_format == 'excel':
                export_manager.export_to_excel(formatted_data, file_path)
            elif file_format == 'json':
                export_manager.export_to_json(formatted_data, file_path)
            
            self.progress_updated.emit(100)
            self.export_completed.emit(True, f"成功导出 {len(formatted_data)} 条记录")
            
        except Exception as e:
            self.export_completed.emit(False, str(e))

class ExportDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.export_worker = None
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("导出数据")
        self.setModal(True)
        self.setMinimumSize(500, 600)
        
        layout = QVBoxLayout(self)
        
        # 数据源选择组
        source_group = QGroupBox("数据源")
        source_layout = QVBoxLayout(source_group)
        
        # 表选择
        table_layout = QHBoxLayout()
        table_layout.addWidget(QLabel("选择表:"))
        
        self.table_combo = QComboBox()
        self.load_tables()
        table_layout.addWidget(self.table_combo)
        
        source_layout.addLayout(table_layout)
        
        # 筛选条件
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("筛选条件:"))
        
        self.filter_input = QLineEdit()
        self.filter_input.setPlaceholderText("WHERE 条件 (可选)")
        filter_layout.addWidget(self.filter_input)
        
        source_layout.addLayout(filter_layout)
        
        # 记录限制
        limit_layout = QHBoxLayout()
        limit_layout.addWidget(QLabel("记录限制:"))
        
        self.limit_spin = QSpinBox()
        self.limit_spin.setRange(0, 1000000)
        self.limit_spin.setValue(0)
        self.limit_spin.setSpecialValueText("无限制")
        limit_layout.addWidget(self.limit_spin)
        
        source_layout.addLayout(limit_layout)
        
        layout.addWidget(source_group)
        
        # 导出格式组
        format_group = QGroupBox("导出格式")
        format_layout = QVBoxLayout(format_group)
        
        self.format_group = QButtonGroup()
        
        self.csv_radio = QRadioButton("CSV 文件")
        self.csv_radio.setChecked(True)
        self.format_group.addButton(self.csv_radio, 0)
        format_layout.addWidget(self.csv_radio)
        
        self.excel_radio = QRadioButton("Excel 文件")
        self.format_group.addButton(self.excel_radio, 1)
        format_layout.addWidget(self.excel_radio)
        
        self.json_radio = QRadioButton("JSON 文件")
        self.format_group.addButton(self.json_radio, 2)
        format_layout.addWidget(self.json_radio)
        
        layout.addWidget(format_group)
        
        # CSV选项组
        self.csv_options_group = QGroupBox("CSV选项")
        csv_options_layout = QVBoxLayout(self.csv_options_group)
        
        # 编码选择
        encoding_layout = QHBoxLayout()
        encoding_layout.addWidget(QLabel("编码:"))
        
        self.encoding_combo = QComboBox()
        self.encoding_combo.addItems(["utf-8", "gbk", "gb2312", "ascii"])
        encoding_layout.addWidget(self.encoding_combo)
        
        csv_options_layout.addLayout(encoding_layout)
        
        # 分隔符选择
        separator_layout = QHBoxLayout()
        separator_layout.addWidget(QLabel("分隔符:"))
        
        self.separator_combo = QComboBox()
        self.separator_combo.addItems([",", ";", "\t", "|"])
        separator_layout.addWidget(self.separator_combo)
        
        csv_options_layout.addLayout(separator_layout)
        
        # 包含标题
        self.include_headers_checkbox = QCheckBox("包含列标题")
        self.include_headers_checkbox.setChecked(True)
        csv_options_layout.addWidget(self.include_headers_checkbox)
        
        layout.addWidget(self.csv_options_group)
        
        # 文件路径组
        file_group = QGroupBox("输出文件")
        file_layout = QVBoxLayout(file_group)
        
        file_path_layout = QHBoxLayout()
        
        self.file_path_input = QLineEdit()
        self.file_path_input.setPlaceholderText("选择输出文件路径...")
        file_path_layout.addWidget(self.file_path_input)
        
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_output_file)
        file_path_layout.addWidget(browse_btn)
        
        file_layout.addLayout(file_path_layout)
        
        layout.addWidget(file_group)
        
        # 预览组
        preview_group = QGroupBox("数据预览")
        preview_layout = QVBoxLayout(preview_group)
        
        preview_btn = QPushButton("预览数据")
        preview_btn.clicked.connect(self.preview_data)
        preview_layout.addWidget(preview_btn)
        
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(150)
        self.preview_text.setReadOnly(True)
        self.preview_text.setPlaceholderText("点击预览按钮查看数据...")
        preview_layout.addWidget(self.preview_text)
        
        layout.addWidget(preview_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 按钮组
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.export_btn = QPushButton("开始导出")
        self.export_btn.clicked.connect(self.start_export)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        button_layout.addWidget(self.export_btn)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.format_group.buttonClicked.connect(self.on_format_changed)
    
    def load_tables(self):
        """加载表列表"""
        if db_manager.is_connected:
            tables = db_manager.get_tables()
            self.table_combo.addItems(tables)
    
    def load_settings(self):
        """加载导出设置"""
        settings = config_manager.get_export_settings()
        
        # 设置编码
        encoding_index = self.encoding_combo.findText(settings['default_encoding'])
        if encoding_index >= 0:
            self.encoding_combo.setCurrentIndex(encoding_index)
        
        # 设置分隔符
        separator_index = self.separator_combo.findText(settings['default_separator'])
        if separator_index >= 0:
            self.separator_combo.setCurrentIndex(separator_index)
        
        # 设置包含标题
        self.include_headers_checkbox.setChecked(settings['include_headers'])
    
    def on_format_changed(self, button):
        """格式选择改变"""
        # 只有CSV格式才显示CSV选项
        self.csv_options_group.setVisible(button == self.csv_radio)
    
    def browse_output_file(self):
        """浏览输出文件"""
        # 根据选择的格式确定文件过滤器
        if self.csv_radio.isChecked():
            file_filter = "CSV文件 (*.csv)"
            default_ext = ".csv"
        elif self.excel_radio.isChecked():
            file_filter = "Excel文件 (*.xlsx)"
            default_ext = ".xlsx"
        else:
            file_filter = "JSON文件 (*.json)"
            default_ext = ".json"
        
        # 生成默认文件名
        table_name = self.table_combo.currentText()
        default_name = f"{table_name}_export{default_ext}"
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "选择输出文件", default_name, file_filter
        )
        
        if file_path:
            self.file_path_input.setText(file_path)
    
    def preview_data(self):
        """预览数据"""
        if not db_manager.is_connected:
            QMessageBox.warning(self, "警告", "数据库未连接")
            return
        
        table_name = self.table_combo.currentText()
        if not table_name:
            QMessageBox.warning(self, "警告", "请选择表")
            return
        
        try:
            where_clause = self.filter_input.text().strip()
            data = db_manager.get_table_data(table_name, 5, 0, where_clause)
            
            if data.empty:
                self.preview_text.setText("没有数据可预览")
            else:
                preview = export_manager.get_export_preview(data)
                self.preview_text.setText(preview)
                
        except Exception as e:
            QMessageBox.critical(self, "预览错误", f"预览数据失败: {e}")
    
    def start_export(self):
        """开始导出"""
        # 验证输入
        if not self.table_combo.currentText():
            QMessageBox.warning(self, "警告", "请选择表")
            return
        
        if not self.file_path_input.text().strip():
            QMessageBox.warning(self, "警告", "请选择输出文件")
            return
        
        # 保存设置
        self.save_settings()
        
        # 准备导出配置
        export_config = {
            'table': self.table_combo.currentText(),
            'file_path': self.file_path_input.text().strip(),
            'where_clause': self.filter_input.text().strip(),
            'limit': self.limit_spin.value(),
            'format': self.get_selected_format()
        }
        
        # 开始导出
        self.export_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        self.export_worker = ExportWorker(export_config)
        self.export_worker.progress_updated.connect(self.progress_bar.setValue)
        self.export_worker.export_completed.connect(self.on_export_completed)
        self.export_worker.start()
    
    def get_selected_format(self):
        """获取选择的格式"""
        if self.csv_radio.isChecked():
            return 'csv'
        elif self.excel_radio.isChecked():
            return 'excel'
        else:
            return 'json'
    
    def save_settings(self):
        """保存导出设置"""
        settings = {
            'default_encoding': self.encoding_combo.currentText(),
            'default_separator': self.separator_combo.currentText(),
            'include_headers': self.include_headers_checkbox.isChecked()
        }
        config_manager.update_export_settings(settings)
    
    def on_export_completed(self, success, message):
        """导出完成"""
        self.progress_bar.setVisible(False)
        self.export_btn.setEnabled(True)
        
        if success:
            QMessageBox.information(self, "导出成功", message)
            self.accept()
        else:
            QMessageBox.critical(self, "导出失败", f"导出失败: {message}")
