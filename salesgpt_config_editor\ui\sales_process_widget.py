#!/usr/bin/env python3
"""
销售流程配置组件
负责销售阶段和流程的配置管理
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QTextEdit, QComboBox, QGroupBox,
                            QFormLayout, QScrollArea, QListWidget, QListWidgetItem,
                            QPushButton, QInputDialog, QMessageBox, QSpinBox,
                            QTreeWidget, QTreeWidgetItem, QSplitter, QTabWidget)
from PyQt6.QtCore import pyqtSignal, Qt
from PyQt6.QtGui import QFont
import sys
import os
from pathlib import Path

# 添加core目录到路径
current_dir = Path(__file__).parent.parent
sys.path.insert(0, str(current_dir))

# DataConnector已移除：GUI专注于配置文件编辑，不需要动态数据连接
# 所有数据都直接从配置文件加载


class SalesProcessWidget(QWidget):
    """销售流程配置组件"""
    
    # 信号定义
    config_changed = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.config_data = {}
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 主容器
        main_widget = QWidget()
        layout = QVBoxLayout(main_widget)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 销售阶段标签页
        stages_tab = self.create_stages_tab()
        tab_widget.addTab(stages_tab, "🎯 销售阶段")
        
        # 异议处理标签页
        objections_tab = self.create_objections_tab()
        tab_widget.addTab(objections_tab, "🛡️ 异议处理")
        
        # 成交技巧标签页
        closing_tab = self.create_closing_tab()
        tab_widget.addTab(closing_tab, "🎯 成交技巧")
        
        layout.addWidget(tab_widget)
        
        # 设置滚动区域
        scroll.setWidget(main_widget)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(scroll)
    
    def create_stages_tab(self):
        """创建销售阶段标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 说明信息
        help_label = QLabel("""
💡 销售阶段配置说明:
• 定义AI销售助手的工作流程，从初次接触到成交的完整过程
• 每个阶段都有特定的目标、策略和触发条件
• AI会根据客户的回应自动判断和切换销售阶段
        """)
        help_label.setStyleSheet("color: #666; font-size: 11px; background: #f5f5f5; padding: 10px; border-radius: 5px;")
        layout.addWidget(help_label)
        help_label.setMaximumHeight(100)  # 限制最大高度，使其更紧凑
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：阶段列表
        stages_group = QGroupBox("📋 销售阶段列表")
        stages_layout = QVBoxLayout(stages_group)
        
        self.stages_tree = QTreeWidget()
        self.stages_tree.setHeaderLabels(["阶段", "目标", "预计轮数"])
        self.stages_tree.currentItemChanged.connect(self.on_stage_selected)
        stages_layout.addWidget(self.stages_tree)
        
        # 阶段操作按钮
        stage_btn_layout = QHBoxLayout()
        self.add_stage_btn = QPushButton("➕ 添加阶段")
        self.add_stage_btn.clicked.connect(self.add_stage)
        stage_btn_layout.addWidget(self.add_stage_btn)
        
        self.remove_stage_btn = QPushButton("➖ 删除阶段")
        self.remove_stage_btn.clicked.connect(self.remove_stage)
        stage_btn_layout.addWidget(self.remove_stage_btn)
        
        stage_btn_layout.addStretch()
        stages_layout.addLayout(stage_btn_layout)
        
        splitter.addWidget(stages_group)
        
        # 右侧：阶段详细配置
        detail_group = QGroupBox("⚙️ 阶段详细配置")
        detail_layout = QFormLayout(detail_group)
        
        # 阶段名称 - 使用QGroupBox
        stage_name_group = QGroupBox("🏷️ 阶段名称")
        stage_name_group.setMinimumHeight(80)  # 设置最小高度
        stage_name_layout = QVBoxLayout(stage_name_group)
        stage_name_layout.setContentsMargins(8, 8, 8, 8)
        stage_name_layout.setSpacing(5)

        # 阶段名称编辑器填满容器
        self.stage_name_edit = QLineEdit()
        self.stage_name_edit.setPlaceholderText("输入销售阶段名称，如：需求探索、方案展示等")
        self.stage_name_edit.textChanged.connect(self.on_data_changed)
        stage_name_layout.addWidget(self.stage_name_edit, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        detail_layout.addRow(stage_name_group)

        # 阶段目标 - 使用QGroupBox
        stage_objective_group = QGroupBox("🎯 阶段目标")
        stage_objective_group.setMinimumHeight(120)  # 设置最小高度
        stage_objective_layout = QVBoxLayout(stage_objective_group)
        stage_objective_layout.setContentsMargins(8, 8, 8, 8)
        stage_objective_layout.setSpacing(5)

        # 阶段目标编辑器填满容器
        self.stage_objective_edit = QTextEdit()
        self.stage_objective_edit.setPlaceholderText("描述该阶段的主要目标和期望达成的效果")
        # 移除最大高度限制，让编辑器自动填充可用空间
        self.stage_objective_edit.textChanged.connect(self.on_data_changed)
        stage_objective_layout.addWidget(self.stage_objective_edit, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        detail_layout.addRow(stage_objective_group)
        
        # 预计对话轮数
        self.duration_spin = QSpinBox()
        self.duration_spin.setRange(1, 20)
        self.duration_spin.setValue(3)
        self.duration_spin.valueChanged.connect(self.on_config_changed)
        detail_layout.addRow("预计轮数:", self.duration_spin)
        
        # 关键行动 - 使用QGroupBox，编辑框填满容器，按钮在右下角
        actions_group = QGroupBox("🎯 关键行动")
        actions_group.setMinimumHeight(160)  # 设置最小高度
        actions_main_layout = QVBoxLayout(actions_group)
        actions_main_layout.setContentsMargins(8, 8, 8, 8)
        actions_main_layout.setSpacing(5)

        # 使用相对布局让列表填满大部分空间
        self.key_actions_list = QListWidget()
        # 移除最大高度限制，让列表自动填充可用空间
        actions_main_layout.addWidget(self.key_actions_list, 1)  # stretch factor = 1

        # 按钮布局 - 右对齐，紧凑排列
        action_btn_layout = QHBoxLayout()
        action_btn_layout.setContentsMargins(0, 5, 0, 0)  # 顶部留一点间距
        action_btn_layout.addStretch()  # 左侧弹性空间，让按钮右对齐

        self.add_action_btn = QPushButton("➕ 添加")
        self.add_action_btn.setMaximumWidth(80)  # 限制按钮宽度，更紧凑
        self.add_action_btn.clicked.connect(self.add_key_action)
        action_btn_layout.addWidget(self.add_action_btn)

        self.remove_action_btn = QPushButton("➖ 删除")
        self.remove_action_btn.setMaximumWidth(80)  # 限制按钮宽度，更紧凑
        self.remove_action_btn.clicked.connect(self.remove_key_action)
        action_btn_layout.addWidget(self.remove_action_btn)

        actions_main_layout.addLayout(action_btn_layout)

        # 直接添加QGroupBox，不需要标签
        detail_layout.addRow(actions_group)
        
        # 触发条件 - 使用QGroupBox，编辑框填满容器，按钮在右下角
        triggers_group = QGroupBox("⚡ 触发条件")
        triggers_group.setMinimumHeight(160)  # 设置最小高度
        triggers_main_layout = QVBoxLayout(triggers_group)
        triggers_main_layout.setContentsMargins(8, 8, 8, 8)
        triggers_main_layout.setSpacing(5)

        # 使用相对布局让列表填满大部分空间
        self.triggers_list = QListWidget()
        # 移除最大高度限制，让列表自动填充可用空间
        triggers_main_layout.addWidget(self.triggers_list, 1)  # stretch factor = 1

        # 按钮布局 - 右对齐，紧凑排列
        trigger_btn_layout = QHBoxLayout()
        trigger_btn_layout.setContentsMargins(0, 5, 0, 0)  # 顶部留一点间距
        trigger_btn_layout.addStretch()  # 左侧弹性空间，让按钮右对齐

        self.add_trigger_btn = QPushButton("➕ 添加")
        self.add_trigger_btn.setMaximumWidth(80)  # 限制按钮宽度，更紧凑
        self.add_trigger_btn.clicked.connect(self.add_trigger)
        trigger_btn_layout.addWidget(self.add_trigger_btn)

        self.remove_trigger_btn = QPushButton("➖ 删除")
        self.remove_trigger_btn.setMaximumWidth(80)  # 限制按钮宽度，更紧凑
        self.remove_trigger_btn.clicked.connect(self.remove_trigger)
        trigger_btn_layout.addWidget(self.remove_trigger_btn)

        triggers_main_layout.addLayout(trigger_btn_layout)

        # 直接添加QGroupBox，不需要标签
        detail_layout.addRow(triggers_group)
        
        # 建议问题 - 使用QGroupBox
        questions_group = QGroupBox("❓ 建议问题")
        questions_group.setMinimumHeight(140)  # 设置最小高度
        questions_layout = QVBoxLayout(questions_group)
        questions_layout.setContentsMargins(8, 8, 8, 8)
        questions_layout.setSpacing(5)

        # 建议问题编辑器填满容器
        self.questions_edit = QTextEdit()
        self.questions_edit.setPlaceholderText("输入该阶段建议询问的问题，每行一个")
        # 移除最大高度限制，让编辑器自动填充可用空间
        self.questions_edit.textChanged.connect(self.on_data_changed)
        questions_layout.addWidget(self.questions_edit, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        detail_layout.addRow(questions_group)
        
        splitter.addWidget(detail_group)
        splitter.setSizes([400, 600])
        
        layout.addWidget(splitter)
        
        # 加载默认阶段
        self.load_default_stages()
        
        return widget
    
    def create_objections_tab(self):
        """创建异议处理标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 说明信息
        help_label = QLabel("""
💡 异议处理配置说明:
• 配置常见客户异议和相应的处理策略
• AI会自动识别客户的异议类型并给出合适的回应
• 包括认同、重新定义、提供证据、试探成交等步骤
        """)
        help_label.setStyleSheet("color: #666; font-size: 11px; background: #f5f5f5; padding: 10px; border-radius: 5px;")
        layout.addWidget(help_label)
        help_label.setMaximumHeight(100)
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：异议类型列表
        objections_group = QGroupBox("📋 常见异议类型")
        objections_layout = QVBoxLayout(objections_group)
        
        self.objections_list = QListWidget()
        self.objections_list.currentItemChanged.connect(self.on_objection_selected)
        objections_layout.addWidget(self.objections_list)
        
        # 异议操作按钮
        objection_btn_layout = QHBoxLayout()
        self.add_objection_btn = QPushButton("➕ 添加异议")
        self.add_objection_btn.clicked.connect(self.add_objection)
        objection_btn_layout.addWidget(self.add_objection_btn)
        
        self.remove_objection_btn = QPushButton("➖ 删除异议")
        self.remove_objection_btn.clicked.connect(self.remove_objection)
        objection_btn_layout.addWidget(self.remove_objection_btn)
        
        objection_btn_layout.addStretch()
        objections_layout.addLayout(objection_btn_layout)
        
        splitter.addWidget(objections_group)
        
        # 右侧：异议处理配置
        handling_group = QGroupBox("🛡️ 异议处理策略")
        handling_layout = QFormLayout(handling_group)
        handling_layout.setLabelAlignment(Qt.AlignmentFlag.AlignTop)  # 标签顶部对齐
        
        # 异议名称 - 使用QGroupBox
        objection_name_group = QGroupBox("🏷️ 异议名称")
        objection_name_group.setMinimumHeight(80)  # 设置最小高度
        objection_name_layout = QVBoxLayout(objection_name_group)
        objection_name_layout.setContentsMargins(8, 8, 8, 8)
        objection_name_layout.setSpacing(5)

        # 异议名称编辑器填满容器
        self.objection_name_edit = QLineEdit()
        self.objection_name_edit.setPlaceholderText("输入异议类型名称，如：价格异议、质量担忧等")
        self.objection_name_edit.textChanged.connect(self.on_data_changed)
        objection_name_layout.addWidget(self.objection_name_edit, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        handling_layout.addRow(objection_name_group)
        
        # 认同话术 - 使用QGroupBox
        acknowledge_group = QGroupBox("🤝 认同话术")
        acknowledge_group.setMinimumHeight(100)  # 设置最小高度
        acknowledge_layout = QVBoxLayout(acknowledge_group)
        acknowledge_layout.setContentsMargins(8, 8, 8, 8)
        acknowledge_layout.setSpacing(5)

        # 认同话术编辑器填满容器
        self.acknowledge_edit = QTextEdit()
        self.acknowledge_edit.setPlaceholderText("例如: 我理解您的担心，价格确实是重要的考虑因素...")
        # 移除最大高度限制，让编辑器自动填充可用空间
        self.acknowledge_edit.textChanged.connect(self.on_data_changed)
        acknowledge_layout.addWidget(self.acknowledge_edit, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        handling_layout.addRow(acknowledge_group)
        
        # 重新定义 - 使用QGroupBox
        reframe_group = QGroupBox("🔄 重新定义")
        reframe_group.setMinimumHeight(100)  # 设置最小高度
        reframe_layout = QVBoxLayout(reframe_group)
        reframe_layout.setContentsMargins(8, 8, 8, 8)
        reframe_layout.setSpacing(5)

        # 重新定义编辑器填满容器
        self.reframe_edit = QTextEdit()
        self.reframe_edit.setPlaceholderText("例如: 让我们从长期价值的角度来看这个问题...")
        # 移除最大高度限制，让编辑器自动填充可用空间
        self.reframe_edit.textChanged.connect(self.on_data_changed)
        reframe_layout.addWidget(self.reframe_edit, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        handling_layout.addRow(reframe_group)
        
        # 证明点 - 使用QGroupBox，编辑框填满容器，按钮在右下角
        proof_group = QGroupBox("📋 证明点")
        proof_group.setMinimumHeight(180)  # 设置最小高度
        proof_main_layout = QVBoxLayout(proof_group)
        proof_main_layout.setContentsMargins(8, 8, 8, 8)
        proof_main_layout.setSpacing(5)

        # 使用相对布局让列表填满大部分空间
        self.proof_points_list = QListWidget()
        # 移除最大高度限制，让列表自动填充可用空间
        proof_main_layout.addWidget(self.proof_points_list, 1)  # stretch factor = 1

        # 按钮布局 - 右对齐，紧凑排列
        proof_btn_layout = QHBoxLayout()
        proof_btn_layout.setContentsMargins(0, 5, 0, 0)  # 顶部留一点间距
        proof_btn_layout.addStretch()  # 左侧弹性空间，让按钮右对齐

        self.add_proof_btn = QPushButton("➕ 添加")
        self.add_proof_btn.setMaximumWidth(80)  # 限制按钮宽度，更紧凑
        self.add_proof_btn.clicked.connect(self.add_proof_point)
        proof_btn_layout.addWidget(self.add_proof_btn)

        self.remove_proof_btn = QPushButton("➖ 删除")
        self.remove_proof_btn.setMaximumWidth(80)  # 限制按钮宽度，更紧凑
        self.remove_proof_btn.clicked.connect(self.remove_proof_point)
        proof_btn_layout.addWidget(self.remove_proof_btn)

        proof_main_layout.addLayout(proof_btn_layout)

        # 直接添加QGroupBox，不需要标签
        handling_layout.addRow(proof_group)
        
        # 试探成交 - 使用QGroupBox
        trial_close_group = QGroupBox("🎯 试探成交")
        trial_close_group.setMinimumHeight(100)  # 设置最小高度
        trial_close_layout = QVBoxLayout(trial_close_group)
        trial_close_layout.setContentsMargins(8, 8, 8, 8)
        trial_close_layout.setSpacing(5)

        # 试探成交编辑器填满容器
        self.trial_close_edit = QTextEdit()
        self.trial_close_edit.setPlaceholderText("例如: 如果价格合适，您会考虑购买吗？")
        # 移除最大高度限制，让编辑器自动填充可用空间
        self.trial_close_edit.textChanged.connect(self.on_data_changed)
        trial_close_layout.addWidget(self.trial_close_edit, 1)  # stretch factor = 1

        # 直接添加QGroupBox，不需要标签
        handling_layout.addRow(trial_close_group)
        
        splitter.addWidget(handling_group)
        splitter.setSizes([300, 700])
        
        layout.addWidget(splitter)
        
        # 加载默认异议
        self.load_default_objections()
        
        return widget
    
    def create_closing_tab(self):
        """创建成交技巧标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 说明信息
        help_label = QLabel("""
💡 成交技巧配置说明:
• 配置购买信号识别和成交技巧
• AI会识别客户的购买意向并使用合适的成交方法
• 包括假设成交、选择成交、紧迫成交等多种技巧
        """)
        help_label.setStyleSheet("color: #666; font-size: 11px; background: #f5f5f5; padding: 10px; border-radius: 5px;")
        layout.addWidget(help_label)
        
        # 购买信号配置 - 使用优化的QGroupBox布局
        signals_group = QGroupBox("🎯 购买信号识别")
        signals_group.setMinimumHeight(200)  # 设置最小高度
        signals_main_layout = QVBoxLayout(signals_group)
        signals_main_layout.setContentsMargins(8, 8, 8, 8)
        signals_main_layout.setSpacing(5)

        # 添加帮助信息
        signals_help = QLabel("💡 配置AI识别客户购买意向的关键信号")
        signals_help.setStyleSheet("color: #666; font-size: 11px;")
        signals_main_layout.addWidget(signals_help)

        # 使用相对布局让列表填满大部分空间
        self.buying_signals_list = QListWidget()
        # 移除最大高度限制，让列表自动填充可用空间
        signals_main_layout.addWidget(self.buying_signals_list, 1)  # stretch factor = 1

        # 按钮布局 - 右对齐，紧凑排列
        signals_btn_layout = QHBoxLayout()
        signals_btn_layout.setContentsMargins(0, 5, 0, 0)  # 顶部留一点间距
        signals_btn_layout.addStretch()  # 左侧弹性空间，让按钮右对齐

        self.add_signal_btn = QPushButton("➕ 添加")
        self.add_signal_btn.setMaximumWidth(80)  # 限制按钮宽度，更紧凑
        self.add_signal_btn.clicked.connect(self.add_buying_signal)
        signals_btn_layout.addWidget(self.add_signal_btn)

        self.remove_signal_btn = QPushButton("➖ 删除")
        self.remove_signal_btn.setMaximumWidth(80)  # 限制按钮宽度，更紧凑
        self.remove_signal_btn.clicked.connect(self.remove_buying_signal)
        signals_btn_layout.addWidget(self.remove_signal_btn)

        signals_main_layout.addLayout(signals_btn_layout)
        
        layout.addWidget(signals_group)
        
        # 成交技巧配置
        techniques_group = QGroupBox("🎯 成交技巧")
        techniques_layout = QVBoxLayout(techniques_group)
        
        # 创建成交技巧标签页
        techniques_tab = QTabWidget()
        
        # 假设成交 - 使用优化的布局
        assumptive_widget = QWidget()
        assumptive_main_layout = QVBoxLayout(assumptive_widget)
        assumptive_main_layout.setContentsMargins(8, 8, 8, 8)
        assumptive_main_layout.setSpacing(5)

        # 添加帮助信息
        assumptive_help = QLabel("💡 假设客户已经决定购买，直接询问具体细节")
        assumptive_help.setStyleSheet("color: #666; font-size: 11px;")
        assumptive_main_layout.addWidget(assumptive_help)

        # 话术列表自动填充空间
        self.assumptive_phrases_list = QListWidget()
        assumptive_main_layout.addWidget(self.assumptive_phrases_list, 1)  # stretch factor = 1

        # 按钮布局 - 右对齐，紧凑排列
        assumptive_btn_layout = QHBoxLayout()
        assumptive_btn_layout.setContentsMargins(0, 5, 0, 0)
        assumptive_btn_layout.addStretch()  # 左侧弹性空间，让按钮右对齐

        self.add_assumptive_btn = QPushButton("➕ 添加话术")
        self.add_assumptive_btn.setMaximumWidth(100)  # 限制按钮宽度
        self.add_assumptive_btn.clicked.connect(lambda: self.add_closing_phrase("assumptive"))
        assumptive_btn_layout.addWidget(self.add_assumptive_btn)

        self.remove_assumptive_btn = QPushButton("➖ 删除话术")
        self.remove_assumptive_btn.setMaximumWidth(100)  # 限制按钮宽度
        self.remove_assumptive_btn.clicked.connect(lambda: self.remove_closing_phrase("assumptive"))
        assumptive_btn_layout.addWidget(self.remove_assumptive_btn)

        assumptive_main_layout.addLayout(assumptive_btn_layout)
        
        techniques_tab.addTab(assumptive_widget, "假设成交")
        
        # 选择成交 - 使用优化的布局
        alternative_widget = QWidget()
        alternative_main_layout = QVBoxLayout(alternative_widget)
        alternative_main_layout.setContentsMargins(8, 8, 8, 8)
        alternative_main_layout.setSpacing(5)

        # 添加帮助信息
        alternative_help = QLabel("💡 给客户提供选择，让客户在不同选项中做决定")
        alternative_help.setStyleSheet("color: #666; font-size: 11px;")
        alternative_main_layout.addWidget(alternative_help)

        # 话术列表自动填充空间
        self.alternative_phrases_list = QListWidget()
        alternative_main_layout.addWidget(self.alternative_phrases_list, 1)  # stretch factor = 1

        # 按钮布局 - 右对齐，紧凑排列
        alternative_btn_layout = QHBoxLayout()
        alternative_btn_layout.setContentsMargins(0, 5, 0, 0)
        alternative_btn_layout.addStretch()  # 左侧弹性空间，让按钮右对齐

        self.add_alternative_btn = QPushButton("➕ 添加话术")
        self.add_alternative_btn.setMaximumWidth(100)  # 限制按钮宽度
        self.add_alternative_btn.clicked.connect(lambda: self.add_closing_phrase("alternative"))
        alternative_btn_layout.addWidget(self.add_alternative_btn)

        self.remove_alternative_btn = QPushButton("➖ 删除话术")
        self.remove_alternative_btn.setMaximumWidth(100)  # 限制按钮宽度
        self.remove_alternative_btn.clicked.connect(lambda: self.remove_closing_phrase("alternative"))
        alternative_btn_layout.addWidget(self.remove_alternative_btn)

        alternative_main_layout.addLayout(alternative_btn_layout)
        
        techniques_tab.addTab(alternative_widget, "选择成交")
        
        # 紧迫成交 - 使用优化的布局
        urgency_widget = QWidget()
        urgency_main_layout = QVBoxLayout(urgency_widget)
        urgency_main_layout.setContentsMargins(8, 8, 8, 8)
        urgency_main_layout.setSpacing(5)

        # 添加帮助信息
        urgency_help = QLabel("💡 创造紧迫感，促使客户立即做决定")
        urgency_help.setStyleSheet("color: #666; font-size: 11px;")
        urgency_main_layout.addWidget(urgency_help)

        # 话术列表自动填充空间
        self.urgency_phrases_list = QListWidget()
        urgency_main_layout.addWidget(self.urgency_phrases_list, 1)  # stretch factor = 1

        # 按钮布局 - 右对齐，紧凑排列
        urgency_btn_layout = QHBoxLayout()
        urgency_btn_layout.setContentsMargins(0, 5, 0, 0)
        urgency_btn_layout.addStretch()  # 左侧弹性空间，让按钮右对齐

        self.add_urgency_btn = QPushButton("➕ 添加话术")
        self.add_urgency_btn.setMaximumWidth(100)  # 限制按钮宽度
        self.add_urgency_btn.clicked.connect(lambda: self.add_closing_phrase("urgency"))
        urgency_btn_layout.addWidget(self.add_urgency_btn)

        self.remove_urgency_btn = QPushButton("➖ 删除话术")
        self.remove_urgency_btn.setMaximumWidth(100)  # 限制按钮宽度
        self.remove_urgency_btn.clicked.connect(lambda: self.remove_closing_phrase("urgency"))
        urgency_btn_layout.addWidget(self.remove_urgency_btn)

        urgency_main_layout.addLayout(urgency_btn_layout)
        
        techniques_tab.addTab(urgency_widget, "紧迫成交")
        
        techniques_layout.addWidget(techniques_tab)
        layout.addWidget(techniques_group)
        
        # 加载默认成交技巧
        self.load_default_closing_techniques()

        return widget

    def load_default_stages(self):
        """从配置文件加载销售阶段（移除硬编码）"""
        # 销售阶段数据将在load_config时从配置文件加载
        # 这里不加载默认数据，避免重复
        pass

    def load_default_objections(self):
        """从配置文件加载异议处理"""
        # 异议处理数据将在load_config时从配置文件加载
        # 这里不加载默认数据，避免重复
        pass

    def load_default_closing_techniques(self):
        """从配置文件加载成交技巧（移除硬编码）"""
        # 成交技巧数据将在load_config时从配置文件加载
        # 这里不加载任何硬编码的默认数据，避免重复
        pass

    def on_stage_selected(self, current, previous):
        """选择销售阶段时的处理"""
        try:
            print(f"🖱️ 用户切换销售阶段")

            # 🔧 统一为Agent模式：只有在配置已加载完成后才保存之前的数据
            if previous and hasattr(self.parent(), 'current_config_loaded') and self.parent().current_config_loaded:
                print(f"  保存之前选中的阶段数据: {previous.text(0)}")
                # 临时设置当前项为previous，以便update_current_stage_data能正确工作
                self.stages_tree.setCurrentItem(previous)
                self.update_current_stage_data()
                # 恢复当前项
                self.stages_tree.setCurrentItem(current)

            if current:
                stage_key = current.data(0, Qt.ItemDataRole.UserRole)
                stage_data = current.data(1, Qt.ItemDataRole.UserRole)
                print(f"🔄 当前选中项: {current.text(0)}")
                print(f"  键值: {stage_key}")
                print(f"  数据: {stage_data is not None}")

                if stage_data:
                    print(f"  加载阶段详细信息: {stage_data.get('name', 'Unknown')}")
                    # 加载阶段详细信息到右侧面板
                    # 优先使用stage_data中的name，如果没有则使用stage_key的格式化版本
                    stage_name = stage_data.get('name', stage_key.replace('_', ' ').title())
                    self.stage_name_edit.setText(stage_name)
                    self.stage_objective_edit.setPlainText(stage_data.get('objective', ''))

                    # 处理duration_turns字段
                    duration = stage_data.get('duration_turns', 0)
                    if isinstance(duration, int) and duration > 0:
                        self.duration_spin.setValue(duration)
                    else:
                        self.duration_spin.setValue(3)  # 默认值

                    # 加载关键行动
                    self.key_actions_list.clear()
                    key_actions = stage_data.get('key_actions', [])
                    for action in key_actions:
                        self.key_actions_list.addItem(action)

                    # 加载触发条件
                    self.triggers_list.clear()
                    triggers = stage_data.get('transition_triggers', [])
                    for trigger in triggers:
                        self.triggers_list.addItem(trigger)

                    # 加载建议问题 - 支持多种字段名
                    questions = []
                    if 'questions_to_ask' in stage_data:
                        questions = stage_data['questions_to_ask']
                    elif 'key_questions' in stage_data:
                        questions = stage_data['key_questions']
                    elif 'questions' in stage_data:
                        questions = stage_data['questions']

                    if isinstance(questions, list):
                        self.questions_edit.setPlainText('\n'.join(questions))
                    else:
                        self.questions_edit.clear()

                    # 加载示例回复
                    sample_responses = stage_data.get('sample_responses', [])
                    if hasattr(self, 'sample_responses_list'):
                        self.sample_responses_list.clear()
                        for response in sample_responses:
                            self.sample_responses_list.addItem(response)

                    # 特殊处理：如果没有标准字段，尝试显示特殊字段的信息
                    if not key_actions and not triggers and not questions:
                        self._load_special_stage_info(stage_key, stage_data)

                    print(f"  ✅ 阶段数据加载完成")
                else:
                    print(f"  ❌ 无法获取阶段数据")
                    # 如果没有详细数据，只显示基本信息
                    self.stage_name_edit.setText(stage_key.replace('_', ' ').title())
                    self.stage_objective_edit.clear()
                    self.duration_spin.setValue(3)
                    self.key_actions_list.clear()
                    self.triggers_list.clear()
                    self.questions_edit.clear()
            else:
                print(f"  ❌ 没有选中的阶段")
        except Exception as e:
            print(f"❌ 阶段选择处理失败: {e}")
            import traceback
            traceback.print_exc()

    def _load_special_stage_info(self, stage_key, stage_data):
        """加载特殊阶段的信息"""
        if stage_key == "qualification":
            # 资格确认阶段的特殊字段
            criteria = stage_data.get('qualification_criteria', [])
            for criterion in criteria:
                self.key_actions_list.addItem(f"资格标准: {criterion}")

            disqualification = stage_data.get('disqualification_signals', [])
            for signal in disqualification:
                self.triggers_list.addItem(f"取消资格信号: {signal}")

        elif stage_key == "needs_analysis":
            # 需求分析阶段的特殊字段
            framework = stage_data.get('discovery_framework', [])
            for item in framework:
                self.key_actions_list.addItem(f"发现框架: {item}")

            pain_points = stage_data.get('pain_points_library', {})
            for category, points in pain_points.items():
                self.triggers_list.addItem(f"痛点类别: {category}")

        elif stage_key == "solution_presentation":
            # 解决方案展示阶段的特殊字段
            structure = stage_data.get('presentation_structure', [])
            for item in structure:
                self.key_actions_list.addItem(f"展示结构: {item}")

            customization = stage_data.get('customization_rules', {})
            for rule_type, rules in customization.items():
                self.triggers_list.addItem(f"定制规则: {rule_type}")

        elif stage_key == "objection_handling":
            # 异议处理阶段的特殊字段
            objections = stage_data.get('common_objections', {})
            for obj_type, obj_data in objections.items():
                self.key_actions_list.addItem(f"异议类型: {obj_type}")

        elif stage_key == "closing":
            # 成交阶段的特殊字段
            signals = stage_data.get('buying_signals', [])
            for signal in signals:
                self.triggers_list.addItem(f"购买信号: {signal}")

            techniques = stage_data.get('closing_techniques', {})
            for tech_type, tech_data in techniques.items():
                self.key_actions_list.addItem(f"成交技巧: {tech_type}")

    def on_objection_selected(self, current, previous):
        """选择异议时的处理"""
        try:
            print(f"🖱️ 用户切换异议")

            # 🔧 统一为Agent模式：只有在配置已加载完成后才保存之前的数据
            if previous and hasattr(self.parent(), 'current_config_loaded') and self.parent().current_config_loaded:
                print(f"  保存之前选中的异议数据: {previous.text()}")
                # 临时设置当前项为previous，以便update_current_objection_data能正确工作
                self.objections_list.setCurrentItem(previous)
                self.update_current_objection_data()
                # 恢复当前项
                self.objections_list.setCurrentItem(current)

            if current:
                objection_key = current.data(Qt.ItemDataRole.UserRole)
                objection_data = current.data(Qt.ItemDataRole.UserRole + 1)  # 使用不同的角色存储数据
                print(f"🔄 当前选中项: {current.text()}")
                print(f"  键值: {objection_key}")
                print(f"  数据: {objection_data}")

                if objection_data:
                    print(f"  加载异议详细信息: {objection_data.get('name', 'Unknown')}")
                    # 加载异议详细信息
                    self.objection_name_edit.setText(objection_data.get('name', objection_key.replace('_', ' ').title()))
                    self.acknowledge_edit.setPlainText(objection_data.get('acknowledge', ''))
                    self.reframe_edit.setPlainText(objection_data.get('reframe', ''))
                    self.trial_close_edit.setPlainText(objection_data.get('trial_close', ''))

                    # 加载证明点 - 优先使用动态数据
                    self.proof_points_list.clear()
                    proof_points = self.get_dynamic_proof_points(objection_key, objection_data)
                    for proof in proof_points:
                        self.proof_points_list.addItem(proof)
                    print(f"  ✅ 异议数据加载完成")
                else:
                    print(f"  ❌ 无法获取异议数据")
                    # 如果没有详细数据，只显示基本信息
                    self.objection_name_edit.setText(objection_key.replace('_', ' ').title())
                    self.acknowledge_edit.clear()
                    self.reframe_edit.clear()
                    self.trial_close_edit.clear()
                    self.proof_points_list.clear()
            else:
                print(f"  ❌ 没有选中的异议")
        except Exception as e:
            print(f"❌ 异议选择处理失败: {e}")
            import traceback
            traceback.print_exc()

    def add_stage(self):
        """添加销售阶段"""
        text, ok = QInputDialog.getText(self, "添加销售阶段", "请输入阶段名称:")
        if ok and text:
            # 创建新阶段数据
            stage_data = {
                'name': text,
                'objective': '新阶段目标',
                'duration_turns': 3,
                'key_actions': [],
                'transition_triggers': [],
                'questions': []
            }

            item = QTreeWidgetItem([text, "新阶段目标", "3"])
            item.setData(0, Qt.ItemDataRole.UserRole, text.lower().replace(' ', '_'))
            item.setData(1, Qt.ItemDataRole.UserRole, stage_data)
            self.stages_tree.addTopLevelItem(item)
            self.stages_tree.setCurrentItem(item)  # 自动选中新添加的项
            self.on_config_changed()

    def remove_stage(self):
        """删除销售阶段"""
        current = self.stages_tree.currentItem()
        if current:
            index = self.stages_tree.indexOfTopLevelItem(current)
            self.stages_tree.takeTopLevelItem(index)
            self.on_config_changed()

    def add_key_action(self):
        """添加关键行动"""
        text, ok = QInputDialog.getText(self, "添加关键行动", "请输入行动描述:")
        if ok and text:
            self.key_actions_list.addItem(text)
            self.update_current_stage_data()
            self.on_config_changed()

    def remove_key_action(self):
        """删除关键行动"""
        current_row = self.key_actions_list.currentRow()
        if current_row >= 0:
            self.key_actions_list.takeItem(current_row)
            self.update_current_stage_data()
            self.on_config_changed()

    def add_trigger(self):
        """添加触发条件"""
        text, ok = QInputDialog.getText(self, "添加触发条件", "请输入触发条件:")
        if ok and text:
            self.triggers_list.addItem(text)
            self.update_current_stage_data()
            self.on_config_changed()

    def remove_trigger(self):
        """删除触发条件"""
        current_row = self.triggers_list.currentRow()
        if current_row >= 0:
            self.triggers_list.takeItem(current_row)
            self.update_current_stage_data()
            self.on_config_changed()

    def add_objection(self):
        """添加异议类型"""
        text, ok = QInputDialog.getText(self, "添加异议类型", "请输入异议名称:")
        if ok and text:
            # 创建新异议数据
            objection_data = {
                'name': text,
                'acknowledge': '',
                'reframe': '',
                'proof_points': [],
                'trial_close': ''
            }

            item = QListWidgetItem(text)
            item.setData(Qt.ItemDataRole.UserRole, text.lower().replace(' ', '_'))
            item.setData(Qt.ItemDataRole.UserRole + 1, objection_data)
            self.objections_list.addItem(item)
            self.objections_list.setCurrentItem(item)  # 自动选中新添加的项
            self.on_config_changed()

    def remove_objection(self):
        """删除异议类型"""
        current_row = self.objections_list.currentRow()
        if current_row >= 0:
            self.objections_list.takeItem(current_row)
            self.on_config_changed()

    def add_proof_point(self):
        """添加证明点"""
        text, ok = QInputDialog.getText(self, "添加证明点", "请输入证明点:")
        if ok and text:
            self.proof_points_list.addItem(text)
            self.update_current_objection_data()
            self.on_config_changed()

    def remove_proof_point(self):
        """删除证明点"""
        current_row = self.proof_points_list.currentRow()
        if current_row >= 0:
            self.proof_points_list.takeItem(current_row)
            self.update_current_objection_data()
            self.on_config_changed()

    def add_buying_signal(self):
        """添加购买信号"""
        text, ok = QInputDialog.getText(self, "添加购买信号", "请输入购买信号:")
        if ok and text:
            self.buying_signals_list.addItem(text)
            self.on_config_changed()

    def remove_buying_signal(self):
        """删除购买信号"""
        current_row = self.buying_signals_list.currentRow()
        if current_row >= 0:
            self.buying_signals_list.takeItem(current_row)
            self.on_config_changed()

    def add_closing_phrase(self, technique_type):
        """添加成交话术"""
        text, ok = QInputDialog.getText(self, f"添加{technique_type}话术", "请输入话术:")
        if ok and text:
            if technique_type == "assumptive":
                self.assumptive_phrases_list.addItem(text)
            elif technique_type == "alternative":
                self.alternative_phrases_list.addItem(text)
            elif technique_type == "urgency":
                self.urgency_phrases_list.addItem(text)
            self.on_config_changed()

    def remove_closing_phrase(self, technique_type):
        """删除成交话术"""
        current_row = -1
        list_widget = None

        if technique_type == "assumptive":
            list_widget = self.assumptive_phrases_list
            current_row = self.assumptive_phrases_list.currentRow()
        elif technique_type == "alternative":
            list_widget = self.alternative_phrases_list
            current_row = self.alternative_phrases_list.currentRow()
        elif technique_type == "urgency":
            list_widget = self.urgency_phrases_list
            current_row = self.urgency_phrases_list.currentRow()

        if list_widget and current_row >= 0:
            list_widget.takeItem(current_row)
            self.on_config_changed()

    def load_config(self, config_data):
        """加载配置数据"""
        print(f"🔄 销售流程模块 load_config() 被调用，配置数据键: {list(config_data.keys()) if config_data else '无数据'}")
        self.config_data = config_data

        # 🔧 统一为Agent模式：直接加载，依赖current_config_loaded保护
        # 加载销售阶段数据
        stages_data = config_data.get('stages', {})
        print(f"📊 获取到的stages数据: {list(stages_data.keys()) if stages_data else '无stages数据'}")
        if stages_data:
            self.stages_tree.clear()
            loaded_stages = 0

            for stage_key, stage_info in stages_data.items():
                # 跳过注释条目
                if stage_key.startswith('_comment'):
                    continue

                if isinstance(stage_info, dict):
                    # 确保阶段数据有name字段
                    if 'name' not in stage_info:
                        stage_info['name'] = stage_key.replace('_', ' ').title()

                    objective = stage_info.get('objective', '')
                    duration = stage_info.get('duration_turns', 0)

                    # 如果没有duration_turns，尝试从其他字段推断
                    if duration == 0:
                        if stage_key == "objection_handling":
                            duration = "多轮"
                        elif stage_key == "closing":
                            duration = "1-3"
                        else:
                            duration = "未设置"

                    item = QTreeWidgetItem([
                        stage_info['name'],  # 使用name字段而不是stage_key
                        objective,
                        str(duration)
                    ])
                    item.setData(0, Qt.ItemDataRole.UserRole, stage_key)
                    item.setData(1, Qt.ItemDataRole.UserRole, stage_info)  # 存储完整的阶段数据
                    self.stages_tree.addTopLevelItem(item)
                    loaded_stages += 1

            print(f"✅ 已加载 {loaded_stages} 个销售阶段")

        # 加载异议处理数据
        stages_data = config_data.get('stages', {})
        objection_handling_stage = stages_data.get('objection_handling', {})
        objections_data = objection_handling_stage.get('common_objections', {})

        if objections_data:
            self.objections_list.clear()  # 使用正确的控件名称
            loaded_objections = 0

            for objection_key, objection_info in objections_data.items():
                if isinstance(objection_info, dict):
                    # 确保异议数据有name字段
                    if 'name' not in objection_info:
                        objection_info['name'] = objection_key.replace('_', ' ').title()

                    acknowledge = objection_info.get('acknowledge', '')
                    display_name = objection_info['name']  # 使用name字段

                    item = QListWidgetItem(display_name)
                    item.setData(Qt.ItemDataRole.UserRole, objection_key)
                    item.setData(Qt.ItemDataRole.UserRole + 1, objection_info)  # 存储完整数据
                    self.objections_list.addItem(item)
                    loaded_objections += 1

            print(f"✅ 已加载 {loaded_objections} 个异议处理策略")
        else:
            print("⚠️ 配置文件中未找到异议处理数据")

        # 加载成交技巧数据
        closing_stage = stages_data.get('closing', {})
        if closing_stage:
            # 加载购买信号
            buying_signals = closing_stage.get('buying_signals', [])
            if buying_signals:
                self.buying_signals_list.clear()
                for signal in buying_signals:
                    self.buying_signals_list.addItem(signal)
                print(f"✅ 已加载 {len(buying_signals)} 个购买信号")

            # 加载成交技巧
            techniques = closing_stage.get('closing_techniques', {})
            if techniques:
                # 清空现有列表
                self.assumptive_phrases_list.clear()
                self.alternative_phrases_list.clear()
                self.urgency_phrases_list.clear()

                loaded_techniques = 0
                for technique_type, technique_data in techniques.items():
                    if isinstance(technique_data, dict):
                        # 处理有phrases字段的技巧
                        if 'phrases' in technique_data:
                            phrases = technique_data['phrases']
                            for phrase in phrases:
                                if technique_type == "assumptive_close":
                                    self.assumptive_phrases_list.addItem(phrase)
                                    loaded_techniques += 1
                                elif technique_type == "alternative_close":
                                    self.alternative_phrases_list.addItem(phrase)
                                    loaded_techniques += 1
                                elif technique_type == "urgency_close":
                                    self.urgency_phrases_list.addItem(phrase)
                                    loaded_techniques += 1

                        # 处理summary_close的template字段
                        elif technique_type == "summary_close" and 'template' in technique_data:
                            template = technique_data['template']
                            # 可以添加到一个专门的summary列表，或者添加到assumptive列表
                            self.assumptive_phrases_list.addItem(f"Summary: {template}")
                            loaded_techniques += 1

                print(f"✅ 已加载 {loaded_techniques} 个成交技巧话术")
            else:
                print("⚠️ 配置文件中未找到成交技巧数据")
        else:
            print("⚠️ 配置文件中未找到成交阶段数据")

        # 默认选择第一个阶段
        if self.stages_tree.topLevelItemCount() > 0:
            self.stages_tree.setCurrentItem(self.stages_tree.topLevelItem(0))

        print("✅ 销售流程配置加载完成")

    def get_config(self):
        """获取当前配置"""
        print("🔄 销售流程模块 get_config() 被调用")

        # 强制让所有控件失去焦点，确保内容被提交
        self.setFocus()

        # 保存当前编辑的数据
        self.update_current_stage_data()
        self.update_current_objection_data()
        # 成交技巧数据直接从列表控件收集，不需要单独的update方法

        # 重新构建配置数据，确保所有UI修改都被包含
        updated_config = {}

        # 收集阶段数据
        stages = {}
        print(f"📊 收集销售阶段数据，共 {self.stages_tree.topLevelItemCount()} 个阶段")
        for i in range(self.stages_tree.topLevelItemCount()):
            item = self.stages_tree.topLevelItem(i)
            stage_key = item.data(0, Qt.ItemDataRole.UserRole)
            stage_data = item.data(1, Qt.ItemDataRole.UserRole)
            if stage_key and stage_data:
                print(f"  阶段 {stage_key}: {stage_data.get('name', 'No Name')} - {stage_data.get('objective', 'No Objective')[:30]}...")

                # 调试：检查config_data中的对应数据
                if hasattr(self, 'config_data') and 'stages' in self.config_data and stage_key in self.config_data['stages']:
                    config_data_stage = self.config_data['stages'][stage_key]
                    config_name = config_data_stage.get('name', 'No Name')
                    config_objective = config_data_stage.get('objective', '')
                    tree_name = stage_data.get('name', 'No Name')
                    tree_objective = stage_data.get('objective', '')

                    print(f"    🔍 详细数据对比:")
                    print(f"      名称 - config: '{config_name}' vs tree: '{tree_name}'")
                    print(f"      目标 - config: '{config_objective[:50]}...' vs tree: '{tree_objective[:50]}...'")
                    print(f"      关键行动 - config: {len(config_data_stage.get('key_actions', []))} vs tree: {len(stage_data.get('key_actions', []))}")
                    print(f"      触发条件 - config: {len(config_data_stage.get('transition_triggers', []))} vs tree: {len(stage_data.get('transition_triggers', []))}")

                    # 🔧 修复：始终优先使用最新的 config_data，因为它包含了最新的UI修改
                    print(f"    ✅ 优先使用最新的 config_data")
                    stages[stage_key] = config_data_stage
                else:
                    print(f"    📝 使用 tree_data（config_data 中无对应数据）")
                    stages[stage_key] = stage_data

        # 收集异议处理数据
        objections = {}
        if hasattr(self, 'objections_list'):
            for i in range(self.objections_list.count()):
                item = self.objections_list.item(i)
                objection_key = item.data(Qt.ItemDataRole.UserRole)
                objection_data = item.data(Qt.ItemDataRole.UserRole + 1)
                if objection_key and objection_data:
                    objections[objection_key] = objection_data

        # 将异议处理数据添加到objection_handling阶段
        if objections and 'objection_handling' in stages:
            stages['objection_handling']['common_objections'] = objections
        elif objections:
            # 如果没有objection_handling阶段，创建一个
            stages['objection_handling'] = {
                'name': 'Objection Handling',
                'objective': 'Address customer concerns and objections',
                'duration_turns': 3,
                'common_objections': objections
            }

        # 收集成交技巧数据
        if hasattr(self, 'buying_signals_list') and hasattr(self, 'assumptive_phrases_list'):
            # 收集购买信号
            buying_signals = []
            for i in range(self.buying_signals_list.count()):
                buying_signals.append(self.buying_signals_list.item(i).text())

            # 收集成交话术
            closing_techniques = {}

            # 假设性成交
            assumptive_phrases = []
            for i in range(self.assumptive_phrases_list.count()):
                assumptive_phrases.append(self.assumptive_phrases_list.item(i).text())
            if assumptive_phrases:
                closing_techniques['assumptive_close'] = {'phrases': assumptive_phrases}

            # 选择性成交
            alternative_phrases = []
            for i in range(self.alternative_phrases_list.count()):
                alternative_phrases.append(self.alternative_phrases_list.item(i).text())
            if alternative_phrases:
                closing_techniques['alternative_close'] = {'phrases': alternative_phrases}

            # 紧迫性成交
            urgency_phrases = []
            for i in range(self.urgency_phrases_list.count()):
                urgency_phrases.append(self.urgency_phrases_list.item(i).text())
            if urgency_phrases:
                closing_techniques['urgency_close'] = {'phrases': urgency_phrases}

            # 将成交技巧数据添加到closing阶段
            if (buying_signals or closing_techniques) and 'closing' in stages:
                if buying_signals:
                    stages['closing']['buying_signals'] = buying_signals
                if closing_techniques:
                    stages['closing']['closing_techniques'] = closing_techniques
            elif buying_signals or closing_techniques:
                # 如果没有closing阶段，创建一个
                closing_stage = {
                    'name': 'Closing',
                    'objective': 'Close the sale and secure commitment',
                    'duration_turns': 2
                }
                if buying_signals:
                    closing_stage['buying_signals'] = buying_signals
                if closing_techniques:
                    closing_stage['closing_techniques'] = closing_techniques
                stages['closing'] = closing_stage

        if stages:
            updated_config['stages'] = stages

        # 保留所有原始配置数据，包括注释
        for key, value in self.config_data.items():
            if key not in updated_config:
                updated_config[key] = value

        print(f"📤 销售流程模块返回配置数据，包含键: {list(updated_config.keys())}")
        print(f"📊 其中stages包含 {len(updated_config.get('stages', {}))} 个阶段")

        # 详细打印保存的阶段数据
        if 'stages' in updated_config:
            for stage_key, stage_data in updated_config['stages'].items():
                print(f"  📋 保存阶段 {stage_key}: name='{stage_data.get('name', 'No Name')}', objective='{stage_data.get('objective', 'No Objective')[:50]}...'")

        return updated_config

    def update_current_stage_data(self):
        """更新当前选中阶段的数据"""
        current = self.stages_tree.currentItem()
        if current:
            stage_key = current.data(0, Qt.ItemDataRole.UserRole)  # 获取阶段键
            if stage_key:
                print(f"🔧 更新阶段数据: {stage_key}")

                # 🔧 修复：直接从UI控件读取数据，然后同时更新UserRole和config_data
                new_name = self.stage_name_edit.text()
                new_objective = self.stage_objective_edit.toPlainText()
                new_duration = self.duration_spin.value()

                print(f"  🔍 UI控件读取值:")
                print(f"    名称: '{new_name}' (控件: {type(self.stage_name_edit).__name__})")
                print(f"    目标: '{new_objective[:50]}...' (控件: {type(self.stage_objective_edit).__name__})")
                print(f"    持续轮数: {new_duration} (控件: {type(self.duration_spin).__name__})")

                # 收集关键行动
                key_actions = []
                for i in range(self.key_actions_list.count()):
                    action_text = self.key_actions_list.item(i).text()
                    key_actions.append(action_text)
                    print(f"    关键行动 {i+1}: '{action_text}'")
                print(f"  关键行动: {len(key_actions)} 项")

                # 收集触发条件
                triggers = []
                for i in range(self.triggers_list.count()):
                    triggers.append(self.triggers_list.item(i).text())
                print(f"  触发条件: {len(triggers)} 项")

                # 收集建议问题
                questions_text = self.questions_edit.toPlainText()
                questions_list = [q.strip() for q in questions_text.split('\n') if q.strip()] if questions_text else []
                print(f"  建议问题: {len(questions_list)} 项")

                # 🔧 修复：构建完整的阶段数据
                updated_stage_data = {
                    'name': new_name,
                    'objective': new_objective,
                    'duration_turns': new_duration,
                    'key_actions': key_actions,
                    'transition_triggers': triggers,
                    'questions': questions_list
                }

                # 🔧 修复：同时更新UserRole数据和config_data
                current.setData(1, Qt.ItemDataRole.UserRole, updated_stage_data)

                # 更新config_data中的对应数据
                if not hasattr(self, 'config_data'):
                    self.config_data = {}
                if 'stages' not in self.config_data:
                    self.config_data['stages'] = {}

                self.config_data['stages'][stage_key] = updated_stage_data.copy()

                print(f"  ✅ 阶段数据已同步到UserRole和config_data")
                print(f"  📝 最终数据: name='{new_name}', objective='{new_objective[:30]}...', actions={len(key_actions)}, triggers={len(triggers)}, questions={len(questions_list)}")

                # 🔧 修复：更新树形控件的显示文本
                current.setText(0, new_name)
                current.setText(1, new_objective[:50] + "..." if len(new_objective) > 50 else new_objective)
                current.setText(2, str(new_duration))

                # 🔧 移除信号发射，避免循环调用
                # 信号发射由调用者负责

    def update_current_objection_data(self):
        """更新当前选中异议的数据"""
        current = self.objections_list.currentItem()
        if current:
            objection_key = current.data(Qt.ItemDataRole.UserRole)
            objection_data = current.data(Qt.ItemDataRole.UserRole + 1)
            if objection_data and objection_key:
                print(f"🔧 更新异议数据: {objection_data.get('name', 'Unknown')}")

                # 🔧 修复：直接从UI控件读取数据，然后同时更新UserRole和config_data
                old_name = objection_data.get('name', '')
                new_name = self.objection_name_edit.text()
                new_acknowledge = self.acknowledge_edit.toPlainText()
                new_reframe = self.reframe_edit.toPlainText()
                new_trial_close = self.trial_close_edit.toPlainText()

                print(f"  🔍 UI控件读取值:")
                print(f"    名称: '{new_name}' (控件: {type(self.objection_name_edit).__name__})")
                print(f"    认同话术: '{new_acknowledge[:50]}...' (控件: {type(self.acknowledge_edit).__name__})")
                print(f"    重新定义: '{new_reframe[:50]}...' (控件: {type(self.reframe_edit).__name__})")
                print(f"    试探性成交: '{new_trial_close[:50]}...' (控件: {type(self.trial_close_edit).__name__})")

                # 收集证明点
                proof_points = []
                for i in range(self.proof_points_list.count()):
                    proof_points.append(self.proof_points_list.item(i).text())
                print(f"  证明点: {len(proof_points)} 项")

                # 🔧 修复：构建完整的异议数据
                updated_objection_data = {
                    'name': new_name,
                    'acknowledge': new_acknowledge,
                    'reframe': new_reframe,
                    'trial_close': new_trial_close,
                    'proof_points': proof_points
                }

                # 🔧 修复：同时更新UserRole数据和config_data
                current.setData(Qt.ItemDataRole.UserRole + 1, updated_objection_data)

                # 更新config_data中的对应数据
                if not hasattr(self, 'config_data'):
                    self.config_data = {}
                if 'stages' not in self.config_data:
                    self.config_data['stages'] = {}
                if 'objection_handling' not in self.config_data['stages']:
                    self.config_data['stages']['objection_handling'] = {
                        'name': 'Objection Handling',
                        'objective': 'Address customer concerns and objections',
                        'duration_turns': 3,
                        'common_objections': {}
                    }

                # 确保common_objections存在
                if 'common_objections' not in self.config_data['stages']['objection_handling']:
                    self.config_data['stages']['objection_handling']['common_objections'] = {}

                self.config_data['stages']['objection_handling']['common_objections'][objection_key] = updated_objection_data.copy()

                print(f"  ✅ 异议数据已同步到UserRole和config_data")
                print(f"  📝 最终数据: name='{new_name}', acknowledge='{new_acknowledge[:30]}...', reframe='{new_reframe[:30]}...', proof_points={len(proof_points)}")

                # 🔧 修复：更新列表控件的显示文本
                current.setText(updated_objection_data['name'])
                print(f"✅ 异议数据更新完成")

    def get_dynamic_proof_points(self, objection_key, objection_data):
        """获取动态证明点"""
        # 配置文件编辑器不需要动态生成，直接从配置数据获取
        default_points = objection_data.get('proof_points', [])

        # 如果配置中没有证明点，提供一些默认的证明点
        if not default_points:
            default_points = self.get_fallback_proof_points(objection_key)

        return default_points

    def get_fallback_proof_points(self, objection_key):
        """获取备用证明点（移除硬编码）"""
        # 配置文件编辑器不应该提供硬编码的备用数据
        # 如果配置文件中没有证明点，提示用户在配置文件中添加
        return [f"请在配置文件中为 '{objection_key}' 添加证明点"]

    def set_data_connector(self, connector):
        """设置数据连接器"""
        global data_connector
        data_connector = connector
        if hasattr(connector, 'sales_process_widget'):
            connector.sales_process_widget = self

    def refresh_proof_points(self):
        """刷新当前异议的证明点"""
        current_item = self.objections_list.currentItem()
        if current_item:
            objection_key = current_item.data(Qt.ItemDataRole.UserRole)
            objection_data = current_item.data(Qt.ItemDataRole.UserRole + 1)

            if objection_data:
                # 重新加载证明点
                self.proof_points_list.clear()
                proof_points = self.get_dynamic_proof_points(objection_key, objection_data)
                for proof in proof_points:
                    self.proof_points_list.addItem(proof)

    def on_data_changed(self):
        """数据改变时的实时处理（带时序保护）"""
        # 🔧 统一为Agent模式：依赖main_window的current_config_loaded保护
        # 只有在配置已加载完成后才处理数据变更
        if hasattr(self.parent(), 'current_config_loaded') and self.parent().current_config_loaded:
            # 立即更新当前选中的数据
            self.update_current_stage_data()
            self.update_current_objection_data()
        # 直接发射信号，让main_window处理
        self.config_changed.emit()

    def on_config_changed(self):
        """配置改变时的处理"""
        print(f"📝 配置发生变化，触发保存")
        # 🔧 修复：与agent模块保持一致，只发射信号，不做数据更新
        self.config_changed.emit()
        print(f"✅ 配置变化处理完成")


