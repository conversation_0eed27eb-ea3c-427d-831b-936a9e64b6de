{% comment %}
YF AI Chat Widget for Shopify
在主题的 theme.liquid 文件的 </body> 标签前包含此代码片段：
{% include 'yf-chat-widget' %}
{% endcomment %}

<!-- YF AI Chat Widget CSS -->
{{ 'yf-chat-shopify.css' | asset_url | stylesheet_tag }}

<!-- YF AI Chat Widget JavaScript -->
<script>
// 🔒 安全配置：不在控制台暴露任何敏感信息
console.log('YF AI Chat: Initializing...');

// 🚨 重要安全提醒：请在生产环境中修改以下配置
// ⚠️ 不要在浏览器控制台中暴露API URL和Token
window.YF_CHAT_CONFIG = {
    // 🔧 需要配置的参数 - 请修改这些值
    apiUrl: 'https://chat.22668.xyz',  // 后端API地址 (通过Cloudflare隧道转发)
    apiToken: 'qQiV0ZZJeL9A7rAgAt1BA89vNyVPAGVb',  // API Token (使用WordPress Token)

    // 界面配置
    position: 'bottom-right',  // 位置: top-left, top-right, bottom-left, bottom-right
    margin: 100,               // 距离边缘的像素

    // 文本配置
    strings: {
        title: 'Customer Support',
        placeholder: 'Type your message...',
        welcomeMessage: 'Hello! That is a beautiful day uh？',
        assistantName: 'Dylan',
        error: 'I apologize, but I\'m experiencing technical difficulties. Please try again later.'
    },

    // 功能配置
    settings: {
        sessionTimeout: 30 * 60 * 1000,  // 30分钟会话超时
        saveHistory: true                 // 是否保存聊天历史
    }
};

// 🔒 安全验证：不暴露配置详情
if (!window.YF_CHAT_CONFIG.apiUrl) {
    console.error('YF AI Chat: Configuration required');
    window.YF_CHAT_CONFIG = null; // 标记配置无效
}

if (!window.YF_CHAT_CONFIG.apiToken || window.YF_CHAT_CONFIG.apiToken.length < 10) {
    console.error('YF AI Chat: Authentication required');
    window.YF_CHAT_CONFIG = null; // 标记配置无效
}

// console.log('YF AI Chat: Configuration validated');

// 🛍️ Shopify特有功能：获取当前页面商品信息
if (typeof window.ShopifyAnalytics !== 'undefined' && window.ShopifyAnalytics.meta && window.ShopifyAnalytics.meta.product) {
    window.YF_CHAT_SHOPIFY_PRODUCT = {
        id: window.ShopifyAnalytics.meta.product.id,
        title: window.ShopifyAnalytics.meta.product.title,
        vendor: window.ShopifyAnalytics.meta.product.vendor,
        type: window.ShopifyAnalytics.meta.product.type,
        price: window.ShopifyAnalytics.meta.product.price,
        url: window.location.href
    };
    // 🔒 不暴露产品详细信息
    console.log('Shopify product information loaded');
}

// 🛒 Shopify购物车功能
window.YF_CHAT_SHOPIFY_CART = {
    // 添加商品到购物车
    addToCart: function(variantId, quantity = 1) {
        return fetch('/cart/add.js', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: variantId,
                quantity: quantity
            })
        })
        .then(response => response.json())
        .then(data => {
            // 🔒 不暴露购物车详情
            // console.log('Product added to cart');
            return data;
        })
        .catch(error => {
            // 🔒 不暴露错误详情
            // console.log('Cart operation failed');
            throw error;
        });
    },
    
    // 获取购物车信息
    getCart: function() {
        return fetch('/cart.js')
        .then(response => response.json())
        .then(data => {
            // 🔒 不暴露购物车详情
            // console.log('Cart information loaded');
            return data;
        })
        .catch(error => {
            // 🔒 不暴露错误详情
            // console.log('Cart loading failed');
            throw error;
        });
    }
};
</script>

<!-- 加载主要的聊天脚本 -->
{{ 'yf-chat-shopify.js' | asset_url | append: '?v=' | append: 'now' | script_tag }}

{% comment %}
使用说明：

1. 配置API信息
   - 修改上面的 apiUrl 和 apiToken
   - apiToken 使用与WordPress插件相同的token

2. 自定义界面
   - 修改 position: 聊天按钮位置
   - 修改 margin: 距离边缘的距离
   - 修改 strings: 界面文本

3. Shopify特有功能
   - 自动获取当前页面商品信息
   - 提供购物车操作API
   - 可以在AI回复中引用商品信息

4. 安装方法
   在 theme.liquid 文件的 </body> 标签前添加：
   {% include 'yf-chat-widget' %}

5. 文件上传
   - 将 yf-chat-shopify.css 上传到 assets/ 目录
   - 将 yf-chat-shopify.js 上传到 assets/ 目录
   - 将此文件上传到 snippets/ 目录
{% endcomment %}
