"""
升级规则服务
实现自动升级、手动升级、上下文传递等功能
"""

import json
import os
import re
from typing import Dict, Any, Tuple, List, Optional
from datetime import datetime
import logging
from utils.path_utils import get_salesgpt_config_directory

logger = logging.getLogger(__name__)


class EscalationService:
    """升级规则服务"""
    
    def __init__(self):
        self.config = {}
        self.escalation_history = {}  # 存储升级历史
        self.load_config()
    
    def load_config(self):
        """加载升级规则配置"""
        try:
            config_dir = get_salesgpt_config_directory()
            config_path = config_dir / 'advanced_sales_config.json'
            with open(config_path, 'r', encoding='utf-8') as f:
                full_config = json.load(f)
                self.config = full_config.get('escalation_rules', {})
            logger.info("升级规则配置加载成功")
        except Exception as e:
            logger.error(f"加载升级规则配置失败: {e}")
            self.config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置（移除硬编码，返回基本配置）"""
        logger.warning("升级规则配置文件加载失败，使用基本配置。请检查配置文件是否存在且格式正确。")
        return {
            "human_handover": {
                "auto_trigger": False,
                "manual_only": True,
                "context_transfer": True
            },
            "escalation_triggers": {}
        }
    
    def check_auto_escalation(self, message: str, session_id: str, conversation_history: List[Dict] = None) -> Tuple[bool, str, str]:
        """检查是否需要自动升级"""
        # 移除人工接管设置检查 - AI默认按照规则自动判断
        escalation_triggers = self.config.get('escalation_triggers', {})

        # 🔧 添加调试日志
        logger.debug(f"检查升级规则 - 消息: '{message}', 会话: {session_id}")

        # 检查客户沮丧情绪（移除enabled检查 - 既然配置了规则就默认启用）
        frustration_config = escalation_triggers.get('customer_frustration', {})
        if frustration_config and self._detect_frustration(message, frustration_config.get('keywords', [])):
            action = frustration_config.get('action', 'notify_supervisor')
            logger.info(f"检测到客户沮丧情绪 - 消息: '{message}'")
            return True, 'customer_frustration', action

        # 检查复杂请求（移除enabled检查 - 既然配置了规则就默认启用）
        complex_config = escalation_triggers.get('complex_request', {})
        if complex_config and self._detect_complex_request(message, complex_config.get('keywords', [])):
            action = complex_config.get('action', 'transfer_to_specialist')
            logger.info(f"检测到复杂请求 - 消息: '{message}'")
            return True, 'complex_request', action

        # 检查重复问题（如果客户多次询问同一问题）
        if conversation_history and self._detect_repeated_issues(conversation_history):
            logger.info(f"检测到重复问题 - 消息: '{message}'")
            return True, 'repeated_issues', 'transfer_to_specialist'

        logger.debug(f"未触发升级规则 - 消息: '{message}'")
        return False, "", ""
    
    def _detect_frustration(self, message: str, keywords: List[str]) -> bool:
        """检测客户沮丧情绪"""
        message_lower = message.lower()
        
        # 检查关键词
        for keyword in keywords:
            if keyword.lower() in message_lower:
                return True
        
        # 检查情绪模式
        frustration_patterns = [
            r'\b(this is ridiculous|this is stupid|waste of time|terrible service)\b',
            r'\b(not working|doesn\'t work|broken|useless|horrible)\b',
            r'\b(angry|mad|furious|outraged|livid)\b',
            r'[!]{2,}',  # 多个感叹号
            r'[A-Z]{5,}',  # 大写字母（表示愤怒）
        ]
        
        for pattern in frustration_patterns:
            if re.search(pattern, message, re.IGNORECASE):
                return True
        
        return False
    
    def _detect_complex_request(self, message: str, keywords: List[str]) -> bool:
        """检测复杂请求"""
        message_lower = message.lower()
        
        # 检查关键词
        for keyword in keywords:
            if keyword.lower() in message_lower:
                return True
        
        # 检查复杂请求模式
        complex_patterns = [
            r'\b(custom|customize|personalize|tailor|modify)\b',
            r'\b(bulk|wholesale|large quantity|enterprise|business)\b',
            r'\b(integration|api|technical specification|detailed requirement)\b',
            r'\b(special|unique|specific|particular) (need|requirement|request)\b'
        ]
        
        for pattern in complex_patterns:
            if re.search(pattern, message, re.IGNORECASE):
                return True
        
        return False
    
    def _detect_repeated_issues(self, conversation_history: List[Dict]) -> bool:
        """检测重复问题"""
        if len(conversation_history) < 6:  # 至少需要3轮对话
            return False
        
        # 简单的重复检测：检查最近的用户消息是否相似
        user_messages = [msg['content'] for msg in conversation_history if msg.get('role') == 'user']
        
        if len(user_messages) < 3:
            return False
        
        # 检查最近3条消息的相似性
        recent_messages = user_messages[-3:]
        similarity_threshold = 0.7
        
        for i in range(len(recent_messages)):
            for j in range(i + 1, len(recent_messages)):
                if self._calculate_similarity(recent_messages[i], recent_messages[j]) > similarity_threshold:
                    return True
        
        return False
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似性（简单实现）"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    def initiate_handover(self, session_id: str, reason: str, conversation_history: List[Dict] = None) -> Dict[str, Any]:
        """启动人工接管"""
        handover_config = self.config.get('human_handover', {})
        
        handover_data = {
            'session_id': session_id,
            'timestamp': datetime.now().isoformat(),
            'reason': reason,
            'context_included': False,
            'conversation_context': []
        }
        
        # 如果启用上下文传递，包含对话历史
        if handover_config.get('context_transfer', True) and conversation_history:
            handover_data['context_included'] = True
            handover_data['conversation_context'] = self._prepare_context(conversation_history)
        
        # 记录升级历史
        if session_id not in self.escalation_history:
            self.escalation_history[session_id] = []
        
        self.escalation_history[session_id].append({
            'timestamp': datetime.now().isoformat(),
            'reason': reason,
            'type': 'handover'
        })
        
        logger.info(f"启动人工接管 - 会话: {session_id}, 原因: {reason}")
        
        return handover_data
    
    def _prepare_context(self, conversation_history: List[Dict]) -> List[Dict]:
        """准备上下文信息"""
        # 只包含最近的对话内容，避免过长
        max_context_messages = 10
        recent_history = conversation_history[-max_context_messages:] if len(conversation_history) > max_context_messages else conversation_history
        
        # 清理和格式化上下文
        context = []
        for msg in recent_history:
            context.append({
                'role': msg.get('role', 'unknown'),
                'content': msg.get('content', ''),
                'timestamp': msg.get('timestamp', datetime.now().isoformat())
            })
        
        return context
    
    def is_manual_only(self) -> bool:
        """检查是否仅允许手动升级"""
        handover_config = self.config.get('human_handover', {})
        return handover_config.get('manual_only', True)
    
    def is_auto_trigger_enabled(self) -> bool:
        """检查是否启用自动触发"""
        handover_config = self.config.get('human_handover', {})
        return handover_config.get('auto_trigger', False)
    
    def is_context_transfer_enabled(self) -> bool:
        """检查是否启用上下文传递"""
        handover_config = self.config.get('human_handover', {})
        return handover_config.get('context_transfer', True)
    
    def get_escalation_history(self, session_id: str) -> List[Dict]:
        """获取升级历史"""
        return self.escalation_history.get(session_id, [])
    
    def get_escalation_message(self) -> str:
        """获取售后升级消息"""
        # 从售后支持配置中获取升级消息
        try:
            config_dir = get_salesgpt_config_directory()
            config_path = config_dir / 'advanced_sales_config.json'
            with open(config_path, 'r', encoding='utf-8') as f:
                full_config = json.load(f)
                after_sales = full_config.get('after_sales_support', {})
                advanced_support = after_sales.get('advanced_support', {})
                return advanced_support.get('escalation_message',
                    'I understand this is a complex issue that requires specialized attention. '
                    'I\'ll escalate this to our advanced support team for you.')
        except Exception:
            return 'I\'ll escalate this to our advanced support team for you.'


# 全局实例
escalation_service = EscalationService()
