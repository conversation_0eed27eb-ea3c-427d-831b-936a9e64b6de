"""
优化的日志配置
过滤无关的网络连接警告，保持重要日志的可见性
"""

import logging
import sys
from typing import Dict, Any

class ConnectionErrorFilter(logging.Filter):
    """过滤连接相关的无关错误"""
    
    def filter(self, record: logging.LogRecord) -> bool:
        # 过滤的错误类型
        filtered_messages = [
            "Invalid HTTP request received",
            "An existing connection was forcibly closed by the remote host",
            "ConnectionResetError",
            "_ProactorBasePipeTransport._call_connection_lost",
            "[WinError 10054]"
        ]
        
        # 检查是否包含需要过滤的消息
        message = record.getMessage()
        for filtered_msg in filtered_messages:
            if filtered_msg in message:
                return False
        
        return True

class UvicornAccessFilter(logging.Filter):
    """过滤Uvicorn访问日志中的无关信息"""
    
    def filter(self, record: logging.LogRecord) -> bool:
        # 过滤扫描器和无效请求
        message = record.getMessage()
        
        # 过滤常见的扫描器请求
        scanner_patterns = [
            "GET /",
            "GET /favicon.ico",
            "GET /robots.txt",
            "GET /.well-known",
            "GET /wp-admin",
            "GET /admin",
            "POST /",
            "HEAD /",
            "OPTIONS /",
        ]
        
        for pattern in scanner_patterns:
            if pattern in message and ("404" in message or "405" in message):
                return False
        
        return True

def setup_logging_filters():
    """设置日志过滤器"""
    
    # 为uvicorn.error添加连接错误过滤器
    uvicorn_error_logger = logging.getLogger("uvicorn.error")
    uvicorn_error_logger.addFilter(ConnectionErrorFilter())
    
    # 为uvicorn.access添加访问过滤器
    uvicorn_access_logger = logging.getLogger("uvicorn.access")
    uvicorn_access_logger.addFilter(UvicornAccessFilter())
    
    # 为asyncio添加连接错误过滤器
    asyncio_logger = logging.getLogger("asyncio")
    asyncio_logger.addFilter(ConnectionErrorFilter())
    
    # 设置日志级别
    uvicorn_error_logger.setLevel(logging.WARNING)
    uvicorn_access_logger.setLevel(logging.WARNING)
    asyncio_logger.setLevel(logging.WARNING)

def configure_uvicorn_logging() -> Dict[str, Any]:
    """配置Uvicorn日志设置"""
    
    # 设置过滤器
    setup_logging_filters()
    
    return {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            },
            "access": {
                "format": "%(asctime)s - %(levelname)s - %(message)s",
            },
        },
        "filters": {
            "connection_filter": {
                "()": ConnectionErrorFilter,
            },
            "access_filter": {
                "()": UvicornAccessFilter,
            },
        },
        "handlers": {
            "default": {
                "formatter": "default",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stdout",
                "filters": ["connection_filter"],
            },
            "access": {
                "formatter": "access",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stdout",
                "filters": ["access_filter"],
            },
        },
        "loggers": {
            "uvicorn": {
                "handlers": ["default"],
                "level": "WARNING",
                "propagate": False,
            },
            "uvicorn.error": {
                "handlers": ["default"],
                "level": "WARNING",
                "propagate": False,
            },
            "uvicorn.access": {
                "handlers": ["access"],
                "level": "WARNING",
                "propagate": False,
            },
            "asyncio": {
                "handlers": ["default"],
                "level": "WARNING",
                "propagate": False,
            },
        },
    }

# 在模块导入时自动设置过滤器
setup_logging_filters()
