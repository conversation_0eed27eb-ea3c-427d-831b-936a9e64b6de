#!/usr/bin/env python3
"""
IP黑名单服务
支持从多个GitHub源下载和管理IP黑名单，用于保护API免受恶意攻击
"""

import os
import json
import asyncio
import aiohttp
import ipaddress
from pathlib import Path
from datetime import datetime, timedelta
from typing import Set, Dict, List, Optional, Tuple
import logging
import re

class IPBlacklistService:
    def __init__(self):
        self.enabled = os.getenv("IP_BLACKLIST_ENABLED", "true").lower() == "true"
        self.update_interval = int(os.getenv("IP_BLACKLIST_UPDATE_INTERVAL", "6"))  # 小时
        self.cache_dir = Path(os.getenv("IP_BLACKLIST_CACHE_DIR", "blacklist_cache"))
        self.log_level = os.getenv("IP_BLACKLIST_LOG_LEVEL", "info").lower()
        
        # 创建缓存目录
        self.cache_dir.mkdir(exist_ok=True)
        
        # 配置日志
        self.logger = logging.getLogger("ip_blacklist")
        self.logger.setLevel(getattr(logging, self.log_level.upper()))
        
        # 黑名单存储
        self.blacklisted_ips: Set[str] = set()
        self.blacklisted_networks: List[ipaddress.IPv4Network] = []

        # 自定义黑名单存储（手动管理）
        self.custom_blacklisted_ips: Set[str] = set()
        self.custom_blacklist_file = self.cache_dir / "custom_blacklist.txt"

        # 源配置
        self.sources = self._load_sources_config()

        # 最后更新时间
        self.last_update = None

        # 加载自定义黑名单
        self._load_custom_blacklist()
        
        # 统计信息
        self.stats = {
            "total_ips": 0,
            "total_networks": 0,
            "sources_loaded": 0,
            "last_update": None,
            "update_errors": []
        }
        
        self.logger.info(f"IP黑名单服务初始化完成 - 启用状态: {self.enabled}")
    
    def _load_sources_config(self) -> Dict:
        """加载黑名单源配置"""
        try:
            sources_json = os.getenv("IP_BLACKLIST_SOURCES", "{}")
            sources = json.loads(sources_json)
            
            # 按优先级排序
            sorted_sources = dict(sorted(sources.items(), key=lambda x: x[1].get('priority', 999)))
            
            enabled_count = sum(1 for source in sorted_sources.values() if source.get('enabled', False))
            self.logger.info(f"加载了 {len(sorted_sources)} 个黑名单源，其中 {enabled_count} 个已启用")
            
            return sorted_sources
        except json.JSONDecodeError as e:
            self.logger.error(f"解析黑名单源配置失败: {e}")
            return {}
        except Exception as e:
            self.logger.error(f"加载黑名单源配置失败: {e}")
            return {}

    def _load_custom_blacklist(self):
        """加载自定义黑名单文件"""
        try:
            if self.custom_blacklist_file.exists():
                with open(self.custom_blacklist_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                loaded_count = 0
                for line in lines:
                    line = line.strip()
                    # 跳过空行和注释
                    if not line or line.startswith('#'):
                        continue

                    # 支持行内注释
                    if '#' in line:
                        ip = line.split('#')[0].strip()
                    else:
                        ip = line

                    if self._is_valid_ip(ip):
                        self.custom_blacklisted_ips.add(ip)
                        loaded_count += 1

                self.logger.info(f"加载自定义黑名单: {loaded_count} 个IP")
            else:
                self.logger.info("自定义黑名单文件不存在，已创建空文件")
                # 创建空的自定义黑名单文件
                self.custom_blacklist_file.parent.mkdir(parents=True, exist_ok=True)
                with open(self.custom_blacklist_file, 'w', encoding='utf-8') as f:
                    f.write("# 自定义IP黑名单\n# 每行一个IP地址，支持注释\n")
        except Exception as e:
            self.logger.error(f"加载自定义黑名单失败: {e}")

    async def is_ip_blacklisted(self, ip: str) -> Tuple[bool, Optional[str]]:
        """检查IP是否在黑名单中"""
        if not self.enabled:
            return False, None
        
        # 检查是否需要更新黑名单
        await self._check_and_update()
        
        try:
            # 检查自定义黑名单（优先级最高）
            if ip in self.custom_blacklisted_ips:
                return True, "custom_blacklist"

            # 直接IP匹配（外部黑名单源）
            if ip in self.blacklisted_ips:
                return True, "external_blacklist"

            # 网络段匹配
            ip_obj = ipaddress.IPv4Address(ip)
            for network in self.blacklisted_networks:
                if ip_obj in network:
                    return True, f"network_match:{network}"

            return False, None
            
        except ipaddress.AddressValueError:
            self.logger.warning(f"无效的IP地址: {ip}")
            return False, None
        except Exception as e:
            self.logger.error(f"检查IP黑名单时出错: {e}")
            return False, None
    
    async def _check_and_update(self):
        """检查是否需要更新黑名单"""
        if not self.enabled:
            return
        
        now = datetime.now()
        
        # 如果从未更新过，或者超过更新间隔，则更新
        if (self.last_update is None or 
            now - self.last_update > timedelta(hours=self.update_interval)):
            await self.update_blacklists()
    
    async def update_blacklists(self, force: bool = False) -> Dict:
        """更新所有启用的黑名单源"""
        if not self.enabled and not force:
            return {"success": False, "message": "IP黑名单功能未启用"}
        
        self.logger.info("开始更新IP黑名单...")
        start_time = datetime.now()
        
        # 重置统计
        self.stats["update_errors"] = []
        sources_loaded = 0
        total_ips = 0
        total_networks = 0
        
        # 临时存储
        temp_ips = set()
        temp_networks = []
        
        # 并发下载所有启用的源
        tasks = []
        for source_name, source_config in self.sources.items():
            if source_config.get('enabled', False):
                tasks.append(self._download_source(source_name, source_config))
        
        if not tasks:
            self.logger.warning("没有启用的黑名单源")
            return {"success": False, "message": "没有启用的黑名单源"}
        
        # 执行下载任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for i, result in enumerate(results):
            source_name = list(self.sources.keys())[i]
            
            if isinstance(result, Exception):
                error_msg = f"源 {source_name} 下载失败: {str(result)}"
                self.logger.error(error_msg)
                self.stats["update_errors"].append(error_msg)
                continue
            
            if result:
                ips, networks = result
                temp_ips.update(ips)
                temp_networks.extend(networks)
                sources_loaded += 1
                self.logger.info(f"源 {source_name} 加载成功: {len(ips)} 个IP, {len(networks)} 个网络段")
        
        # 更新主存储
        self.blacklisted_ips = temp_ips
        self.blacklisted_networks = temp_networks
        self.last_update = datetime.now()
        
        # 更新统计
        self.stats.update({
            "total_ips": len(self.blacklisted_ips),
            "total_networks": len(self.blacklisted_networks),
            "sources_loaded": sources_loaded,
            "last_update": self.last_update.isoformat()
        })
        
        duration = (datetime.now() - start_time).total_seconds()
        self.logger.info(f"黑名单更新完成: {len(self.blacklisted_ips)} 个IP, "
                        f"{len(self.blacklisted_networks)} 个网络段, "
                        f"耗时 {duration:.2f} 秒")
        
        return {
            "success": True,
            "message": "黑名单更新成功",
            "stats": self.stats,
            "duration": duration
        }
    
    async def _download_source(self, source_name: str, source_config: Dict) -> Optional[Tuple[Set[str], List]]:
        """下载单个黑名单源"""
        url = source_config.get('url')
        format_type = source_config.get('format', 'plain_ip')
        
        if not url:
            self.logger.error(f"源 {source_name} 缺少URL配置")
            return None
        
        try:
            # 设置代理（如果启用）
            connector = None
            if os.getenv("PROXY_ENABLED", "false").lower() == "true":
                proxy_url = f"{os.getenv('PROXY_TYPE', 'socks5')}://{os.getenv('PROXY_HOST')}:{os.getenv('PROXY_PORT')}"
                connector = aiohttp.TCPConnector()
            
            timeout = aiohttp.ClientTimeout(total=30)
            
            async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        raise Exception(f"HTTP {response.status}")
                    
                    content = await response.text()
                    
                    # 缓存到本地
                    cache_file = self.cache_dir / f"{source_name}.txt"
                    with open(cache_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    # 解析内容
                    return self._parse_blacklist_content(content, format_type, source_name)
        
        except Exception as e:
            self.logger.error(f"下载源 {source_name} 失败: {e}")
            
            # 尝试使用缓存文件
            cache_file = self.cache_dir / f"{source_name}.txt"
            if cache_file.exists():
                self.logger.info(f"使用缓存文件: {cache_file}")
                try:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    return self._parse_blacklist_content(content, format_type, source_name)
                except Exception as cache_error:
                    self.logger.error(f"读取缓存文件失败: {cache_error}")
            
            return None
    
    def _parse_blacklist_content(self, content: str, format_type: str, source_name: str) -> Tuple[Set[str], List]:
        """解析黑名单内容"""
        ips = set()
        networks = []
        
        lines = content.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            
            # 跳过空行和注释
            if not line or line.startswith('#') or line.startswith(';'):
                continue
            
            try:
                if format_type == 'plain_ip':
                    # 纯IP格式
                    if self._is_valid_ip(line):
                        ips.add(line)
                
                elif format_type == 'cidr':
                    # CIDR格式
                    if '/' in line:
                        network = ipaddress.IPv4Network(line, strict=False)
                        networks.append(network)
                    elif self._is_valid_ip(line):
                        ips.add(line)
                
                elif format_type == 'spamhaus':
                    # Spamhaus格式 (IP/CIDR ; comment)
                    if ';' in line:
                        ip_part = line.split(';')[0].strip()
                    else:
                        ip_part = line
                    
                    if '/' in ip_part:
                        network = ipaddress.IPv4Network(ip_part, strict=False)
                        networks.append(network)
                    elif self._is_valid_ip(ip_part):
                        ips.add(ip_part)
            
            except Exception as e:
                # 忽略解析错误的行
                continue
        
        self.logger.debug(f"源 {source_name} 解析完成: {len(ips)} 个IP, {len(networks)} 个网络段")
        return ips, networks
    
    def _is_valid_ip(self, ip: str) -> bool:
        """验证IP地址格式"""
        try:
            ipaddress.IPv4Address(ip)
            return True
        except ipaddress.AddressValueError:
            return False
    
    def get_stats(self) -> Dict:
        """获取黑名单统计信息"""
        return {
            "enabled": self.enabled,
            "stats": self.stats,
            "sources": {
                name: {
                    "enabled": config.get('enabled', False),
                    "priority": config.get('priority', 999),
                    "description": config.get('description', ''),
                    "url": config.get('url', '')
                }
                for name, config in self.sources.items()
            },
            "update_interval_hours": self.update_interval,
            "cache_dir": str(self.cache_dir),
            "next_update": (
                (self.last_update + timedelta(hours=self.update_interval)).isoformat()
                if self.last_update else "立即更新"
            )
        }
    
    async def force_update(self) -> Dict:
        """强制更新黑名单"""
        return await self.update_blacklists(force=True)

    def add_custom_ip(self, ip: str, reason: str = "manual") -> bool:
        """手动添加IP到自定义黑名单"""
        try:
            if not self._is_valid_ip(ip):
                return False

            # 添加到内存中的自定义黑名单
            self.custom_blacklisted_ips.add(ip)

            # 保存到文件
            self._save_custom_blacklist()

            self.logger.info(f"手动添加IP到自定义黑名单: {ip} (原因: {reason})")
            return True
        except Exception as e:
            self.logger.error(f"添加IP到自定义黑名单失败: {e}")
            return False

    def remove_custom_ip(self, ip: str) -> bool:
        """从自定义黑名单中移除IP"""
        try:
            if ip in self.custom_blacklisted_ips:
                self.custom_blacklisted_ips.remove(ip)

                # 保存到文件
                self._save_custom_blacklist()

                self.logger.info(f"从自定义黑名单中移除IP: {ip}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"从自定义黑名单移除IP失败: {e}")
            return False

    def _save_custom_blacklist(self):
        """保存自定义黑名单到文件"""
        try:
            with open(self.custom_blacklist_file, 'w', encoding='utf-8') as f:
                f.write("# 自定义IP黑名单\n")
                f.write("# 每行一个IP地址，支持注释\n")
                f.write("# 此文件由系统自动维护\n\n")

                for ip in sorted(self.custom_blacklisted_ips):
                    f.write(f"{ip}\n")

            self.logger.debug(f"自定义黑名单已保存: {len(self.custom_blacklisted_ips)} 个IP")
        except Exception as e:
            self.logger.error(f"保存自定义黑名单失败: {e}")

    def get_custom_blacklist(self) -> List[str]:
        """获取自定义黑名单列表"""
        return sorted(list(self.custom_blacklisted_ips))

    def get_blacklist_summary(self) -> Dict:
        """获取黑名单摘要信息"""
        return {
            "enabled": self.enabled,
            "total_ips": len(self.blacklisted_ips),
            "total_networks": len(self.blacklisted_networks),
            "custom_ips": len(self.custom_blacklisted_ips),
            "total_all_ips": len(self.blacklisted_ips) + len(self.custom_blacklisted_ips),
            "last_update": self.last_update.isoformat() if self.last_update else None,
            "sources_count": len([s for s in self.sources.values() if s.get('enabled', False)]),
            "cache_dir_exists": self.cache_dir.exists(),
            "custom_blacklist_file": str(self.custom_blacklist_file),
            "update_interval_hours": self.update_interval
        }

    async def test_ip(self, ip: str) -> Dict:
        """测试IP是否在黑名单中（用于调试）"""
        is_blacklisted, match_type = await self.is_ip_blacklisted(ip)
        return {
            "ip": ip,
            "is_blacklisted": is_blacklisted,
            "match_type": match_type,
            "checked_at": datetime.now().isoformat()
        }

# 全局实例
ip_blacklist_service = IPBlacklistService()
