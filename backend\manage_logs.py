#!/usr/bin/env python3
"""
日志管理工具
用于清理、查看和管理日志文件
"""
import os
import sys
import time
import argparse
from pathlib import Path
from datetime import datetime, <PERSON><PERSON><PERSON>

def get_log_files(logs_dir):
    """获取所有日志文件"""
    if not os.path.exists(logs_dir):
        return []
    
    log_files = []
    for filename in os.listdir(logs_dir):
        if filename.startswith('backend.log'):
            file_path = os.path.join(logs_dir, filename)
            stat = os.stat(file_path)
            log_files.append({
                'name': filename,
                'path': file_path,
                'size': stat.st_size,
                'modified': stat.st_mtime,
                'modified_str': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
            })
    
    return sorted(log_files, key=lambda x: x['modified'], reverse=True)

def format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    else:
        return f"{size_bytes / (1024 * 1024):.1f} MB"

def list_logs(logs_dir):
    """列出所有日志文件"""
    log_files = get_log_files(logs_dir)
    
    if not log_files:
        print("📁 没有找到日志文件")
        return
    
    print(f"📁 日志目录: {logs_dir}")
    print(f"📊 总计: {len(log_files)} 个文件")
    print("-" * 80)
    print(f"{'文件名':<30} {'大小':<10} {'修改时间':<20}")
    print("-" * 80)
    
    total_size = 0
    for log_file in log_files:
        print(f"{log_file['name']:<30} {format_size(log_file['size']):<10} {log_file['modified_str']:<20}")
        total_size += log_file['size']
    
    print("-" * 80)
    print(f"总大小: {format_size(total_size)}")

def clean_logs(logs_dir, days):
    """清理指定天数之前的日志"""
    log_files = get_log_files(logs_dir)
    
    if not log_files:
        print("📁 没有找到日志文件")
        return
    
    cutoff_time = time.time() - (days * 24 * 60 * 60)
    cutoff_date = datetime.fromtimestamp(cutoff_time).strftime('%Y-%m-%d')
    
    to_delete = [f for f in log_files if f['modified'] < cutoff_time and f['name'] != 'backend.log']
    
    if not to_delete:
        print(f"🧹 没有找到 {days} 天前（{cutoff_date}）的日志文件需要清理")
        return
    
    print(f"🧹 将清理 {len(to_delete)} 个日志文件（{cutoff_date} 之前）:")
    total_size = 0
    for log_file in to_delete:
        print(f"  - {log_file['name']} ({format_size(log_file['size'])})")
        total_size += log_file['size']
    
    print(f"总计释放空间: {format_size(total_size)}")
    
    confirm = input("\n确认删除这些文件吗？(y/N): ").strip().lower()
    if confirm in ['y', 'yes']:
        deleted_count = 0
        for log_file in to_delete:
            try:
                os.remove(log_file['path'])
                deleted_count += 1
                print(f"✅ 已删除: {log_file['name']}")
            except Exception as e:
                print(f"❌ 删除失败: {log_file['name']} - {e}")
        
        print(f"\n🎉 清理完成！删除了 {deleted_count} 个文件，释放 {format_size(total_size)} 空间")
    else:
        print("❌ 取消清理操作")

def view_log(logs_dir, filename, lines=50):
    """查看日志文件内容"""
    if filename == 'current':
        filename = 'backend.log'
    
    log_path = os.path.join(logs_dir, filename)
    
    if not os.path.exists(log_path):
        print(f"❌ 日志文件不存在: {filename}")
        return
    
    print(f"📖 查看日志: {filename}")
    print(f"📍 路径: {log_path}")
    print("-" * 80)
    
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            
        if lines > 0:
            # 显示最后N行
            display_lines = all_lines[-lines:]
            if len(all_lines) > lines:
                print(f"... (显示最后 {lines} 行，共 {len(all_lines)} 行)")
        else:
            # 显示全部
            display_lines = all_lines
        
        for line in display_lines:
            print(line.rstrip())
            
    except Exception as e:
        print(f"❌ 读取日志文件失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='日志管理工具')
    parser.add_argument('--logs-dir', default='logs', help='日志目录路径 (默认: logs)')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # list 命令
    subparsers.add_parser('list', help='列出所有日志文件')
    
    # clean 命令
    clean_parser = subparsers.add_parser('clean', help='清理旧日志文件')
    clean_parser.add_argument('--days', type=int, default=30, help='保留天数 (默认: 30)')
    
    # view 命令
    view_parser = subparsers.add_parser('view', help='查看日志文件')
    view_parser.add_argument('filename', nargs='?', default='current', help='日志文件名 (默认: current)')
    view_parser.add_argument('--lines', type=int, default=50, help='显示行数 (默认: 50, 0=全部)')
    
    args = parser.parse_args()
    
    # 确保日志目录存在
    logs_dir = os.path.abspath(args.logs_dir)
    
    if not args.command:
        parser.print_help()
        return
    
    if args.command == 'list':
        list_logs(logs_dir)
    elif args.command == 'clean':
        clean_logs(logs_dir, args.days)
    elif args.command == 'view':
        view_log(logs_dir, args.filename, args.lines)

if __name__ == '__main__':
    main()
