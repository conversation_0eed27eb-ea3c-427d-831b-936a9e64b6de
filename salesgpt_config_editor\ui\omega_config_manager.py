"""
OmegaConf配置管理器
使用OmegaConf库提供更强大的配置管理功能
"""

import os
from pathlib import Path
from typing import Dict, Any, List
from omegaconf import OmegaConf, DictConfig
import json


class OmegaConfigManager:
    """基于OmegaConf的配置管理器"""
    
    def __init__(self):
        self.config: DictConfig = OmegaConf.create({})
        self.config_path: str = None
        self.is_modified: bool = False
    
    def load_config_file(self, file_path: str) -> bool:
        """加载配置文件"""
        try:
            # OmegaConf可以直接加载JSON文件
            self.config = OmegaConf.load(file_path)
            self.config_path = file_path
            self.is_modified = False
            print(f"✅ OmegaConf成功加载配置文件: {file_path}")
            return True
        except Exception as e:
            print(f"❌ OmegaConf加载配置文件失败: {e}")
            return False
    
    def save_config_file(self, file_path: str = None) -> bool:
        """保存配置文件"""
        try:
            save_path = file_path or self.config_path
            if not save_path:
                print("❌ 没有指定保存路径")
                return False
            
            # 确保目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # 将OmegaConf转换为字典，然后保存为JSON格式
            print("🔍 检查 OmegaConf 中的销售流程数据...")
            if hasattr(self.config, 'sales_process') and hasattr(self.config.sales_process, 'stages'):
                omega_stages = self.config.sales_process.stages
                print(f"  📊 OmegaConf 中有 {len(omega_stages)} 个阶段")
                if 'introduction' in omega_stages:
                    intro_data = omega_stages['introduction']
                    print(f"  🔍 introduction 阶段数据:")
                    print(f"    名称: {intro_data.get('name', 'No Name')}")
                    print(f"    目标: {intro_data.get('objective', 'No Objective')[:50]}...")
                else:
                    print("  ❌ 没有找到 introduction 阶段")
            else:
                print("  ❌ OmegaConf 中没有销售流程数据")

            config_dict = OmegaConf.to_container(self.config, resolve=True)
            print(f"🔍 准备保存配置，包含键: {list(config_dict.keys()) if config_dict else '空配置'}")

            # 检查转换后的字典中的销售流程数据
            if config_dict and 'sales_process' in config_dict and 'stages' in config_dict['sales_process']:
                dict_stages = config_dict['sales_process']['stages']
                print(f"  📊 转换后字典中有 {len(dict_stages)} 个阶段")
                if 'introduction' in dict_stages:
                    intro_dict = dict_stages['introduction']
                    print(f"  🔍 转换后 introduction 阶段数据:")
                    print(f"    名称: {intro_dict.get('name', 'No Name')}")
                    print(f"    目标: {intro_dict.get('objective', 'No Objective')[:50]}...")
                else:
                    print("  ❌ 转换后字典中没有 introduction 阶段")
            else:
                print("  ❌ 转换后字典中没有销售流程数据")

            if not config_dict:
                print("❌ 警告：配置数据为空！")
                return False

            # 保存前先备份
            backup_path = f"{save_path}.backup"
            if os.path.exists(save_path):
                import shutil
                shutil.copy2(save_path, backup_path)
                print(f"📋 已创建备份: {backup_path}")

            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, ensure_ascii=False, indent=2)

            # 验证保存的文件
            try:
                with open(save_path, 'r', encoding='utf-8') as f:
                    test_load = json.load(f)
                print(f"✅ 文件保存验证成功，包含键: {list(test_load.keys())}")
            except Exception as verify_error:
                print(f"❌ 文件保存验证失败: {verify_error}")
                return False

            print(f"📁 配置文件已写入: {save_path}")
            
            self.config_path = save_path
            self.is_modified = False
            print(f"✅ OmegaConf成功保存配置文件: {save_path}")
            return True
        except Exception as e:
            print(f"❌ OmegaConf保存配置文件失败: {e}")
            return False
    
    def get_config_data(self) -> Dict[str, Any]:
        """获取配置数据（转换为普通字典）"""
        return OmegaConf.to_container(self.config, resolve=True)
    
    def set_config_data(self, data: Dict[str, Any]):
        """设置配置数据"""
        self.config = OmegaConf.create(data)
        self.is_modified = True
        print("✅ OmegaConf配置数据已更新")
    
    def update_config(self, path: str, value: Any):
        """使用点语法更新配置"""
        try:
            OmegaConf.set(self.config, path, value)
            self.is_modified = True
            print(f"✅ 更新配置: {path} = {value}")
        except Exception as e:
            print(f"❌ 更新配置失败: {path} = {value}, 错误: {e}")
    
    def get_config_value(self, path: str, default=None):
        """使用点语法获取配置值"""
        try:
            return OmegaConf.select(self.config, path, default=default)
        except Exception as e:
            print(f"❌ 获取配置失败: {path}, 错误: {e}")
            return default
    
    # 兼容性方法 - 保持与原有代码的接口一致
    def get_agent_config(self) -> Dict[str, Any]:
        """获取Agent配置"""
        agent_config = self.config.get('agent_profile', {})
        return OmegaConf.to_container(agent_config) if agent_config else {}
    
    def set_agent_config(self, config: Dict[str, Any]):
        """设置Agent配置"""
        self.config.agent_profile = OmegaConf.create(config)
        self.is_modified = True
    
    def get_products_config(self) -> Dict[str, Any]:
        """获取产品配置"""
        products_config = self.config.get('products', {})
        return OmegaConf.to_container(products_config) if products_config else {}
    
    def set_products_config(self, config: Dict[str, Any]):
        """设置产品配置"""
        self.config.products = OmegaConf.create(config)
        self.is_modified = True
    
    def get_sales_process_config(self) -> Dict[str, Any]:
        """获取销售流程配置"""
        sales_config = self.config.get('sales_process', {})
        return OmegaConf.to_container(sales_config) if sales_config else {}
    
    def set_sales_process_config(self, config: Dict[str, Any]):
        """设置销售流程配置"""
        self.config.sales_process = OmegaConf.create(config)
        self.is_modified = True
    
    def get_after_sales_config(self) -> Dict[str, Any]:
        """获取售后支持配置"""
        # 保持与原UIConfigManager一致，直接获取after_sales_support
        after_sales_config = self.config.get('after_sales_support', {})
        if after_sales_config:
            result = OmegaConf.to_container(after_sales_config)
            print(f"🔍 找到售后支持配置，包含键: {list(result.keys()) if result else '无数据'}")
            return result

        print("⚠️ 未找到售后支持配置数据")
        return {}
    
    def set_after_sales_config(self, config: Dict[str, Any]):
        """设置售后支持配置"""
        # 保持与get_after_sales_config一致，使用after_sales_support键
        self.config.after_sales_support = OmegaConf.create(config)
        self.is_modified = True
    
    def get_escalation_rules_config(self) -> Dict[str, Any]:
        """获取升级规则配置"""
        escalation_config = self.config.get('escalation_rules', {})
        return OmegaConf.to_container(escalation_config) if escalation_config else {}
    
    def get_conversation_rules_config(self) -> Dict[str, Any]:
        """获取对话规则配置"""
        conversation_config = self.config.get('conversation_rules', {})
        return OmegaConf.to_container(conversation_config) if conversation_config else {}
    
    def get_personalization_config(self) -> Dict[str, Any]:
        """获取个性化配置"""
        personalization_config = self.config.get('personalization', {})
        return OmegaConf.to_container(personalization_config) if personalization_config else {}
    
    def validate_config(self) -> List[str]:
        """基本配置检查"""
        errors = []
        try:
            # 检查必需的配置节
            required_sections = ['agent_profile']
            for section in required_sections:
                if not self.config.get(section):
                    errors.append(f"缺少必需的配置节: {section}")
            
            # 检查Agent配置
            if self.config.get('agent_profile'):
                agent = self.config.agent_profile
                required_fields = ['name', 'role', 'company']
                for field in required_fields:
                    if not agent.get(field):
                        errors.append(f"Agent配置缺少必需字段: {field}")
        except Exception as e:
            errors.append(f"配置验证时发生错误: {e}")
        
        return errors
    
    def merge_config(self, other_config: Dict[str, Any]):
        """合并配置"""
        try:
            other_omega = OmegaConf.create(other_config)
            self.config = OmegaConf.merge(self.config, other_omega)
            self.is_modified = True
            print("✅ 配置合并成功")
        except Exception as e:
            print(f"❌ 配置合并失败: {e}")
    
    def backup_config(self, backup_path: str = None) -> bool:
        """备份当前配置"""
        try:
            if not backup_path:
                backup_path = f"{self.config_path}.backup"
            
            return self.save_config_file(backup_path)
        except Exception as e:
            print(f"❌ 备份配置失败: {e}")
            return False
    
    def print_config_structure(self):
        """打印配置结构（调试用）"""
        print("📋 当前配置结构:")
        print(OmegaConf.to_yaml(self.config))
